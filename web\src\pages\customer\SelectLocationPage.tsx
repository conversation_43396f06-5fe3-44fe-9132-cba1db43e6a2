import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, MapPin, Search, Navigation, Check, Crosshair, Target, X } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Component to handle map clicks
function LocationMarker({ position, setPosition }: { 
  position: [number, number] | null; 
  setPosition: (pos: [number, number]) => void;
}) {
  useMapEvents({
    click(e) {
      setPosition([e.latlng.lat, e.latlng.lng]);
    },
  });

  return position === null ? null : (
    <Marker position={position}>
      <Popup>Selected location</Popup>
    </Marker>
  );
}

const SelectLocationPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const type = searchParams.get('type');
  
  const [position, setPosition] = useState<[number, number] | null>(null);
  const [address, setAddress] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [headerSearchQuery, setHeaderSearchQuery] = useState('');

  // Default to Nablus, Palestine
  const defaultCenter: [number, number] = [32.2211, 35.2544];

  // Scroll detection for animated header
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 100);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Mock addresses for search
  const mockAddresses = [
    { name: 'Nablus City Center', address: 'Nablus, Palestine', lat: 32.2211, lng: 35.2544 },
    { name: 'An-Najah University', address: 'An-Najah University, Nablus', lat: 32.2319, lng: 35.2542 },
    { name: 'Rafidia Hospital', address: 'Rafidia Hospital, Nablus', lat: 32.2156, lng: 35.2631 },
    { name: 'Old City Nablus', address: 'Old City, Nablus, Palestine', lat: 32.2208, lng: 35.2625 },
    { name: 'Ramallah City Center', address: 'Ramallah, Palestine', lat: 31.9038, lng: 35.2034 },
    { name: 'Jerusalem', address: 'Jerusalem, Palestine', lat: 31.7683, lng: 35.2137 },
    { name: 'Bethlehem', address: 'Bethlehem, Palestine', lat: 31.7054, lng: 35.2024 }
  ];

  // Use either main search or header search query
  const activeSearchQuery = searchQuery || headerSearchQuery;
  const filteredAddresses = mockAddresses.filter(addr =>
    addr.name.toLowerCase().includes(activeSearchQuery.toLowerCase()) ||
    addr.address.toLowerCase().includes(activeSearchQuery.toLowerCase())
  );

  const handleAddressSelect = (addr: typeof mockAddresses[0]) => {
    setPosition([addr.lat, addr.lng]);
    setAddress(addr.address);
    setSearchQuery('');
  };

  const getCurrentLocation = () => {
    setIsLoading(true);
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setPosition([latitude, longitude]);
          setAddress(`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);
          setIsLoading(false);
        },
        (error) => {
          console.error('Error getting location:', error);
          setIsLoading(false);
          alert('Unable to get your current location. Please select manually.');
        }
      );
    } else {
      setIsLoading(false);
      alert('Geolocation is not supported by this browser.');
    }
  };

  const handleConfirm = () => {
    if (!position || !address) {
      alert('Please select a location');
      return;
    }

    const locationData = {
      address,
      lat: position[0],
      lng: position[1],
      type
    };

    console.log('Confirming location:', locationData);

    // Store in localStorage as backup with type-specific key
    const storageKey = type === 'pickup' ? 'selectedPickupLocation' :
                      type === 'dropoff' ? 'selectedDropoffLocation' :
                      type === 'req-pickup' ? 'selectedRequestPickupLocation' :
                      type === 'req-delivery' ? 'selectedRequestDeliveryLocation' :
                      type === 'req-dropoff' ? 'selectedRequestDropoffLocation' :
                      'selectedDeliveryLocation';
    localStorage.setItem(storageKey, JSON.stringify(locationData));

    // Emit custom event for location selection (for checkout page)
    const event = new CustomEvent('locationSelected', { detail: locationData });
    window.dispatchEvent(event);

    // Navigate back with location data in state
    if(type === 'pickup' || type === 'dropoff'){
      navigate('/customer/send-package', { state: { locationData } });
    }
    else if(type === 'req-pickup' || type === 'req-delivery' || type === 'req-dropoff'){
      navigate('/customer/request-pickup', { state: { locationData } });
    }
    else{
      navigate('/customer/order-checkout', { state: { locationData } });
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
                      {/* Animated gradient orbs */}
                      <motion.div
                        animate={{
                          scale: [1, 1.2, 1],
                          opacity: [0.3, 0.6, 0.3],
                        }}
                        transition={{
                          duration: 8,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                        className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-orange-500/30 to-red-600/30 rounded-full blur-3xl"
                      />
                      <motion.div
                        animate={{
                          scale: [1.2, 1, 1.2],
                          opacity: [0.4, 0.7, 0.4],
                        }}
                        transition={{
                          duration: 10,
                          repeat: Infinity,
                          ease: "easeInOut",
                          delay: 2
                        }}
                        className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-pink-500/30 to-purple-600/30 rounded-full blur-3xl"
                      />
                      <motion.div
                        animate={{
                          scale: [1, 1.3, 1],
                          opacity: [0.2, 0.5, 0.2],
                        }}
                        transition={{
                          duration: 12,
                          repeat: Infinity,
                          ease: "easeInOut",
                          delay: 4
                        }}
                        className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-blue-500/30 to-cyan-600/30 rounded-full blur-3xl"
                      />
            
                      {/* Floating particles */}
                      {[...Array(15)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-2 h-2 bg-white/20 rounded-full"
                          style={{
                            left: `${Math.random() * 100}%`,
                            top: `${Math.random() * 100}%`,
                          }}
                          animate={{
                            y: [-20, -100, -20],
                            opacity: [0, 1, 0],
                          }}
                          transition={{
                            duration: 3 + Math.random() * 2,
                            repeat: Infinity,
                            delay: Math.random() * 2,
                          }}
                        />
                      ))}
      </div>

      {/* Animated Scroll Header */}
      <AnimatePresence>
        {isScrolled && (
          <motion.div
            initial={{ y: -100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -100, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-slate-950/95 via-purple-950/95 to-slate-950/95 backdrop-blur-2xl border-b border-purple-400/20 shadow-2xl"
          >
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
              <div className="flex items-center justify-between gap-4">
                {/* Left side - Back button and title */}
                <div className="flex items-center gap-3">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => navigate(-1)}
                    className="p-2 hover:bg-purple-500/20 rounded-xl transition-all duration-300 backdrop-blur-sm border border-purple-400/15"
                  >
                    <ArrowLeft className="w-5 h-5 text-white" />
                  </motion.button>
                  <div className="flex items-center gap-2">
                    <Target className="w-6 h-6 text-orange-300" />
                    <span className="text-white font-bold text-lg">Select Location</span>
                  </div>
                </div>

                {/* Center - Search bar */}
                <div className="flex-1 max-w-md mx-4">
                  <div className="relative">
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 p-1 bg-cyan-500/20 rounded-lg">
                      <Search className="w-4 h-4 text-cyan-300" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search locations..."
                      value={headerSearchQuery}
                      onChange={(e) => {
                        setHeaderSearchQuery(e.target.value);
                        setSearchQuery(e.target.value); // Sync with main search
                      }}
                      className="w-full pl-12 pr-10 py-3 bg-slate-900/60 border border-cyan-400/25 rounded-xl text-white placeholder-cyan-200/50 focus:ring-2 focus:ring-cyan-400 focus:border-cyan-400/60 backdrop-blur-sm transition-all duration-300"
                    />
                    {headerSearchQuery && (
                      <motion.button
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => {
                          setHeaderSearchQuery('');
                          setSearchQuery('');
                        }}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 hover:bg-red-500/20 rounded-lg transition-all duration-200"
                      >
                        <X className="w-4 h-4 text-red-300" />
                      </motion.button>
                    )}
                  </div>
                </div>

                {/* Right side - Current location button */}
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={getCurrentLocation}
                  disabled={isLoading}
                  className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-xl hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 disabled:opacity-50 shadow-lg font-medium"
                >
                  <motion.div
                    animate={isLoading ? { rotate: 360 } : {}}
                    transition={{ duration: 1, repeat: isLoading ? Infinity : 0, ease: "linear" }}
                  >
                    <Navigation className="w-4 h-4" />
                  </motion.div>
                  <span className="hidden sm:inline">
                    {isLoading ? 'Getting...' : 'Current'}
                  </span>
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Content Overlay */}
      <div className="relative z-10">
        {/* Enhanced Header */}
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="bg-white/10 backdrop-blur-xl border-b border-white/20 shadow-2xl"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => navigate(-1)}
                  className="p-3 hover:bg-white/20 rounded-xl transition-all duration-300 backdrop-blur-sm border border-white/10"
                >
                  <ArrowLeft className="w-5 h-5 text-white" />
                </motion.button>
                <div>
                  <h1 className="text-3xl font-bold text-white flex items-center gap-3">
                    <Target className="w-8 h-8 text-blue-300" />
                    Select {type === 'pickup' ? 'Pickup' : type === 'delivery' ? 'Delivery' : 'Location'}
                  </h1>
                  <p className="text-blue-200 mt-1">Choose your location on the map</p>
                </div>
              </div>

              {/* Location Badge */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.3, type: "spring" }}
                className="flex items-center gap-2 bg-blue-500/20 text-blue-300 px-4 py-2 rounded-full border border-blue-400/30"
              >
                <Crosshair className="w-4 h-4" />
                <span className="text-sm font-medium">Interactive Map</span>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Main Content Container */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
          {/* Top Panel - Search and Selected Location */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Search Panel */}
            <motion.div
              initial={{ y: -50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-xl rounded-3xl border border-white/30 p-8 shadow-2xl relative overflow-hidden"
            >
              {/* Background decoration */}
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-blue-500/5 rounded-3xl"></div>
              <div className="absolute top-0 right-0 w-24 h-24 bg-purple-400/20 rounded-full blur-2xl translate-x-12 -translate-y-12"></div>

              <div className="relative z-10">
                <div className="flex items-center gap-4 mb-8">
                  <div className="p-3 bg-gradient-to-br from-purple-500/30 to-blue-500/30 rounded-xl border border-white/20 shadow-lg">
                    <Search className="w-7 h-7 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">Search Location</h2>
                    <p className="text-white/70 text-sm">Find your desired location</p>
                  </div>
                </div>

                <div className="space-y-6">
                  <div className="relative">
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2 p-1 bg-purple-500/20 rounded-lg">
                      <Search className="w-4 h-4 text-purple-300" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search for a location..."
                      value={searchQuery}
                      onChange={(e) => {
                        setSearchQuery(e.target.value);
                        setHeaderSearchQuery(e.target.value); // Sync with header search
                      }}
                      className="w-full pl-14 pr-6 py-4 bg-white/10 border border-white/20 rounded-2xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-purple-400/50 backdrop-blur-sm transition-all duration-300 text-lg shadow-inner"
                    />
                  </div>

                  <motion.button
                    whileHover={{ scale: 1.02, boxShadow: "0 20px 40px rgba(139, 92, 246, 0.3)" }}
                    whileTap={{ scale: 0.98 }}
                    onClick={getCurrentLocation}
                    disabled={isLoading}
                    className="w-full flex items-center justify-center gap-3 py-4 px-6 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 text-white rounded-2xl hover:from-blue-700 hover:via-purple-700 hover:to-blue-700 transition-all duration-300 disabled:opacity-50 shadow-xl font-semibold text-lg relative overflow-hidden"
                  >
                    {/* Button background animation */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] hover:translate-x-[100%] transition-transform duration-1000"></div>

                    <motion.div
                      animate={isLoading ? { rotate: 360 } : {}}
                      transition={{ duration: 1, repeat: isLoading ? Infinity : 0, ease: "linear" }}
                    >
                      <Navigation className="w-6 h-6" />
                    </motion.div>
                    {isLoading ? 'Getting Location...' : 'Use Current Location'}
                  </motion.button>
                </div>
              </div>
            </motion.div>

            {/* Selected Location Panel */}
            <motion.div
              initial={{ y: -50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-xl rounded-3xl border border-white/30 p-8 shadow-2xl relative overflow-hidden"
            >
              {/* Background decoration */}
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/5 rounded-3xl"></div>
              <div className="absolute top-0 left-0 w-24 h-24 bg-green-400/20 rounded-full blur-2xl -translate-x-12 -translate-y-12"></div>

              <div className="relative z-10">
                <div className="flex items-center gap-4 mb-8">
                  <div className="p-3 bg-gradient-to-br from-green-500/30 to-emerald-500/30 rounded-xl border border-white/20 shadow-lg">
                    <MapPin className="w-7 h-7 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">Selected Location</h2>
                    <p className="text-white/70 text-sm">Your chosen destination</p>
                  </div>
                </div>

                {position ? (
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.4 }}
                    className="space-y-6"
                  >
                    <div className="p-6 bg-gradient-to-br from-white/10 to-white/5 rounded-2xl border border-white/20 shadow-inner">
                      <div className="flex items-start gap-4">
                        <div className="p-2 bg-green-500/20 rounded-lg">
                          <MapPin className="w-5 h-5 text-green-300" />
                        </div>
                        <div className="flex-1">
                          <p className="font-bold text-white text-lg mb-2">Selected Address</p>
                          <p className="text-white/90 leading-relaxed">{address}</p>

                          <div className="mt-4 p-3 bg-white/5 rounded-lg border border-white/10">
                            <div className="text-xs text-white/60 font-mono">
                              <div>Latitude: {position[0].toFixed(6)}</div>
                              <div>Longitude: {position[1].toFixed(6)}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <motion.button
                      whileHover={{
                        scale: 1.02,
                        boxShadow: "0 20px 40px rgba(34, 197, 94, 0.3)"
                      }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleConfirm}
                      className="w-full flex items-center justify-center gap-3 py-4 px-6 bg-gradient-to-r from-green-600 via-emerald-600 to-green-600 text-white rounded-2xl hover:from-green-700 hover:via-emerald-700 hover:to-green-700 transition-all duration-300 shadow-xl font-bold text-lg relative overflow-hidden"
                    >
                      {/* Button background animation */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] hover:translate-x-[100%] transition-transform duration-1000"></div>

                      <Check className="w-6 h-6" />
                      Confirm Location
                    </motion.button>
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-8"
                  >
                    <div className="p-8 bg-gradient-to-br from-white/5 to-white/10 rounded-2xl border border-white/20">
                      <motion.div
                        animate={{
                          scale: [1, 1.1, 1],
                          rotate: [0, 5, -5, 0]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      >
                        <Crosshair className="w-12 h-12 text-white/40 mx-auto mb-4" />
                      </motion.div>
                      <p className="text-white/70 font-medium">
                        Click on the map to select your location
                      </p>
                      <p className="text-white/50 text-sm mt-2">
                        Or use the search above to find a specific place
                      </p>
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>
          </div>

          {/* Horizontal Search Results */}
          {activeSearchQuery && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4 }}
              className="bg-gradient-to-r from-white/10 via-white/5 to-white/10 backdrop-blur-xl rounded-3xl border border-white/30 p-8 shadow-2xl relative overflow-hidden"
            >
              {/* Background decoration */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-transparent to-blue-500/10 rounded-3xl"></div>
              <div className="absolute top-0 left-0 w-32 h-32 bg-purple-500/20 rounded-full blur-3xl -translate-x-16 -translate-y-16"></div>
              <div className="absolute bottom-0 right-0 w-24 h-24 bg-blue-500/20 rounded-full blur-2xl translate-x-12 translate-y-12"></div>

              <div className="relative z-10">
                <motion.div
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className="flex items-center gap-3 mb-6"
                >
                  <div className="p-3 bg-gradient-to-br from-purple-500/30 to-blue-500/30 rounded-xl border border-white/20 shadow-lg">
                    <Search className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-white">Search Results</h3>
                    <p className="text-white/70 text-sm">Found {filteredAddresses.length} location{filteredAddresses.length !== 1 ? 's' : ''}</p>
                  </div>
                </motion.div>

                <div
                  className="overflow-x-auto overflow-y-hidden scrollbar-thin scrollbar-thumb-purple-400/30 scrollbar-track-transparent hover:scrollbar-thumb-purple-400/50 pb-2 pt-2"
                  style={{
                    scrollbarWidth: 'thin',
                    msOverflowStyle: 'auto'
                  }}
                >
                  <div className="flex gap-6 min-w-max px-2">
                    {filteredAddresses.map((addr, index) => (
                      <motion.button
                        key={index}
                        initial={{ opacity: 0, x: 30, scale: 0.9 }}
                        animate={{ opacity: 1, x: 0, scale: 1 }}
                        transition={{
                          duration: 0.4,
                          delay: index * 0.1,
                          type: "spring",
                          stiffness: 100
                        }}
                        whileHover={{
                          scale: 1.03,
                          y: -4,
                          transition: { duration: 0.2 }
                        }}
                        whileTap={{ scale: 0.97 }}
                        onClick={() => handleAddressSelect(addr)}
                        className="group flex-shrink-0 w-80 p-6 bg-gradient-to-br from-white/10 to-white/5 hover:from-white/20 hover:to-white/10 rounded-2xl border border-white/20 hover:border-white/40 transition-all duration-300 text-left shadow-xl hover:shadow-2xl backdrop-blur-sm relative overflow-hidden"
                      >
                        {/* Card background decoration */}
                        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-transparent to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div className="absolute top-0 right-0 w-20 h-20 bg-purple-400/20 rounded-full blur-2xl translate-x-10 -translate-y-10 group-hover:scale-150 transition-transform duration-500"></div>

                        <div className="relative z-10">
                          <div className="flex items-start gap-4">
                            <div className="p-3 bg-gradient-to-br from-purple-500/30 to-blue-500/30 rounded-xl border border-white/20 shadow-lg group-hover:scale-110 transition-transform duration-300">
                              <MapPin className="w-5 h-5 text-white flex-shrink-0" />
                            </div>
                            <div className="min-w-0 flex-1">
                              <div className="font-bold text-white text-lg truncate group-hover:text-purple-100 transition-colors duration-300">
                                {addr.name}
                              </div>
                              <div className="text-white/70 text-sm mt-2 line-clamp-2 group-hover:text-white/90 transition-colors duration-300">
                                {addr.address}
                              </div>

                              {/* Action indicator */}
                              <div className="flex items-center gap-2 mt-3 text-xs text-purple-300 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                                <span>Click to select</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.button>
                    ))}

                    {filteredAddresses.length === 0 && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.4 }}
                        className="flex-shrink-0 w-80 p-8 bg-gradient-to-br from-red-500/10 to-orange-500/10 rounded-2xl border border-red-400/20 text-center relative overflow-hidden"
                      >
                        <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 to-orange-500/5"></div>
                        <div className="relative z-10">
                          <div className="p-3 bg-red-500/20 rounded-xl border border-red-400/30 w-fit mx-auto mb-4">
                            <Search className="w-6 h-6 text-red-300" />
                          </div>
                          <div className="text-white font-semibold mb-2">No locations found</div>
                          <div className="text-white/60 text-sm">Try a different search term</div>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </div>

                {/* Scroll indicator */}
                {filteredAddresses.length > 2 && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                    className="flex justify-center mt-6"
                  >
                    <div className="text-sm text-white/80 flex items-center gap-3 bg-gradient-to-r from-purple-500/20 to-blue-500/20 px-6 py-3 rounded-full border border-white/20 backdrop-blur-sm shadow-lg">
                      <motion.div
                        animate={{ x: [0, 8, 0] }}
                        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                        className="flex items-center gap-1"
                      >
                        <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                      </motion.div>
                      <span className="font-medium">Scroll to explore more locations</span>
                      <motion.div
                        animate={{ x: [0, 8, 0] }}
                        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                        className="flex items-center gap-1"
                      >
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                      </motion.div>
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>
          )}

          {/* Map Section */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 overflow-hidden shadow-2xl"
          >
            <div className="h-96 lg:h-[600px] relative">
              <MapContainer
                center={position || defaultCenter}
                zoom={13}
                style={{ height: '100%', width: '100%', borderRadius: '1rem' }}
              >
                <TileLayer
                  attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                  url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                />
                <LocationMarker position={position} setPosition={setPosition} />
              </MapContainer>

              {/* Map Overlay Instructions */}
              <div className="absolute top-4 left-4 bg-black/50 backdrop-blur-sm text-white px-3 py-2 rounded-lg text-sm">
                <div className="flex items-center gap-2">
                  <Target className="w-4 h-4" />
                  Click anywhere to select location
                </div>
              </div>
            </div>
          </motion.div>

          {/* Instructions */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-blue-500/10 backdrop-blur-xl border border-blue-400/30 rounded-2xl p-6"
          >
            <h3 className="font-bold text-blue-300 mb-4 flex items-center gap-2">
              <Target className="w-5 h-5" />
              How to select a location:
            </h3>
            <ul className="text-blue-200 space-y-2">
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                Click anywhere on the map to place a marker
              </li>
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                Use the search box to find specific locations
              </li>
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                Click "Use Current Location" to automatically detect your position
              </li>
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                Confirm your selection when you're satisfied with the location
              </li>
            </ul>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default SelectLocationPage;