import React from 'react';
import SupplierHomeGUI from './home-page-components/SupplierHomeGUI';
import SupplierOrderDetails from './home-page-components/SupplierOrderDetails';
import SupplierTracking from './home-page-components/SupplierTracking';
import SupplierProfileGUI from './profile-page-components/SupplierProfileGUI';
import SupplierProductsGUI from './products-page-components/SupplierProductsGUI';
import SupplierAddProduct from './products-page-components/SupplierAddProduct';
import SupplierEditProduct from './products-page-components/SupplierEditProduct';
import SupplierManageOptions from './products-page-components/SupplierManageOptions';
import SupplierAnalyticsGUI from './analytics-page-components/SupplierAnalyticsGUI';
import SupplierNotificationsGUI from './notifications-page-components/SupplierNotificationsGUI';

type SupplierScreenContentProps = {
  title: string;
  path: string;
  children?: React.ReactNode;
};

export const SupplierScreenContent = ({ title, path, children }: SupplierScreenContentProps) => {

  return ((title==="SupplierHomeGUI") ? (
      <SupplierHomeGUI />
    ) : (title==="SupplierOrderDetails") ? (
      <SupplierOrderDetails />
    ) : (title==="SupplierTracking") ? (
      <SupplierTracking />
    ) : (title==="SupplierProfileGUI") ? (
      <SupplierProfileGUI />
    ) : (title==="SupplierProductsGUI") ? (
      <SupplierProductsGUI />
    ) : (title==="SupplierAddProduct") ? (
      <SupplierAddProduct />
    ) : (title==="SupplierEditProduct") ? (
      <SupplierEditProduct />
    ) : (title==="SupplierManageAdditions") ? (
      <SupplierManageOptions />
    ) : (title==="SupplierAnalyticsGUI") ? (
      <SupplierAnalyticsGUI />
    ) : (title==="SupplierNotificationsGUI") ? (
      <SupplierNotificationsGUI />
    ) : (
      null
    )
  );
};