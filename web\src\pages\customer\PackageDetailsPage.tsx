import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Package,
  MapPin,
  User,
  Phone,
  Clock,
  ArrowLeft,
  Navigation,
  Truck,
  CheckCircle,
  RotateCcw,
  Calendar,
  Hash,
  Star,
  Zap,
  Activity,
  Eye,
  MessageCircle,
  Shield,
  DollarSign,
  Timer,
  Route,
  FileText,
  Award,
  Loader2,
  AlertCircle,
  // More descriptive icons
  PackageCheck,
  MapPinOff,
  UserCheck,
  PhoneCall,
  CalendarDays,
  StarHalf,
  EyeOff,
  MessageSquare,
  ShieldCheck,
  TimerReset,
  PackageX,
  MapPinCheck,
  UserX,
  PhoneOff,
  ShieldAlert,
  PackageSearch,
  MapPinPlus,
  UserPlus,
  PhoneIncoming,
  ShieldX
} from 'lucide-react';
import { apiService } from '../../services/api';

// Interface for package data from backend
interface PackageData {
  _id: string;
  packageId: string;
  userId: string;
  senderName: string;
  senderPhone: string;
  recipientName: string;
  recipientPhone: string;
  pickupAddress: {
    street: string;
    city: string;
    coordinates: {
      lat: number;
      lng: number;
    };
    notes?: string;
  };
  deliveryAddress: {
    street: string;
    city: string;
    coordinates: {
      lat: number;
      lng: number;
    };
    notes?: string;
  };
  packageDetails: {
    type: string;
    description: string;
    weight?: number;
    dimensions?: {
      length: number;
      width: number;
      height: number;
    };
    value?: number;
    fragile: boolean;
  };
  serviceType: 'send_package' | 'request_pickup';
  status: 'pending' | 'confirmed' | 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'cancelled';
  priority: 'standard' | 'express' | 'urgent';
  cost: number;
  paymentMethod: 'cash' | 'card' | 'wallet';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  scheduledPickupTime?: string;
  actualPickupTime?: string;
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  driverId?: string;
  driverName?: string;
  driverPhone?: string;
  trackingUpdates: Array<{
    status: string;
    timestamp: string;
    message: string;
    location?: {
      lat: number;
      lng: number;
    };
  }>;
  photos?: {
    pickup?: string[];
    delivery?: string[];
  };
  signature?: {
    recipientSignature?: string;
    recipientName?: string;
    timestamp?: string;
  };
  notes?: string;
  rating?: {
    score: number;
    comment?: string;
    ratedAt: string;
  };
  createdAt: string;
  updatedAt: string;
}

const PackageDetailsPage: React.FC = () => {
  const { packageId } = useParams<{ packageId: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const packageType = searchParams.get('type') || 'sent';
  
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [activeTab, setActiveTab] = useState<'details' | 'tracking' | 'driver'>('details');

  // Backend integration state
  const [packageData, setPackageData] = useState<PackageData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch package details from backend
  useEffect(() => {
    const fetchPackageDetails = async () => {
      if (!packageId) {
        setError('No package ID provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const response = await apiService.getPackageByTrackingNumber(packageId);

        if (response.success && response.data) {
          setPackageData(response.data);
        } else {
          setError(response.message || 'Failed to fetch package details');
        }
      } catch (err) {
        console.error('Error fetching package details:', err);
        setError('Failed to load package details. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPackageDetails();
  }, [packageId]);

  // Handle scroll for header animation
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    let rafId: number;

    const handleScroll = () => {
      rafId = requestAnimationFrame(() => {
        const currentScrollY = window.scrollY;
        setScrollY(currentScrollY);

        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          setIsHeaderCompact(currentScrollY > 80);
        }, 50);
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
      cancelAnimationFrame(rafId);
    };
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4"
          >
            <Loader2 size={32} className="text-white" />
          </motion.div>
          <h2 className="text-2xl font-bold text-gray-700 mb-2">Loading Package Details</h2>
          <p className="text-gray-500">Please wait while we fetch your package information...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !packageData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle size={64} className="text-red-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-700 mb-2">Package Not Found</h2>
          <p className="text-gray-500 mb-6">{error || 'The package you\'re looking for doesn\'t exist.'}</p>
          <button
            onClick={() => navigate('/customer/packages')}
            className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            Back to Packages
          </button>
        </div>
      </div>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered': return <CheckCircle size={24} className="text-green-400" />;
      case 'in_transit': case 'out_for_delivery': return <Truck size={24} className="text-orange-400" />;
      case 'picked_up': return <PackageCheck size={24} className="text-blue-400" />;
      case 'confirmed': return <CheckCircle size={24} className="text-blue-400" />;
      case 'pending': return <Clock size={24} className="text-yellow-400" />;
      case 'cancelled': return <AlertCircle size={24} className="text-red-400" />;
      default: return <Package size={24} className="text-yellow-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered': return 'text-green-300 bg-green-500/20 border-green-400/50';
      case 'in_transit': case 'out_for_delivery': return 'text-orange-300 bg-orange-500/20 border-orange-400/50';
      case 'picked_up': return 'text-blue-300 bg-blue-500/20 border-blue-400/50';
      case 'confirmed': return 'text-blue-300 bg-blue-500/20 border-blue-400/50';
      case 'pending': return 'text-yellow-300 bg-yellow-500/20 border-yellow-400/50';
      case 'cancelled': return 'text-red-300 bg-red-500/20 border-red-400/50';
      default: return 'text-yellow-300 bg-yellow-500/20 border-yellow-400/50';
    }
  };

  const getStatusDisplayName = (status: string) => {
    switch (status) {
      case 'pending': return 'Pending';
      case 'confirmed': return 'Confirmed';
      case 'picked_up': return 'Picked Up';
      case 'in_transit': return 'In Transit';
      case 'out_for_delivery': return 'Out for Delivery';
      case 'delivered': return 'Delivered';
      case 'cancelled': return 'Cancelled';
      default: return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const getPackageSize = () => {
    const dimensions = packageData.packageDetails.dimensions;
    if (!dimensions) return 'Not specified';
    
    const volume = dimensions.length * dimensions.width * dimensions.height;
    if (volume < 1000) return 'Small';
    if (volume < 8000) return 'Medium';
    return 'Large';
  };

  const getPackageWeight = () => {
    if (!packageData.packageDetails.weight) return 'Not specified';
    return `${packageData.packageDetails.weight}g`;
  };

  const getPackageValue = () => {
    if (!packageData.packageDetails.value) return 'Not specified';
    return `₪${packageData.packageDetails.value}`;
  };

  const getEstimatedTime = () => {
    if (packageData.actualDeliveryTime) {
      const pickupTime = packageData.actualPickupTime ? new Date(packageData.actualPickupTime) : new Date(packageData.createdAt);
      const deliveryTime = new Date(packageData.actualDeliveryTime);
      const diffMs = deliveryTime.getTime() - pickupTime.getTime();
      const diffMins = Math.round(diffMs / (1000 * 60));
      return `${diffMins} mins`;
    }
    
    // Estimate based on priority and type
    const baseTime = packageData.priority === 'express' ? 30 : packageData.priority === 'urgent' ? 20 : 60;
    return `${baseTime}-${baseTime + 15} mins`;
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'text-green-400';
      case 'pending': return 'text-yellow-400';
      case 'failed': return 'text-red-400';
      case 'refunded': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'cash': return '💵';
      case 'card': return '💳';
      case 'wallet': return '👛';
      default: return '💰';
    }
  };

  const getPackageDimensions = () => {
    const dimensions = packageData.packageDetails.dimensions;
    if (!dimensions) return 'Not specified';
    return `${dimensions.length}cm × ${dimensions.width}cm × ${dimensions.height}cm`;
  };

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-indigo-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-cyan-500/30 to-blue-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-emerald-500/20 to-teal-500/20 rounded-full blur-3xl"
          />
        </div>

        {/* Main Content */}
        <div className="relative z-10">
          {/* Hero Header */}
          <div className="pt-20 pb-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="text-center mb-12"
              >
                <div className="inline-flex items-center gap-4 mb-6">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-2xl"
                  >
                    {packageData.serviceType === 'send_package' ? (
                      <Package className="text-white" size={36} />
                    ) : (
                      <RotateCcw className="text-white" size={36} />
                    )}
                  </motion.div>
                  <div className="text-left">
                    <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent pb-4">
                      Package #{packageData.packageId.slice(-8)}
                    </h1>
                    <motion.div
                      animate={{ width: ["0%", "100%"] }}
                      transition={{ duration: 2, delay: 0.5 }}
                      className="h-1 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full mt-0"
                    />
                  </div>
                </div>

                {/* Status Badge */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
                  className={`inline-flex items-center gap-3 px-6 ml-3 py-3 rounded-2xl text-lg font-bold border-2 ${getStatusColor(packageData.status)} backdrop-blur-sm mb-6`}
                >
                  {getStatusIcon(packageData.status)}
                  {getStatusDisplayName(packageData.status)}
                </motion.div>

                <p className="text-lg text-gray-300 max-w-2xl mx-auto">
                  {packageData.serviceType === 'send_package' ? 'Sent Package' : 'Pickup Request'} • {packageData.packageDetails.type} • Created {formatDate(packageData.createdAt).date}
                </p>
              </motion.div>

              {/* Navigation Tabs */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="mb-8"
              >
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-2 border border-white/20 max-w-md mx-auto">
                  <div className="flex gap-2">
                    {[
                      { key: 'details', label: 'Details', icon: FileText },
                      { key: 'tracking', label: 'Tracking', icon: Route },
                      { key: 'driver', label: 'Driver', icon: UserCheck }
                    ].map((tab) => (
                      <motion.button
                        key={tab.key}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setActiveTab(tab.key as any)}
                        className={`flex-1 px-4 py-3 rounded-xl text-sm font-semibold transition-all duration-300 flex items-center justify-center gap-2 ${
                          activeTab === tab.key
                            ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-lg'
                            : 'text-white hover:bg-white/20'
                        }`}
                      >
                        <tab.icon size={16} />
                        {tab.label}
                      </motion.button>
                    ))}
                  </div>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Content Sections */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
            <AnimatePresence mode="wait">
              {activeTab === 'details' && (
                <motion.div
                  key="details"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.5 }}
                  className="grid lg:grid-cols-2 gap-8"
                >
                  {/* Package Information */}
                  <div className="space-y-6">
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                    >
                      <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                        <div className="w-10 h-10 bg-orange-500/30 rounded-xl flex items-center justify-center">
                          <Package size={20} />
                        </div>
                        Package Information
                      </h3>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-gray-300 text-sm">Type</p>
                            <p className="text-white font-semibold">{packageData.packageDetails.type}</p>
                          </div>
                          <div>
                            <p className="text-gray-300 text-sm">Size</p>
                            <p className="text-white font-semibold">{getPackageSize()}</p>
                          </div>
                          <div>
                            <p className="text-gray-300 text-sm">Weight</p>
                            <p className="text-white font-semibold">{getPackageWeight()}</p>
                          </div>
                          <div>
                            <p className="text-gray-300 text-sm">Value</p>
                            <p className="text-white font-semibold">{getPackageValue()}</p>
                          </div>
                        </div>
                        <div className="pt-4 border-t border-white/20">
                          <div className="flex justify-between items-center">
                            <span className="text-gray-300">Total Cost</span>
                            <span className="text-3xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                              ₪{packageData.cost}
                            </span>
                          </div>
                        </div>
                        {packageData.packageDetails.fragile && (
                          <div className="flex items-center gap-2 text-yellow-400">
                            <ShieldAlert size={16} />
                            <span className="text-sm">Fragile Package</span>
                          </div>
                        )}
                        {packageData.priority === 'express' && (
                          <div className="flex items-center gap-2 text-yellow-400">
                            <Zap size={16} />
                            <span className="text-sm">Express Delivery</span>
                          </div>
                        )}
                        {packageData.priority === 'urgent' && (
                          <div className="flex items-center gap-2 text-red-400">
                            <Zap size={16} />
                            <span className="text-sm">Urgent Delivery</span>
                          </div>
                        )}
                        <div className="pt-4 border-t border-white/20">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-gray-300 text-sm">Dimensions</p>
                              <p className="text-white font-semibold">{getPackageDimensions()}</p>
                            </div>
                            <div>
                              <p className="text-gray-300 text-sm">Description</p>
                              <p className="text-white font-semibold">{packageData.packageDetails.description}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>

                    {/* Location Information */}
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                    >
                      <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-500/30 rounded-xl flex items-center justify-center">
                          <MapPin size={20} />
                        </div>
                        Locations
                      </h3>
                      <div className="space-y-6">
                        {/* Pickup Location */}
                                                    <div>
                              <div className="flex items-center gap-2 mb-3">
                                <MapPinCheck size={16} className="text-blue-400" />
                                <span className="text-gray-300 font-medium">Pickup Location</span>
                              </div>
                                                      <div className="bg-white/10 rounded-xl p-4">
                              <p className="text-white font-semibold mb-2">{packageData.pickupAddress.street}, {packageData.pickupAddress.city}</p>
                              <div className="text-sm text-gray-300">
                                <p>Contact: {packageData.senderName}</p>
                                <p>Phone: {packageData.senderPhone}</p>
                                {packageData.pickupAddress.notes && (
                                  <p className="mt-2 text-blue-300">Notes: {packageData.pickupAddress.notes}</p>
                                )}
                              </div>
                            </div>
                        </div>

                        {/* Dropoff Location */}
                                                      <div>
                              <div className="flex items-center gap-2 mb-3">
                                <MapPinPlus size={16} className="text-green-400" />
                                <span className="text-gray-300 font-medium">Delivery Location</span>
                              </div>
                            <div className="bg-white/10 rounded-xl p-4">
                              <p className="text-white font-semibold mb-2">{packageData.deliveryAddress.street}, {packageData.deliveryAddress.city}</p>
                              <div className="text-sm text-gray-300">
                                <p>Contact: {packageData.recipientName}</p>
                                <p>Phone: {packageData.recipientPhone}</p>
                                {packageData.deliveryAddress.notes && (
                                  <p className="mt-2 text-green-300">Notes: {packageData.deliveryAddress.notes}</p>
                                )}
                              </div>
                            </div>
                          </div>
                      </div>
                    </motion.div>
                  </div>

                  {/* Timing and Notes */}
                  <div className="space-y-6">
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                    >
                                              <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                          <div className="w-10 h-10 bg-purple-500/30 rounded-xl flex items-center justify-center">
                            <TimerReset size={20} />
                          </div>
                          Timing Information
                        </h3>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-gray-300 text-sm">Created</p>
                            <p className="text-white font-semibold">{formatDate(packageData.createdAt).date}</p>
                            <p className="text-gray-400 text-xs">{formatDate(packageData.createdAt).time}</p>
                          </div>
                          <div>
                            <p className="text-gray-300 text-sm">Estimated Time</p>
                            <p className="text-white font-semibold">{getEstimatedTime()}</p>
                          </div>
                        </div>
                        {packageData.actualPickupTime && (
                          <div className="pt-4 border-t border-white/20">
                            <p className="text-gray-300 text-sm">Pickup Time</p>
                            <p className="text-blue-400 font-semibold">{formatDate(packageData.actualPickupTime).time}</p>
                            <p className="text-gray-400 text-xs">{formatDate(packageData.actualPickupTime).date}</p>
                          </div>
                        )}
                        {packageData.actualDeliveryTime && (
                          <div className="pt-4 border-t border-white/20">
                            <p className="text-gray-300 text-sm">Delivery Time</p>
                            <p className="text-green-400 font-semibold">{formatDate(packageData.actualDeliveryTime).time}</p>
                            <p className="text-gray-400 text-xs">{formatDate(packageData.actualDeliveryTime).date}</p>
                          </div>
                        )}
                      </div>
                    </motion.div>

                    {/* Payment Information */}
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                    >
                      <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-500/30 rounded-xl flex items-center justify-center">
                          <span className="text-white text-xl">₪</span>
                        </div>
                        Payment Information
                      </h3>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-gray-300 text-sm">Payment Method</p>
                            <div className="flex items-center gap-2">
                              <span className="text-2xl">{getPaymentMethodIcon(packageData.paymentMethod)}</span>
                              <p className="text-white font-semibold capitalize">{packageData.paymentMethod}</p>
                            </div>
                          </div>
                          <div>
                            <p className="text-gray-300 text-sm">Payment Status</p>
                            <p className={`font-semibold ${getPaymentStatusColor(packageData.paymentStatus)}`}>
                              {packageData.paymentStatus.charAt(0).toUpperCase() + packageData.paymentStatus.slice(1)}
                            </p>
                          </div>
                        </div>
                        {packageData.scheduledPickupTime && (
                          <div className="pt-4 border-t border-white/20">
                            <p className="text-gray-300 text-sm">Scheduled Pickup Time</p>
                            <p className="text-blue-400 font-semibold">{formatDate(packageData.scheduledPickupTime).time}</p>
                            <p className="text-gray-400 text-xs">{formatDate(packageData.scheduledPickupTime).date}</p>
                          </div>
                        )}
                        {packageData.estimatedDeliveryTime && (
                          <div className="pt-4 border-t border-white/20">
                            <p className="text-gray-300 text-sm">Estimated Delivery Time</p>
                            <p className="text-green-400 font-semibold">{formatDate(packageData.estimatedDeliveryTime).time}</p>
                            <p className="text-gray-400 text-xs">{formatDate(packageData.estimatedDeliveryTime).date}</p>
                          </div>
                        )}
                      </div>
                    </motion.div>

                    {/* Notes */}
                    {packageData.notes && (
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                      >
                        <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                          <div className="w-10 h-10 bg-pink-500/30 rounded-xl flex items-center justify-center">
                            <MessageSquare size={20} />
                          </div>
                          Special Notes
                        </h3>
                        <div className="bg-white/10 rounded-xl p-4">
                          <p className="text-gray-200 leading-relaxed">{packageData.notes}</p>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </motion.div>
              )}

              {activeTab === 'tracking' && (
                <motion.div
                  key="tracking"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="space-y-6">
                    {/* Tracking History */}
                    <motion.div
                      whileHover={{ scale: 1.01 }}
                      className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                    >
                      <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-500/30 rounded-xl flex items-center justify-center">
                          <Route size={20} />
                        </div>
                        Tracking History
                      </h3>
                      <div className="space-y-4">
                        {packageData.trackingUpdates && packageData.trackingUpdates.length > 0 ? (
                          packageData.trackingUpdates.map((event, index) => (
                            <motion.div
                              key={index}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.1 }}
                              className="flex items-start gap-4 p-4 bg-white/10 rounded-xl"
                            >
                              <div className="w-3 h-3 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                              <div className="flex-1">
                                <div className="flex justify-between items-start mb-2">
                                  <h4 className="text-white font-semibold">{getStatusDisplayName(event.status)}</h4>
                                  <span className="text-gray-300 text-sm">
                                    {formatDate(event.timestamp).time}
                                  </span>
                                </div>
                                <p className="text-gray-300 text-sm">{event.message}</p>
                                <p className="text-gray-400 text-xs mt-1">
                                  {formatDate(event.timestamp).date}
                                </p>
                              </div>
                            </motion.div>
                          ))
                        ) : (
                          <div className="text-center py-8">
                            <PackageSearch size={48} className="text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-300">No tracking updates available yet.</p>
                            <p className="text-gray-400 text-sm">Updates will appear here as your package progresses.</p>
                          </div>
                        )}
                      </div>
                    </motion.div>

                    {/* Photos Section */}
                      <motion.div
                        whileHover={{ scale: 1.01 }}
                        className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                      >
                        <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                          <div className="w-10 h-10 bg-purple-500/30 rounded-xl flex items-center justify-center">
                            <Eye size={20} />
                          </div>
                          Package Photos
                        </h3>
                        <div className="space-y-4">
                            <div>
                              <h4 className="text-white font-semibold mb-3">Pickup Photos</h4>
                              {packageData.photos && packageData.photos.pickup && packageData.photos.pickup.length > 0 ? (
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                                  {packageData.photos.pickup.map((photo, index) => (
                                    <div key={index} className="aspect-square bg-white/10 rounded-lg overflow-hidden">
                                      <img src={photo} alt={`Pickup photo ${index + 1}`} className="w-full h-full object-cover" />
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <div className="text-center py-8">
                                  <PackageSearch size={48} className="text-gray-400 mx-auto mb-4" />
                                  <p className="text-gray-300">No pickup photos available yet.</p>
                                  <p className="text-gray-400 text-sm">Pickup photos will appear here as your package progresses.</p>
                                </div>
                              )}
                            </div>
                            <div>
                              <h4 className="text-white font-semibold mb-3">Delivery Photos</h4>
                              {packageData.photos && packageData.photos.delivery && packageData.photos.delivery.length > 0 ? (
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                                  {packageData.photos.delivery.map((photo, index) => (
                                    <div key={index} className="aspect-square bg-white/10 rounded-lg overflow-hidden">
                                      <img src={photo} alt={`Delivery photo ${index + 1}`} className="w-full h-full object-cover" />
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <div className="text-center py-8">
                                  <PackageSearch size={48} className="text-gray-400 mx-auto mb-4" />
                                  <p className="text-gray-300">No delivery photos available yet.</p>
                                  <p className="text-gray-400 text-sm">Delivery photos will appear here as your package progresses.</p>
                                </div>
                              )}
                            </div>
                        </div>
                      </motion.div>

                    {/* Signature Section */}
                    {packageData.signature && (
                      <motion.div
                        whileHover={{ scale: 1.01 }}
                        className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                      >
                        <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                          <div className="w-10 h-10 bg-indigo-500/30 rounded-xl flex items-center justify-center">
                            <Award size={20} />
                          </div>
                          Delivery Signature
                        </h3>
                        <div className="space-y-4">
                          {packageData.signature.recipientName && (
                            <div>
                              <p className="text-gray-300 text-sm">Recipient Name</p>
                              <p className="text-white font-semibold">{packageData.signature.recipientName}</p>
                            </div>
                          )}
                          {packageData.signature.timestamp && (
                            <div>
                              <p className="text-gray-300 text-sm">Signed At</p>
                              <p className="text-white font-semibold">{formatDate(packageData.signature.timestamp).time}</p>
                              <p className="text-gray-400 text-xs">{formatDate(packageData.signature.timestamp).date}</p>
                            </div>
                          )}
                          {packageData.signature.recipientSignature && (
                            <div className="pt-4 border-t border-white/20">
                              <p className="text-gray-300 text-sm mb-2">Digital Signature</p>
                              <div className="bg-white/10 rounded-lg p-4">
                                <img src={packageData.signature.recipientSignature} alt="Recipient signature" className="max-w-full h-auto" />
                              </div>
                            </div>
                          )}
                        </div>
                      </motion.div>
                    )}
                  </div>
                </motion.div>
              )}

              {activeTab === 'driver' && (
                <motion.div
                  key="driver"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.5 }}
                >
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                  >
                    <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                      <div className="w-10 h-10 bg-yellow-500/30 rounded-xl flex items-center justify-center">
                        <UserCheck size={20} />
                      </div>
                      Driver Information
                    </h3>
                    {packageData.driverName ? (
                      <div className="grid md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <div>
                            <p className="text-gray-300 text-sm">Driver Name</p>
                            <p className="text-white font-semibold text-lg">{packageData.driverName}</p>
                          </div>
                          <div>
                            <p className="text-gray-300 text-sm">Phone Number</p>
                            <p className="text-white font-semibold">{packageData.driverPhone}</p>
                          </div>
                                                     {packageData.rating && (
                             <div>
                               <p className="text-gray-300 text-sm">Rating</p>
                               <div className="flex items-center gap-2">
                                 <div className="flex items-center">
                                   {[...Array(5)].map((_, i) => (
                                     <Star
                                       key={i}
                                       size={16}
                                       className={i < Math.floor(packageData.rating!.score) ? 'text-yellow-400 fill-current' : 'text-gray-400'}
                                     />
                                   ))}
                                 </div>
                                 <span className="text-white font-semibold">{packageData.rating.score}</span>
                               </div>
                               {packageData.rating.comment && (
                                 <div className="mt-2">
                                   <p className="text-gray-300 text-sm">Comment</p>
                                   <p className="text-white text-sm italic">"{packageData.rating.comment}"</p>
                                 </div>
                               )}
                               <div className="mt-2">
                                 <p className="text-gray-300 text-xs">Rated on {formatDate(packageData.rating.ratedAt).date}</p>
                               </div>
                             </div>
                           )}
                        </div>
                        <div className="flex items-center justify-center">
                          <motion.div
                            animate={{ scale: [1, 1.1, 1] }}
                            transition={{ duration: 2, repeat: Infinity }}
                            className="w-32 h-32 bg-gradient-to-br from-yellow-500/30 to-orange-500/30 rounded-full flex items-center justify-center"
                          >
                            <UserCheck size={48} className="text-yellow-400" />
                          </motion.div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <UserX size={48} className="text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-300">Driver not assigned yet.</p>
                        <p className="text-gray-400 text-sm">A driver will be assigned when your package is confirmed.</p>
                      </div>
                    )}
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Sticky Header with Ultra-Smooth Scroll Animation */}
        <motion.div
          className="fixed left-0 right-0 transition-all duration-500"
          animate={{
            top: isHeaderCompact ? "0px" : "-100px",
            zIndex: isHeaderCompact ? 999 : 40,
            backgroundColor: isHeaderCompact
              ? "rgba(15, 23, 42, 0.98)"
              : "rgba(15, 23, 42, 0)",
            backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
            borderBottom: isHeaderCompact
              ? "1px solid rgba(255, 255, 255, 0.1)"
              : "1px solid rgba(255, 255, 255, 0)",
            height: isHeaderCompact ? "80px" : "0px",
          }}
          transition={{
            duration: 0.6,
            ease: [0.25, 0.46, 0.45, 0.94],
            type: "tween"
          }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              animate={{
                paddingTop: isHeaderCompact ? "1.5rem" : "2rem",
                paddingBottom: isHeaderCompact ? "1.5rem" : "2rem",
                height: isHeaderCompact ? "80px" : "auto",
              }}
              transition={{ duration: 0.3 }}
              className="flex items-center justify-between h-full"
            >
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/customer/packages')}
                className="flex items-center gap-3 text-white hover:text-gray-300 transition-colors"
              >
                <ArrowLeft size={24} />
                <span className="font-medium">Back to Packages</span>
              </motion.button>

              <motion.div
                animate={{
                  opacity: isHeaderCompact ? 1 : 0,
                  scale: isHeaderCompact ? 1 : 0.8,
                }}
                transition={{ duration: 0.3 }}
                className="flex items-center gap-4"
              >
                <div className="flex items-center gap-3">
                  <Package className="text-white" size={24} />
                  <span className="text-white font-semibold text-lg">Package #{packageData.packageId.slice(-8)}</span>
                </div>

                <div className={`px-3 py-1 rounded-lg text-sm font-medium ${getStatusColor(packageData.status)}`}>
                  {getStatusDisplayName(packageData.status)}
                </div>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </>
  );
};

export default PackageDetailsPage;