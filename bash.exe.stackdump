Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x1FEBA
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210285FF9, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF8E80  0002100690B4 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9160  00021006A49D (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD8B340000 ntdll.dll
7FFD8A880000 KERNEL32.DLL
7FFD88630000 KERNELBASE.dll
7FFD897D0000 USER32.dll
7FFD88600000 win32u.dll
7FFD8B2C0000 GDI32.dll
7FFD88BE0000 gdi32full.dll
7FFD88D20000 msvcp_win.dll
7FFD88A00000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD8AF60000 advapi32.dll
7FFD8B020000 msvcrt.dll
7FFD8AB60000 sechost.dll
7FFD8AA40000 RPCRT4.dll
7FFD87A50000 CRYPTBASE.DLL
7FFD88DD0000 bcryptPrimitives.dll
7FFD8B0D0000 IMM32.DLL
