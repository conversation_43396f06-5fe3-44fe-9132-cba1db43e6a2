import { Router } from 'express';
import { body, param } from 'express-validator';
import { PackageController } from '../controllers/packageController';
import { authenticate } from '../middleware/auth';
import { pickupRequestLimiter, packageCreationLimiter } from '../middleware/security';

const router = Router();

// Validation for creating packages
const createPackageValidation = [
  body('senderName')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Sender name is required'),
  body('senderPhone')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Sender phone is required'),
  body('recipientInfo.name')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Recipient name is required'),
  body('recipientInfo.phone')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Recipient phone is required'),
  body('pickupAddress.street')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Pickup street address is required'),
  body('pickupAddress.city')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Pickup city is required'),
  body('pickupAddress.coordinates.lat')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Pickup latitude must be a valid number between -90 and 90'),
  body('pickupAddress.coordinates.lng')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Pickup longitude must be a valid number between -180 and 180'),
  body('deliveryAddress.street')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Delivery street address is required'),
  body('deliveryAddress.city')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Delivery city is required'),
  body('deliveryAddress.coordinates.lat')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Delivery latitude must be a valid number between -90 and 90'),
  body('deliveryAddress.coordinates.lng')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Delivery longitude must be a valid number between -180 and 180'),
  body('packageDetails.type')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Package type is required'),
  body('packageDetails.description')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ min: 5, max: 1000 })
    .withMessage('Package description is required and must be between 5 and 1000 characters'),
  body('packageDetails.weight')
    .isFloat({ min: 0 })
    .withMessage('Package weight must be a valid number'),
  body('packageDetails.value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Package value must be a valid number'),
  body('packageDetails.fragile')
    .optional()
    .isBoolean()
    .withMessage('Fragile must be a boolean'),
  body('priority')
    .optional()
    .isIn(['standard', 'express', 'urgent'])
    .withMessage('Priority must be standard, express, or urgent'),
  body('paymentMethod')
    .notEmpty()
    .isIn(['cash', 'card', 'wallet'])
    .withMessage('Payment method is required and must be cash, card, or wallet'),
  body('scheduledPickupTime')
    .optional()
    .isString()
    .withMessage('Scheduled pickup time must be a string'),
  body('notes')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Notes must be a string with maximum 500 characters')
];

// Validation for pickup requests
const createPickupValidation = [
  body('pickupAddress.street')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Pickup street address is required and must be less than 200 characters'),
  body('pickupAddress.city')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Pickup city is required and must be less than 100 characters'),
  body('pickupAddress.coordinates.lat')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Pickup latitude must be a valid number between -90 and 90'),
  body('pickupAddress.coordinates.lng')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Pickup longitude must be a valid number between -180 and 180'),
  body('deliveryAddress.street')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Delivery street address is required and must be less than 200 characters'),
  body('deliveryAddress.city')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Delivery city is required and must be less than 100 characters'),
  body('deliveryAddress.coordinates.lat')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Delivery latitude must be a valid number between -90 and 90'),
  body('deliveryAddress.coordinates.lng')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Delivery longitude must be a valid number between -180 and 180'),
  body('senderName')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Sender name is required and must be between 2 and 100 characters'),
  body('senderPhone')
    .notEmpty()
    .isString()
    .trim()
    .matches(/^\+?[\d\s\-\(\)]+$/)
    .isLength({ min: 8, max: 20 })
    .withMessage('Sender phone is required and must be a valid phone number'),
  body('recipientName')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Recipient name is required and must be between 2 and 100 characters'),
  body('recipientPhone')
    .notEmpty()
    .isString()
    .trim()
    .matches(/^\+?[\d\s\-\(\)]+$/)
    .isLength({ min: 8, max: 20 })
    .withMessage('Recipient phone is required and must be a valid phone number'),
  body('packageDetails.type')
    .notEmpty()
    .isString()
    .trim()
    .isIn(['documents', 'electronics', 'clothing', 'food', 'gifts', 'general'])
    .withMessage('Package type is required and must be a valid type'),
  body('packageDetails.description')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ min: 2, max: 1000 })
    .withMessage('Package description is required and must be between 5 and 1000 characters'),
  body('packageDetails.size')
    .notEmpty()
    .isIn(['small', 'medium', 'large'])
    .withMessage('Package size is required and must be small, medium, or large'),
  body('packageDetails.weight')
    .isFloat({ min: 0.1, max: 100 })
    .withMessage('Package weight must be a valid number between 0.1 and 100 kg'),
  body('packageDetails.value')
    .optional()
    .isFloat({ min: 0, max: 10000 })
    .withMessage('Package value must be a valid number between 0 and 10,000'),
  body('packageDetails.fragile')
    .optional()
    .isBoolean()
    .withMessage('Fragile must be a boolean'),
  body('preferredTime')
    .optional()
    .isISO8601()
    .custom((value) => {
      if (value) {
        const pickupTime = new Date(value);
        const now = new Date();
        const minTime = new Date(now.getTime() + 30 * 60 * 1000); // 30 minutes from now
        const maxTime = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days from now
        
        if (pickupTime < minTime) {
          throw new Error('Pickup time must be at least 30 minutes in the future');
        }
        if (pickupTime > maxTime) {
          throw new Error('Pickup time cannot be more than 7 days in the future');
        }
      }
      return true;
    })
    .withMessage('Invalid pickup time - must be between 30 minutes and 7 days from now'),
  body('notes')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Notes must be a string with maximum 1000 characters')
];

// Validation for updating package status
const updatePackageStatusValidation = [
  param('trackingNumber')
    .notEmpty()
    .isString()
    .withMessage('Tracking number is required'),
  body('status')
    .isIn(['pending', 'confirmed', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'cancelled'])
    .withMessage('Status must be a valid package status'),
  body('trackingUpdate.message')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Tracking message must be a string with maximum 200 characters'),
  body('trackingUpdate.location.lat')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be a valid number between -90 and 90'),
  body('trackingUpdate.location.lng')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be a valid number between -180 and 180')
];

// All routes require authentication
router.use(authenticate);

// Package routes
router.get('/types', PackageController.getPackageTypes);
router.post('/', packageCreationLimiter, createPackageValidation, PackageController.createPackage);
router.post('/request-pickup', pickupRequestLimiter, createPickupValidation, PackageController.requestPickup);
router.get('/', PackageController.getUserPackages);
router.get('/:trackingNumber', PackageController.getPackageByTrackingNumber);
router.put('/:trackingNumber/status', updatePackageStatusValidation, PackageController.updatePackageStatus);

export default router;
