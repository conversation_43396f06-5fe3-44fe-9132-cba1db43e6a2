import { Response } from 'express';
import { Package } from '../models/Package';
import { validationResult } from 'express-validator';
import { AuthenticatedRequest } from '../types';

export class PackageController {
  // Get available package types
  static async getPackageTypes(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const packageTypes = [
        {
          id: 'documents',
          name: 'Documents',
          description: 'Papers, contracts, certificates',
          estimatedTime: '2-4 hours',
          basePrice: 15,
          icon: 'FileText'
        },
        {
          id: 'electronics',
          name: 'Electronics',
          description: 'Phones, laptops, gadgets',
          estimatedTime: '3-6 hours',
          basePrice: 25,
          icon: 'Smartphone'
        },
        {
          id: 'clothing',
          name: 'Clothing',
          description: 'Clothes, shoes, accessories',
          estimatedTime: '4-8 hours',
          basePrice: 20,
          icon: 'ShoppingBag'
        },
        {
          id: 'food',
          name: 'Food & Drinks',
          description: 'Meals, beverages, snacks',
          estimatedTime: '1-2 hours',
          basePrice: 12,
          icon: 'Coffee'
        },
        {
          id: 'gifts',
          name: 'Gifts',
          description: 'Presents, flowers, surprises',
          estimatedTime: '2-4 hours',
          basePrice: 18,
          icon: 'Gift'
        },
        {
          id: 'general',
          name: 'General Items',
          description: 'Other items and packages',
          estimatedTime: '3-5 hours',
          basePrice: 18,
          icon: 'Package'
        }

      ];

      res.status(200).json({
        success: true,
        data: packageTypes
      });
    } catch (error) {
      console.error('Error fetching package types:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch package types'
      });
    }
  }

  // Create a new package delivery request
  static async createPackage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const {
        senderName,
        senderPhone,
        recipientInfo,
        pickupAddress,
        deliveryAddress,
        packageDetails,
        priority = 'standard',
        paymentMethod,
        scheduledPickupTime,
        notes
      } = req.body;

      // Generate unique tracking number
      const trackingNumber = `PKG-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // Get package type base price
      const packageTypePrices: Record<string, number> = {
        'documents': 15,
        'electronics': 25,
        'clothing': 20,
        'food': 12,
        'gifts': 18,
        'general': 18
      };

      const baseCost = packageTypePrices[packageDetails.type] || 18;
      const sizeMultiplier = packageDetails.size === 'large' ? 1.5 : packageDetails.size === 'medium' ? 1.2 : 1;

      // Calculate base cost with size
      let cost = baseCost * sizeMultiplier;

      // Apply priority multiplier
      if (priority === 'express') cost *= 1.5;
      if (priority === 'urgent') cost *= 2;

      // Add fragile handling fee (additive, not multiplicative)
      if (packageDetails.fragile) cost += 3;

      // Add insurance if package has value (with security limits)
      if (packageDetails.value && packageDetails.value > 0) {
        // Security: Cap maximum package value to prevent abuse
        const maxPackageValue = 10000; // $10,000 max
        const actualValue = Math.min(packageDetails.value, maxPackageValue);
        const insuranceFee = Math.max(5, actualValue * 0.02); // 2% of value, minimum $5
        cost += insuranceFee;
      }

      cost = Math.round(cost * 100) / 100; // Round to 2 decimal places

      // Security: Validate cost is reasonable (prevent manipulation)
      const minCost = 5;
      const maxCost = 1000;
      if (cost < minCost || cost > maxCost) {
        res.status(400).json({
          success: false,
          message: 'Invalid package cost calculated'
        });
        return;
      }

             const packageDelivery = new Package({
         packageId: trackingNumber,
         userId: userId,
         senderName,
         senderPhone,
         recipientName: recipientInfo.name,
         recipientPhone: recipientInfo.phone,
         pickupAddress,
         deliveryAddress,
         packageDetails,
         serviceType: 'send_package',
         priority,
         cost,
         paymentMethod,
         scheduledPickupTime,
         notes,
         status: 'pending',
         paymentStatus: 'pending'
       });

      await packageDelivery.save();

      res.status(201).json({
        success: true,
        message: 'Package delivery request created successfully',
        data: packageDelivery
      });
    } catch (error) {
      console.error('Error creating package:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create package delivery request',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get user's packages
  static async getUserPackages(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const { page = 1, limit = 20, status, type, search } = req.query;
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      const query: any = { userId: userId };
      
      // Filter by status if provided
      if (status && status !== 'All') {
        query.status = status;
      }

      // Filter by service type if provided
      if (type) {
        query.serviceType = type;
      }

      // Add search functionality
      if (search && typeof search === 'string' && search.trim()) {
        const searchRegex = new RegExp(search.trim(), 'i');
        query.$or = [
          { packageId: searchRegex },
          { 'pickupAddress.street': searchRegex },
          { 'deliveryAddress.street': searchRegex },
          { recipientName: searchRegex },
          { 'packageDetails.type': searchRegex }
        ];
      }

      const packages = await Package.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limitNum)
        .select('-__v');

      const total = await Package.countDocuments(query);

      // Get separate totals for each service type (for stats)
      const sendPackagesTotal = await Package.countDocuments({ 
        userId: userId,
        serviceType: 'send_package',
        ...(status && status !== 'All' ? { status } : {}),
        ...(search && typeof search === 'string' && search.trim() ? {
          $or: [
            { packageId: new RegExp(search.trim(), 'i') },
            { 'pickupAddress.street': new RegExp(search.trim(), 'i') },
            { 'deliveryAddress.street': new RegExp(search.trim(), 'i') },
            { recipientName: new RegExp(search.trim(), 'i') },
            { 'packageDetails.type': new RegExp(search.trim(), 'i') }
          ]
        } : {})
      });

      const pickupPackagesTotal = await Package.countDocuments({ 
        userId: userId,
        serviceType: 'request_pickup',
        ...(status && status !== 'All' ? { status } : {}),
        ...(search && typeof search === 'string' && search.trim() ? {
          $or: [
            { packageId: new RegExp(search.trim(), 'i') },
            { 'pickupAddress.street': new RegExp(search.trim(), 'i') },
            { 'deliveryAddress.street': new RegExp(search.trim(), 'i') },
            { recipientName: new RegExp(search.trim(), 'i') },
            { 'packageDetails.type': new RegExp(search.trim(), 'i') }
          ]
        } : {})
      });

      res.json({
        success: true,
        data: packages,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum),
          hasNextPage: pageNum < Math.ceil(total / limitNum),
          hasPrevPage: pageNum > 1
        },
        stats: {
          sendPackagesTotal,
          pickupPackagesTotal,
          totalPackages: sendPackagesTotal + pickupPackagesTotal
        }
      });
    } catch (error) {
      console.error('Error fetching user packages:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch packages',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get package by tracking number
  static async getPackageByTrackingNumber(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { trackingNumber } = req.params;
      const userId = req.user?._id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      let packageDelivery;
      if (trackingNumber.length === 24 && /^[0-9a-fA-F]{24}$/.test(trackingNumber)) {
        packageDelivery = await Package.findOne({ 
          _id: trackingNumber,
          userId 
        }).select('-__v');
      } else {
        packageDelivery = await Package.findOne({ 
          packageId: trackingNumber,
          userId 
        }).select('-__v');
      }

      if (!packageDelivery) {
        res.status(404).json({
          success: false,
          message: 'Package not found'
        });
        return;
      }
      res.status(200).json({
        success: true,
        data: packageDelivery
      });
    } catch (error) {
      console.error('Error fetching package:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch package'
      });
    }
  }

  // Update package status
  static async updatePackageStatus(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { trackingNumber } = req.params;
      const { status, trackingUpdate } = req.body;

      const packageDelivery = await Package.findOne({ 
        $or: [
          { packageId: trackingNumber },
          { _id: trackingNumber }
        ]
      });

      if (!packageDelivery) {
        res.status(404).json({
          success: false,
          message: 'Package not found'
        });
        return;
      }

      packageDelivery.status = status;

      if (trackingUpdate) {
        packageDelivery.trackingUpdates.push({
          status,
          timestamp: new Date(),
          message: trackingUpdate.message,
          location: trackingUpdate.location
        });
      }

      // Update timestamps based on status
      if (status === 'picked_up' && !packageDelivery.actualPickupTime) {
        packageDelivery.actualPickupTime = new Date();
      } else if (status === 'delivered' && !packageDelivery.actualDeliveryTime) {
        packageDelivery.actualDeliveryTime = new Date();
      }

      await packageDelivery.save();

      res.json({
        success: true,
        message: 'Package status updated successfully',
        data: packageDelivery
      });
    } catch (error) {
      console.error('Error updating package status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update package status',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Request pickup for existing package
  static async requestPickup(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const {
        pickupAddress,
        deliveryAddress,
        senderName,
        senderPhone,
        recipientName,
        recipientPhone,
        packageDetails,
        preferredTime,
        notes
      } = req.body;

      // Generate unique tracking number for pickup request
      const trackingNumber = `PU-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const packageId = `PKG-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // Calculate cost using the same logic as send package
      const packageTypePrices: Record<string, number> = {
        'documents': 15,
        'electronics': 25,
        'clothing': 20,
        'food': 12,
        'gifts': 18,
        'general': 18
      };

      const baseCost = packageTypePrices[packageDetails.type] || 18;
      const sizeMultiplier = packageDetails.size === 'large' ? 1.5 : packageDetails.size === 'medium' ? 1.2 : 1;

      // Calculate base cost with size
      let cost = baseCost * sizeMultiplier;

      // Apply priority multiplier (default to standard for pickup requests)
      const priority = 'standard';
      // For pickup requests, we use standard priority by default
      // Priority multipliers are not applied for pickup requests

      // Add fragile handling fee (additive, not multiplicative)
      if (packageDetails.fragile) cost += 3;

      // Add insurance if package has value (with security limits)
      if (packageDetails.value && packageDetails.value > 0) {
        // Security: Cap maximum package value to prevent abuse
        const maxPackageValue = 10000; // $10,000 max
        const actualValue = Math.min(packageDetails.value, maxPackageValue);
        const insuranceFee = Math.max(5, actualValue * 0.02); // 2% of value, minimum $5
        cost += insuranceFee;
      }

      cost = Math.round(cost * 100) / 100; // Round to 2 decimal places

      // Security: Validate cost is reasonable (prevent manipulation)
      const minCost = 5;
      const maxCost = 1000;
      if (cost < minCost || cost > maxCost) {
        res.status(400).json({
          success: false,
          message: 'Invalid package cost calculated'
        });
        return;
      }

      // Get user info for sender details
      const user = req.user;

      const pickupRequest = new Package({
        packageId,
        userId: userId.toString(),
        senderName,
        senderPhone,
        recipientName,
        recipientPhone,
        pickupAddress,
        deliveryAddress,
        packageDetails,
        serviceType: 'request_pickup',
        priority,
        cost,
        paymentMethod: 'cash',
        scheduledPickupTime: preferredTime === 'asap' ? new Date() : new Date(preferredTime),
        notes,
        status: 'pending',
        paymentStatus: 'pending'
      });

      await pickupRequest.save();

      res.status(201).json({
        success: true,
        message: 'Pickup request created successfully',
        data: pickupRequest
      });
    } catch (error) {
      console.error('Error creating pickup request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create pickup request',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }
}
