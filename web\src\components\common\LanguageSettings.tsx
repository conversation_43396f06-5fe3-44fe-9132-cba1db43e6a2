import React from 'react';
import { useTranslation } from 'react-i18next';
import { Globe, Check, Settings } from 'lucide-react';
import { useLanguageStore, type Language } from '../../stores/languageStore';
import { motion, AnimatePresence } from 'framer-motion';

interface LanguageSettingsProps {
  className?: string;
  showTitle?: boolean;
  variant?: 'card' | 'list' | 'grid';
}

export const LanguageSettings: React.FC<LanguageSettingsProps> = ({
  className = '',
  showTitle = true,
  variant = 'card'
}) => {
  const { t } = useTranslation();
  const { currentLanguage, setLanguage, isRTL } = useLanguageStore();

  const languages = [
    { 
      code: 'en' as Language, 
      name: 'English', 
      nativeName: 'English', 
      flag: '🇺🇸', 
      short: 'EN',
      description: 'English language'
    },
    { 
      code: 'ar' as Language, 
      name: 'Arabic', 
      nativeName: 'العربية', 
      flag: '🇵🇸', 
      short: 'ع',
      description: 'اللغة العربية'
    },
  ];

  const handleLanguageChange = async (language: Language) => {
    await setLanguage(language);
  };

  if (variant === 'list') {
    return (
      <div className={`space-y-4 ${className}`}>
        {showTitle && (
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-primary-100 rounded-lg">
              <Settings size={20} className="text-primary-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {t('language.selectLanguage')}
              </h3>
              <p className="text-sm text-gray-600">
                Choose your preferred language for the application
              </p>
            </div>
          </div>
        )}

        <div className="space-y-3">
          {languages.map((lang, index) => (
            <motion.button
              key={lang.code}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => handleLanguageChange(lang.code)}
              className={`w-full flex items-center gap-4 p-4 rounded-xl border-2 transition-all duration-300 ${
                currentLanguage === lang.code
                  ? 'border-primary-500 bg-primary-50 shadow-lg'
                  : 'border-gray-200 bg-white hover:border-primary-200 hover:bg-primary-50/50'
              }`}
            >
              <div className="flex items-center gap-3">
                <span className="text-2xl">{lang.flag}</span>
                <div className="text-left">
                  <div className="font-semibold text-gray-900">
                    {lang.nativeName}
                  </div>
                  <div className="text-sm text-gray-600">
                    {lang.name}
                  </div>
                </div>
              </div>
              
              <div className="ml-auto flex items-center gap-2">
                {currentLanguage === lang.code && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="p-1 bg-primary-500 rounded-full"
                  >
                    <Check size={16} className="text-white" />
                  </motion.div>
                )}
                <span className={`text-sm font-medium ${
                  currentLanguage === lang.code ? 'text-primary-600' : 'text-gray-500'
                }`}>
                  {lang.short}
                </span>
              </div>
            </motion.button>
          ))}
        </div>
      </div>
    );
  }

  if (variant === 'grid') {
    return (
      <div className={`space-y-4 ${className}`}>
        {showTitle && (
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-primary-100 rounded-lg">
              <Globe size={20} className="text-primary-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {t('language.selectLanguage')}
              </h3>
              <p className="text-sm text-gray-600">
                Choose your preferred language
              </p>
            </div>
          </div>
        )}

        <div className="grid grid-cols-2 gap-4">
          {languages.map((lang, index) => (
            <motion.button
              key={lang.code}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => handleLanguageChange(lang.code)}
              className={`p-6 rounded-xl border-2 transition-all duration-300 text-center ${
                currentLanguage === lang.code
                  ? 'border-primary-500 bg-gradient-to-br from-primary-50 to-secondary-50 shadow-lg'
                  : 'border-gray-200 bg-white hover:border-primary-200 hover:bg-primary-50/50'
              }`}
            >
              <div className="space-y-3">
                <div className="text-3xl">{lang.flag}</div>
                <div>
                  <div className="font-semibold text-gray-900">
                    {lang.nativeName}
                  </div>
                  <div className="text-sm text-gray-600">
                    {lang.name}
                  </div>
                </div>
                {currentLanguage === lang.code && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="flex justify-center"
                  >
                    <div className="p-1 bg-primary-500 rounded-full">
                      <Check size={16} className="text-white" />
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.button>
          ))}
        </div>
      </div>
    );
  }

  // Default card variant
  return (
    <div className={`bg-white rounded-2xl shadow-lg border border-gray-200 p-6 ${className}`}>
      {showTitle && (
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-primary-100 rounded-lg">
            <Globe size={20} className="text-primary-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {t('language.selectLanguage')}
            </h3>
            <p className="text-sm text-gray-600">
              Choose your preferred language for the application
            </p>
          </div>
        </div>
      )}

      <div className="space-y-3">
        {languages.map((lang, index) => (
          <motion.button
            key={lang.code}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            onClick={() => handleLanguageChange(lang.code)}
            className={`w-full flex items-center gap-4 p-4 rounded-xl border-2 transition-all duration-300 ${
              currentLanguage === lang.code
                ? 'border-primary-500 bg-gradient-to-r from-primary-50 to-secondary-50 shadow-md'
                : 'border-gray-200 bg-white hover:border-primary-200 hover:bg-primary-50/50'
            }`}
          >
            <div className="flex items-center gap-3">
              <span className="text-2xl">{lang.flag}</span>
              <div className="text-left">
                <div className="font-semibold text-gray-900">
                  {lang.nativeName}
                </div>
                <div className="text-sm text-gray-600">
                  {lang.description}
                </div>
              </div>
            </div>
            
            <div className="ml-auto flex items-center gap-2">
              {currentLanguage === lang.code && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="p-1 bg-primary-500 rounded-full"
                >
                  <Check size={16} className="text-white" />
                </motion.div>
              )}
              <span className={`text-sm font-medium ${
                currentLanguage === lang.code ? 'text-primary-600' : 'text-gray-500'
              }`}>
                {lang.short}
              </span>
            </div>
          </motion.button>
        ))}
      </div>
    </div>
  );
}; 