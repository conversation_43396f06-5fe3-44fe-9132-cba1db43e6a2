import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import en from './translations/en.json';
import ar from './translations/ar.json';

const resources = {
  en: {
    translation: en,
  },
  ar: {
    translation: ar,
  },
};

// Get device language with fallback
const getDeviceLanguage = () => {
  try {
    const browserLanguage = navigator.language || navigator.languages?.[0];
    if (!browserLanguage || typeof browserLanguage !== 'string') {
      return 'en'; // Default fallback
    }
    const deviceLanguage = browserLanguage.split('-')[0];
    return deviceLanguage === 'ar' ? 'ar' : 'en';
  } catch (error) {
    console.warn('Error getting device language:', error);
    return 'en'; // Default fallback
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      lookupLocalStorage: 'wasel-language',
    },

    interpolation: {
      escapeValue: false, // React already escapes values
    },

    react: {
      useSuspense: false,
    },
  });

// Set initial language based on device or stored preference
const initializeLanguage = () => {
  const savedLanguage = localStorage.getItem('wasel-language');
  let language: 'en' | 'ar';
  
  if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ar')) {
    language = savedLanguage as 'en' | 'ar';
  } else {
    language = getDeviceLanguage();
  }

  i18n.changeLanguage(language);
  return language;
};

// Initialize language on load
initializeLanguage();

export default i18n; 