import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Globe, ChevronDown, Check } from 'lucide-react';
import { useLanguageStore, type Language } from '../../stores/languageStore';
import { motion, AnimatePresence } from 'framer-motion';

interface LanguageSwitcherProps {
  variant?: 'button' | 'dropdown' | 'compact' | 'floating';
  className?: string;
  onLanguageChange?: () => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = 'button',
  className = '',
  onLanguageChange,
  position = 'top-right'
}) => {
  const { t } = useTranslation();
  const { currentLanguage, setLanguage, isRTL } = useLanguageStore();
  const [isOpen, setIsOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleLanguageChange = async (language: Language) => {
    await setLanguage(language);
    setIsOpen(false);
    onLanguageChange?.();
  };

  const languages = [
    { code: 'en' as Language, name: 'English', nativeName: 'English', flag: '🇺🇸', short: 'EN' },
    { code: 'ar' as Language, name: 'Arabic', nativeName: 'العربية', flag: '🇵🇸', short: 'ع' },
  ];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isOpen && !(event.target as Element).closest('.language-switcher')) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  if (variant === 'floating') {
    return (
      <div className={`fixed ${position === 'top-right' ? 'top-4 right-4' : position === 'top-left' ? 'top-4 left-4' : position === 'bottom-right' ? 'bottom-4 right-4' : 'bottom-4 left-4'} z-50 language-switcher`}>
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
          className="relative"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onHoverStart={() => setIsHovered(true)}
            onHoverEnd={() => setIsHovered(false)}
            onClick={() => setIsOpen(!isOpen)}
            className="flex items-center gap-2 px-4 py-3 bg-white/90 backdrop-blur-md border border-white/20 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-white/95"
          >
            <motion.div
              animate={{ rotate: isHovered ? 360 : 0 }}
              transition={{ duration: 0.6, ease: "easeInOut" }}
            >
              <Globe size={20} className="text-gray-700" />
            </motion.div>
            <span className="text-sm font-semibold text-gray-700">
              {currentLanguage === 'en' ? 'EN' : 'ع'}
            </span>
            <motion.div
              animate={{ rotate: isOpen ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown size={16} className="text-gray-500" />
            </motion.div>
          </motion.button>

          <AnimatePresence>
            {isOpen && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -10 }}
                transition={{ duration: 0.2 }}
                className="absolute top-full right-0 mt-2 w-56 bg-white/95 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl overflow-hidden"
              >
                <div className="p-2">
                  {languages.map((lang, index) => (
                    <motion.button
                      key={lang.code}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      onClick={() => handleLanguageChange(lang.code)}
                      className={`w-full flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-200 group ${
                        currentLanguage === lang.code 
                          ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-lg' 
                          : 'hover:bg-gray-50 text-gray-700'
                      }`}
                    >
                      <span className="text-xl">{lang.flag}</span>
                      <div className="flex flex-col items-start">
                        <span className={`text-sm font-semibold ${currentLanguage === lang.code ? 'text-white' : 'text-gray-800'}`}>
                          {lang.nativeName}
                        </span>
                        <span className={`text-xs ${currentLanguage === lang.code ? 'text-white/80' : 'text-gray-500'}`}>
                          {lang.name}
                        </span>
                      </div>
                      {currentLanguage === lang.code && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="ml-auto"
                        >
                          <Check size={16} className="text-white" />
                        </motion.div>
                      )}
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={`flex items-center gap-1 bg-white/90 backdrop-blur-md rounded-full p-1 shadow-lg border border-white/20 ${className}`}>
        {languages.map((lang) => (
          <motion.button
            key={lang.code}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleLanguageChange(lang.code)}
            className={`flex items-center gap-2 px-3 py-2 rounded-full transition-all duration-300 ${
              currentLanguage === lang.code
                ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-lg'
                : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            <span className="text-lg">{lang.flag}</span>
            <span className="text-sm font-semibold">
              {lang.short}
            </span>
          </motion.button>
        ))}
      </div>
    );
  }

  if (variant === 'dropdown') {
    return (
      <div className={`relative language-switcher ${className}`}>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-2 px-4 py-2 bg-white/90 backdrop-blur-md border border-white/20 rounded-xl hover:bg-white/95 transition-all duration-200 shadow-lg"
        >
          <motion.div
            animate={{ rotate: isHovered ? 360 : 0 }}
            transition={{ duration: 0.6, ease: "easeInOut" }}
            onHoverStart={() => setIsHovered(true)}
            onHoverEnd={() => setIsHovered(false)}
          >
            <Globe size={18} className="text-gray-700" />
          </motion.div>
          <span className="text-sm font-semibold text-gray-700">
            {currentLanguage === 'en' ? 'EN' : 'ع'}
          </span>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown size={16} className="text-gray-500" />
          </motion.div>
        </motion.button>

        <AnimatePresence>
          {isOpen && (
            <>
              {/* Backdrop */}
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 z-10" 
                onClick={() => setIsOpen(false)}
              />
              
              {/* Dropdown */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -10 }}
                transition={{ duration: 0.2 }}
                className="absolute top-full right-0 mt-2 w-56 bg-white/95 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl z-20 overflow-hidden"
              >
                <div className="p-2">
                  {languages.map((lang, index) => (
                    <motion.button
                      key={lang.code}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      onClick={() => handleLanguageChange(lang.code)}
                      className={`w-full flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-200 group ${
                        currentLanguage === lang.code 
                          ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-lg' 
                          : 'hover:bg-gray-50 text-gray-700'
                      }`}
                    >
                      <span className="text-xl">{lang.flag}</span>
                      <div className="flex flex-col items-start">
                        <span className={`text-sm font-semibold ${currentLanguage === lang.code ? 'text-white' : 'text-gray-800'}`}>
                          {lang.nativeName}
                        </span>
                        <span className={`text-xs ${currentLanguage === lang.code ? 'text-white/80' : 'text-gray-500'}`}>
                          {lang.name}
                        </span>
                      </div>
                      {currentLanguage === lang.code && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="ml-auto"
                        >
                          <Check size={16} className="text-white" />
                        </motion.div>
                      )}
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>
    );
  }

  // Default button variant
  return (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={() => handleLanguageChange(currentLanguage === 'en' ? 'ar' : 'en')}
      className={`flex items-center gap-2 px-4 py-2 bg-white/90 backdrop-blur-md border border-white/20 rounded-xl hover:bg-white/95 transition-all duration-200 shadow-lg ${className}`}
    >
      <motion.div
        animate={{ rotate: isHovered ? 360 : 0 }}
        transition={{ duration: 0.6, ease: "easeInOut" }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
      >
        <Globe size={18} className="text-gray-700" />
      </motion.div>
      <span className="text-sm font-semibold text-gray-700">
        {currentLanguage === 'en' ? 'EN' : 'ع'}
      </span>
    </motion.button>
  );
}; 