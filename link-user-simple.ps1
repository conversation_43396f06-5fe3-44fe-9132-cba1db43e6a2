Write-Host "🔗 Linking user to 3a-kefak supplier..." -ForegroundColor Yellow

$API_BASE = "http://***********:3000/api"

# Step 1: Login
$loginData = @{
    email = "<EMAIL>"
    password = "SfE#76132"
} | ConvertTo-Json

Write-Host "📧 Logging in..." -ForegroundColor Cyan

$loginResponse = Invoke-WebRequest -Uri "$API_BASE/auth/login" -Method POST -Body $loginData -ContentType "application/json"
$loginResult = $loginResponse.Content | ConvertFrom-Json

if ($loginResult.success) {
    $token = $loginResult.data.accessToken
    Write-Host "✅ Login successful!" -ForegroundColor Green
    Write-Host "Current supplierId: $($loginResult.data.user.supplierId)" -ForegroundColor White
    
    # Step 2: Link to supplier
    Write-Host "`n🔗 Linking to 3a-kefak..." -ForegroundColor Cyan
    
    $linkData = @{ existingSupplierId = "3a-kefak" } | ConvertTo-Json
    $headers = @{ 
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json" 
    }
    
    $linkResponse = Invoke-WebRequest -Uri "$API_BASE/suppliers/link-to-existing" -Method POST -Body $linkData -Headers $headers
    $linkResult = $linkResponse.Content | ConvertFrom-Json
    
    if ($linkResult.success) {
        Write-Host "✅ Link successful!" -ForegroundColor Green
        Write-Host "New supplierId: $($linkResult.data.user.supplierId)" -ForegroundColor Cyan
        
        # Step 3: Test profile access
        Write-Host "`n🧪 Testing profile access..." -ForegroundColor Cyan
        
        $profileResponse = Invoke-WebRequest -Uri "$API_BASE/suppliers/profile" -Method GET -Headers $headers
        $profileResult = $profileResponse.Content | ConvertFrom-Json
        
        Write-Host "✅ Profile access works!" -ForegroundColor Green
        Write-Host "Supplier: $($profileResult.data.supplier.name)" -ForegroundColor Cyan
        Write-Host "isActive: $($profileResult.data.supplier.isActive)" -ForegroundColor Cyan
        
        Write-Host "`n🎉 SUCCESS! Now restart the mobile app and login." -ForegroundColor Green
        
    } else {
        Write-Host "❌ Link failed: $($linkResult.message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Login failed: $($loginResult.message)" -ForegroundColor Red
}
