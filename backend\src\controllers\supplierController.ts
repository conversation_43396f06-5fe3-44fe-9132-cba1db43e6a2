import { Request, Response } from 'express';
import { Supplier } from '../models/Supplier';
import User from '../models/User';
import { validationResult } from 'express-validator';
import { AuthenticatedRequest } from '../types';

export class SupplierController {
  // Get all active suppliers with optional filtering
  static async getSuppliers(req: Request, res: Response) {
    try {
      const { 
        category, 
        search, 
        lat, 
        lng, 
        radius = 10, 
        page = 1, 
        limit = 20,
        sortBy = 'rating',
        sortOrder = 'desc'
      } = req.query;

      // Build query - include all suppliers regardless of isActive status
      // Frontend will handle showing open/closed status
      const query: any = {};

      // Filter by category
      if (category) {
        query.category = category;
      }

      // Search by name or product names
      if (search) {
        query.$text = { $search: search as string };
      }

      // Location-based filtering (if lat/lng provided)
      if (lat && lng) {
        const latitude = parseFloat(lat as string);
        const longitude = parseFloat(lng as string);
        const radiusInKm = parseFloat(radius as string);

        // Using MongoDB's geospatial query (approximate)
        query.lat = {
          $gte: latitude - (radiusInKm / 111), // 1 degree ≈ 111 km
          $lte: latitude + (radiusInKm / 111)
        };
        query.lng = {
          $gte: longitude - (radiusInKm / (111 * Math.cos(latitude * Math.PI / 180))),
          $lte: longitude + (radiusInKm / (111 * Math.cos(latitude * Math.PI / 180)))
        };
      }

      // Pagination
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      // Sorting
      const sort: any = {};
      sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

      const suppliers = await Supplier.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limitNum)
        .select('-__v');

      const total = await Supplier.countDocuments(query);

      res.json({
        success: true,
        data: suppliers,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      });
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch suppliers',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get supplier by ID
  static async getSupplierById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const supplier = await Supplier.findOne({ id })
        .select('-__v');

      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      res.json({
        success: true,
        data: supplier
      });
    } catch (error) {
      console.error('Error fetching supplier:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch supplier',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get suppliers by category
  static async getSuppliersByCategory(req: Request, res: Response) {
    try {
      const { category } = req.params;
      const { search, page = 1, limit = 20 } = req.query;

      const query: any = { category };

      if (search) {
        query.name = { $regex: search, $options: 'i' };
      }

      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      const suppliers = await Supplier.find(query)
        .sort({ rating: -1, name: 1 })
        .skip(skip)
        .limit(limitNum)
        .select('-__v');

      const total = await Supplier.countDocuments(query);

      res.json({
        success: true,
        data: suppliers,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      });
    } catch (error) {
      console.error('Error fetching suppliers by category:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch suppliers',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get supplier products
  static async getSupplierProducts(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { category, search, page = 1, limit = 20 } = req.query;

      const supplier = await Supplier.findOne({ id });

      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      let products = supplier.products.filter(p => p.isAvailable);

      // Filter by product category
      if (category && category !== 'All') {
        products = products.filter(p => p.category === category);
      }

      // Search in product names
      if (search) {
        const searchTerm = (search as string).toLowerCase();
        products = products.filter(p => 
          p.name.toLowerCase().includes(searchTerm)
        );
      }

      // Pagination
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;
      const paginatedProducts = products.slice(skip, skip + limitNum);

      // Get unique categories from products
      const categories = ['All', ...new Set(supplier.products.map(p => p.category))];

      res.json({
        success: true,
        data: {
          supplier: {
            id: supplier.id,
            name: supplier.name,
            rating: supplier.rating,
            ratings: supplier.ratings,
            deliveryTime: supplier.deliveryTime,
            openHours: supplier.openHours
          },
          products: paginatedProducts,
          categories,
          pagination: {
            page: pageNum,
            limit: limitNum,
            total: products.length,
            pages: Math.ceil(products.length / limitNum)
          }
        }
      });
    } catch (error) {
      console.error('Error fetching supplier products:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch supplier products',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get product by ID from supplier
  static async getProductById(req: Request, res: Response): Promise<void> {
    try {
      const { supplierId, productId } = req.params;

      const supplier = await Supplier.findOne({ id: supplierId });

      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      const product = supplier.products.find(p => p.id === productId && p.isAvailable);

      if (!product) {
        res.status(404).json({
          success: false,
          message: 'Product not found'
        });
        return;
      }

      res.json({
        success: true,
        data: {
          supplier: {
            id: supplier.id,
            name: supplier.name,
            rating: supplier.rating,
            ratings: supplier.ratings,
            deliveryTime: supplier.deliveryTime
          },
          product
        }
      });
    } catch (error) {
      console.error('Error fetching product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch product',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Link user to existing supplier
  static async linkToExistingSupplier(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { existingSupplierId, userEmail } = req.body;

      // If no user in request (no auth), find user by email
      let user = req.user;
      if (!user && userEmail) {
        const foundUser = await User.findOne({ email: userEmail, role: 'supplier' });
        user = foundUser || undefined;
      }

      if (!user || user.role !== 'supplier') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Supplier account required.'
        });
        return;
      }

      if (!existingSupplierId) {
        res.status(400).json({
          success: false,
          message: 'Existing supplier ID is required'
        });
        return;
      }

      // Check if the existing supplier profile exists
      const existingSupplier = await Supplier.findOne({ id: existingSupplierId });
      if (!existingSupplier) {
        res.status(404).json({
          success: false,
          message: 'Existing supplier profile not found'
        });
        return;
      }

      // Map supplier category to valid businessType enum
      let businessType = 'other';
      if (existingSupplier.category === 'restaurants') {
        businessType = 'restaurant';
      } else if (existingSupplier.category === 'clothings') {
        businessType = 'clothing';
      } else if (existingSupplier.category === 'grocery') {
        businessType = 'grocery';
      } else if (existingSupplier.category === 'pharmacy') {
        businessType = 'pharmacy';
      } else if (existingSupplier.category === 'electronics') {
        businessType = 'electronics';
      }

      // Update the user's supplier ID to link to existing supplier
      const updatedUser = await User.findByIdAndUpdate(
        user._id,
        {
          $set: {
            supplierId: existingSupplierId,
            storeName: existingSupplier.name,
            businessType: businessType
          }
        },
        { new: true }
      );

      if (!updatedUser) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      // Optional: Remove auto-generated supplier profile if it exists and is different
      if (user.supplierId && user.supplierId !== existingSupplierId) {
        try {
          await Supplier.deleteOne({ id: user.supplierId });
          console.log(`🗑️  Removed auto-generated supplier profile: ${user.supplierId}`);
        } catch (deleteError) {
          console.log(`⚠️  Could not remove auto-generated supplier profile: ${deleteError}`);
        }
      }

      res.json({
        success: true,
        message: 'Successfully linked to existing supplier profile',
        data: {
          user: {
            id: updatedUser._id,
            email: updatedUser.email,
            supplierId: updatedUser.supplierId,
            storeName: updatedUser.storeName
          },
          supplier: {
            id: existingSupplier.id,
            name: existingSupplier.name,
            category: existingSupplier.category,
            productsCount: existingSupplier.products?.length || 0,
            rating: existingSupplier.rating
          }
        }
      });
    } catch (error) {
      console.error('Error linking to existing supplier:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to link to existing supplier',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Add product to supplier
  static async addProduct(req: Request, res: Response): Promise<void> {
    try {
      const { supplierId } = req.params;
      const productData = req.body;

      const supplier = await Supplier.findOne({ id: supplierId });
      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      // Generate unique product ID
      const productId = `${supplierId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      const newProduct = {
        id: productId,
        name: productData.name,
        image: productData.image,
        price: productData.price,
        discountPrice: productData.discountPrice || 0,
        category: productData.category,
        description: productData.description || '',
        isAvailable: productData.isAvailable !== false,
        restaurantOptions: productData.restaurantOptions || {},
        clothingOptions: productData.clothingOptions || {}
      };

      supplier.products.push(newProduct);
      await supplier.save();

      res.status(201).json({
        success: true,
        message: 'Product added successfully',
        data: newProduct
      });
    } catch (error) {
      console.error('Error adding product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to add product',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Update product
  static async updateProduct(req: Request, res: Response): Promise<void> {
    try {
      const { supplierId, productId } = req.params;
      const updateData = req.body;

      const supplier = await Supplier.findOne({ id: supplierId });
      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      const productIndex = supplier.products.findIndex(p => p.id === productId);
      if (productIndex === -1) {
        res.status(404).json({
          success: false,
          message: 'Product not found'
        });
        return;
      }

      // Update product fields
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined) {
          (supplier.products[productIndex] as any)[key] = updateData[key];
        }
      });

      await supplier.save();

      res.json({
        success: true,
        message: 'Product updated successfully',
        data: supplier.products[productIndex]
      });
    } catch (error) {
      console.error('Error updating product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update product',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Delete product
  static async deleteProduct(req: Request, res: Response): Promise<void> {
    try {
      const { supplierId, productId } = req.params;

      const supplier = await Supplier.findOne({ id: supplierId });
      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      const productIndex = supplier.products.findIndex(p => p.id === productId);
      if (productIndex === -1) {
        res.status(404).json({
          success: false,
          message: 'Product not found'
        });
        return;
      }

      supplier.products.splice(productIndex, 1);
      await supplier.save();

      res.json({
        success: true,
        message: 'Product deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete product',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Toggle product availability
  static async toggleProductAvailability(req: Request, res: Response): Promise<void> {
    try {
      const { supplierId, productId } = req.params;

      const supplier = await Supplier.findOne({ id: supplierId });
      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      const product = supplier.products.find(p => p.id === productId);
      if (!product) {
        res.status(404).json({
          success: false,
          message: 'Product not found'
        });
        return;
      }

      product.isAvailable = !product.isAvailable;
      await supplier.save();

      res.json({
        success: true,
        message: `Product ${product.isAvailable ? 'enabled' : 'disabled'} successfully`,
        data: product
      });
    } catch (error) {
      console.error('Error toggling product availability:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to toggle product availability',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get supplier profile (for authenticated supplier)
  static async getSupplierProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const user = req.user;

      if (!user || user.role !== 'supplier') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Supplier account required.'
        });
        return;
      }

      if (!user.supplierId) {
        res.status(404).json({
          success: false,
          message: 'Supplier profile not found'
        });
        return;
      }

      const supplier = await Supplier.findOne({ id: user.supplierId });
      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      res.json({
        success: true,
        data: {
          user: {
            id: user._id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            phoneNumber: user.phoneNumber,
            storeName: user.storeName,
            businessType: user.businessType,
            address: user.address,
            city: user.city,
            country: user.country
          },
          supplier: {
            id: supplier.id,
            name: supplier.name,
            description: supplier.description,
            category: supplier.category,
            rating: supplier.rating,
            ratings: supplier.ratings,
            openHours: supplier.openHours,
            deliveryTime: supplier.deliveryTime,
            phone: supplier.phone,
            address: supplier.address,
            lat: supplier.lat,
            lng: supplier.lng,
            logoUrl: supplier.logoUrl,
            banner: supplier.banner,
            isActive: supplier.isActive,
            productsCount: supplier.products?.length || 0
          }
        }
      });
    } catch (error) {
      console.error('Error getting supplier profile:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get supplier profile',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Update supplier profile
  static async updateSupplierProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      const updateData = req.body;

      if (!user || user.role !== 'supplier') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Supplier account required.'
        });
        return;
      }

      if (!user.supplierId) {
        res.status(404).json({
          success: false,
          message: 'Supplier profile not found'
        });
        return;
      }

      // Update user data
      const userUpdateData: any = {};
      if (updateData.firstName) userUpdateData.firstName = updateData.firstName;
      if (updateData.lastName) userUpdateData.lastName = updateData.lastName;
      if (updateData.phoneNumber) userUpdateData.phoneNumber = updateData.phoneNumber;
      if (updateData.storeName) userUpdateData.storeName = updateData.storeName;
      if (updateData.address) userUpdateData.address = updateData.address;
      if (updateData.city) userUpdateData.city = updateData.city;
      if (updateData.country) userUpdateData.country = updateData.country;

      if (Object.keys(userUpdateData).length > 0) {
        await User.findByIdAndUpdate(user._id, { $set: userUpdateData });
      }

      // Update supplier data
      const supplierUpdateData: any = {};
      if (updateData.storeName) supplierUpdateData.name = updateData.storeName;
      if (updateData.description) supplierUpdateData.description = updateData.description;
      if (updateData.openHours) supplierUpdateData.openHours = updateData.openHours;
      if (updateData.deliveryTime) supplierUpdateData.deliveryTime = updateData.deliveryTime;
      if (updateData.phoneNumber) supplierUpdateData.phone = updateData.phoneNumber;
      if (updateData.address || updateData.city || updateData.country) {
        supplierUpdateData.address = `${updateData.address || ''}, ${updateData.city || ''}, ${updateData.country || ''}`.trim();
      }
      if (updateData.lat && updateData.lng) {
        supplierUpdateData.lat = updateData.lat;
        supplierUpdateData.lng = updateData.lng;
      }
      if (updateData.logoUrl) supplierUpdateData.logoUrl = updateData.logoUrl;
      if (updateData.banner) supplierUpdateData.banner = updateData.banner;
      if (updateData.isActive !== undefined) supplierUpdateData.isActive = updateData.isActive;

      const updatedSupplier = await Supplier.findOneAndUpdate(
        { id: user.supplierId },
        { $set: supplierUpdateData },
        { new: true }
      );

      if (!updatedSupplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier profile not found'
        });
        return;
      }

      // Get updated user data
      const updatedUser = await User.findById(user._id);

      res.json({
        success: true,
        message: 'Supplier profile updated successfully',
        data: {
          user: {
            id: updatedUser?._id,
            firstName: updatedUser?.firstName,
            lastName: updatedUser?.lastName,
            email: updatedUser?.email,
            phoneNumber: updatedUser?.phoneNumber,
            storeName: updatedUser?.storeName,
            businessType: updatedUser?.businessType,
            address: updatedUser?.address,
            city: updatedUser?.city,
            country: updatedUser?.country
          },
          supplier: {
            id: updatedSupplier.id,
            name: updatedSupplier.name,
            description: updatedSupplier.description,
            category: updatedSupplier.category,
            rating: updatedSupplier.rating,
            ratings: updatedSupplier.ratings,
            openHours: updatedSupplier.openHours,
            deliveryTime: updatedSupplier.deliveryTime,
            phone: updatedSupplier.phone,
            address: updatedSupplier.address,
            lat: updatedSupplier.lat,
            lng: updatedSupplier.lng,
            logoUrl: updatedSupplier.logoUrl,
            banner: updatedSupplier.banner,
            isActive: updatedSupplier.isActive,
            productsCount: updatedSupplier.products?.length || 0
          }
        }
      });
    } catch (error) {
      console.error('Error updating supplier profile:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update supplier profile',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }
}
