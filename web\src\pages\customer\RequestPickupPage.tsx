import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import { getPackageTypes, requestPickup } from '../../services/api';
import { useRequestPickupStore } from '../../stores/useRequestPickupStore';
import {
  MapPin,
  User,
  Package,
  Phone,
  FileText,
  Send,
  ArrowRight,
  Truck,
  Clock,
  DollarSign,
  Shield,
  Star,
  CheckCircle,
  AlertCircle,
  Navigation,
  Zap,
  Gift,
  Smartphone,
  Book,
  ShoppingBag,
  Heart,
  Coffee,
  Camera,
  Headphones,
  Monitor,
  Search,
  ChevronDown,
  ChevronRight,
  Map,
  Route,
  Timer,
  CreditCard,
  X,
  Target,
  ArrowLeft
} from 'lucide-react';
import Logo from '../../components/common/Logo';
import {
  formatNumberToHalfStep,
  formatCurrency,
  validateNumericInput,
  validateWeight,
  validatePackageValue
} from '../../utils/numericInputUtils';
import PhoneInput from '../../components/common/PhoneInput';

interface Location {
  lat: number;
  lng: number;
  address: string;
  street: string;
  city: string;
  buildingNumber?: string;
  apartmentNumber?: string;
  landmarks?: string;
}

interface PackageTypeOption {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  estimatedTime: string;
  basePrice: number;
}

interface FormData {
  // Sender Information
  senderName: string;
  senderPhone: string;

  // Receiver Information
  receiverName: string;
  receiverPhone: string;

  // Pickup Location
  pickup: Location | null;

  // Delivery Location
  delivery: Location | null;

  // Package Details
  packageType: string;
  packageDescription: string;
  packageSize: 'small' | 'medium' | 'large';
  packageWeight: string;
  packageValue: number;
  isFragile: boolean;

  // Service Options
  urgency: 'standard' | 'express' | 'urgent';
  paymentMethod: 'cash' | 'card' | 'wallet';
  scheduledPickupTime: Date | null;

  // Additional
  notes: string;
  insurance: boolean;
}



const RequestPickupPage: React.FC = () => {
  // Add custom styles for datetime-local input
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      input[type="datetime-local"]::-webkit-calendar-picker-indicator {
        filter: invert(1) brightness(0) contrast(100);
        cursor: pointer;
      }
      input[type="datetime-local"]::-webkit-datetime-edit {
        color: white;
      }
      input[type="datetime-local"]::-webkit-datetime-edit-fields-wrapper {
        color: white;
      }
      input[type="datetime-local"]::-webkit-datetime-edit-text {
        color: white;
      }
      input[type="datetime-local"]::-webkit-datetime-edit-month-field,
      input[type="datetime-local"]::-webkit-datetime-edit-day-field,
      input[type="datetime-local"]::-webkit-datetime-edit-year-field,
      input[type="datetime-local"]::-webkit-datetime-edit-hour-field,
      input[type="datetime-local"]::-webkit-datetime-edit-minute-field {
        color: white;
      }
    `;
    document.head.appendChild(style);
    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);
  const navigate = useNavigate();
  const location = useLocation();
  const { addPackage, setCurrentPackage } = useRequestPickupStore();
  const [formData, setFormData] = useState<FormData>({
    // Sender Information
    senderName: '',
    senderPhone: '',

    // Receiver Information
    receiverName: '',
    receiverPhone: '',

    // Pickup Location
    pickup: null,

    // Delivery Location
    delivery: null,

    // Package Details
    packageType: '',
    packageDescription: '',
    packageSize: 'medium',
    packageWeight: '',
    packageValue: 0,
    isFragile: false,

    // Service Options
    urgency: 'standard',
    paymentMethod: 'cash',
    scheduledPickupTime: new Date(Date.now() + 60 * 60 * 1000), // Default to 1 hour from now

    // Additional
    notes: '',
    insurance: false
  });

  // Fetch package types from backend
  useEffect(() => {
    const fetchPackageTypes = async () => {
      try {
        setLoading(true);
        setError(null);
        const types = await getPackageTypes();
        const typesWithIcons = types.map(type => ({
          ...type,
          icon: getPackageIcon(type.icon)
        }));
        setPackageTypes(typesWithIcons);
      } catch (err) {
        console.error('Error fetching package types:', err);
        setError('Failed to load package types');
        // Fallback to default types
        setPackageTypes([
          {
            id: 'general',
            name: 'General Items',
            icon: getPackageIcon('Package'),
            description: 'Other items and packages',
            estimatedTime: '3-5 hours',
            basePrice: 18
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchPackageTypes();
  }, []);

  // Debug: Log when component mounts
  useEffect(() => {
    // First, restore form data from sessionStorage if available
    const savedFormData = sessionStorage.getItem('requestPickupFormData');
    if (savedFormData) {
      try {
        const parsedFormData = JSON.parse(savedFormData);
        setFormData(parsedFormData);
        // Clear the saved data after restoring
        sessionStorage.removeItem('requestPickupFormData');
      } catch (error) {
        console.error('Error parsing saved form data:', error);
        sessionStorage.removeItem('requestPickupFormData');
      }
    }

    // Check if we have location data from navigation state
    if (location.state && location.state.locationData) {
      const locationData = location.state.locationData;

      if (locationData.type === 'req-pickup' || locationData.type === 'pickup') {
        const addressParts = parseAddress(locationData.address);
        updateField('pickup', {
          lat: locationData.lat,
          lng: locationData.lng,
          address: locationData.address,
          street: addressParts.street,
          city: addressParts.city
        });
      }

      // Clear the navigation state to prevent reprocessing
      window.history.replaceState({}, document.title);
    }

    return () => {
    };
  }, []);

  // Debug: Log when formData changes
  useEffect(() => {
  }, [formData]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [showPackageTypes, setShowPackageTypes] = useState(false);
  const [estimatedCost, setEstimatedCost] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState('');
  const [costBreakdown, setCostBreakdown] = useState<{
    baseCost: number;
    sizeMultiplier: number;
    fragileFee: number;
    insuranceFee: number;
    total: number;
  }>({
    baseCost: 0,
    sizeMultiplier: 1,
    fragileFee: 0,
    insuranceFee: 0,
    total: 0
  });
  const [packageTypes, setPackageTypes] = useState<PackageTypeOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Icon mapping for package types
  const getPackageIcon = (iconName: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      'FileText': <FileText size={24} />,
      'Smartphone': <Smartphone size={24} />,
      'ShoppingBag': <ShoppingBag size={24} />,
      'Coffee': <Coffee size={24} />,
      'Gift': <Gift size={24} />,
      'Package': <Package size={24} />
    };
    return iconMap[iconName] || <Package size={24} />;
  };

  // Package size options
  const packageSizes = [
    { id: 'small', name: 'Small', description: 'Up to 2kg, fits in envelope', multiplier: 1, color: 'from-blue-500 to-blue-600', icon: Package, estimatedTime: '1-2 hours' },
    { id: 'medium', name: 'Medium', description: 'Up to 10kg, shoebox size', multiplier: 1.2, color: 'from-green-500 to-green-600', icon: Package, estimatedTime: '2-3 hours' },
    { id: 'large', name: 'Large', description: 'Up to 25kg, large box', multiplier: 1.5, color: 'from-orange-500 to-orange-600', icon: Package, estimatedTime: '3-4 hours' }
  ];

  // Payment method options
  const paymentMethods = [
    { id: 'cash', name: 'Cash on Delivery', icon: <DollarSign size={20} />, description: 'Pay when package is delivered' },
    { id: 'card', name: 'Credit Card', icon: <CreditCard size={20} />, description: 'Pay now with card' },
    { id: 'wallet', name: 'Digital Wallet', icon: <Smartphone size={20} />, description: 'Pay with mobile wallet' }
  ];

  // Handle scroll for header animation
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsHeaderCompact(currentScrollY > 200);
      }, 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Listen for location selection from SelectLocationPage
  useEffect(() => {
    const handleLocationUpdate = (event: CustomEvent) => {
      const locationData = event.detail;

      if (locationData && locationData.lat && locationData.lng && locationData.address) {
        const addressParts = parseAddress(locationData.address);
        const location: Location = {
          lat: locationData.lat,
          lng: locationData.lng,
          address: locationData.address,
          street: addressParts.street,
          city: addressParts.city
        };

        if (locationData.type === 'req-pickup') {
          updateField('pickup', location);
          // Clear the request pickup location from localStorage after successful update
          localStorage.removeItem('selectedRequestPickupLocation');
        } else if (locationData.type === 'req-delivery') {
          updateField('delivery', location);
          // Clear the request delivery location from localStorage after successful update
          localStorage.removeItem('selectedRequestDeliveryLocation');
        } else if (locationData.type === 'pickup') {
          updateField('pickup', location);
          // Clear only the pickup location from localStorage after successful update
          localStorage.removeItem('selectedPickupLocation');
        }

        // Also remove old key for backward compatibility
          localStorage.removeItem('selectedDeliveryLocation');
      }
    };

    window.addEventListener('locationSelected', handleLocationUpdate as EventListener);

    return () => {
      window.removeEventListener('locationSelected', handleLocationUpdate as EventListener);
    };
  }, []); // Remove dependencies to prevent re-running when locations change

  // Separate effect to check localStorage on component mount only
  useEffect(() => {
    const checkStoredLocation = () => {
      // Check for request pickup location first (most relevant for this page)
      const storedRequestPickup = localStorage.getItem('selectedRequestPickupLocation');
      if (storedRequestPickup) {
        try {
          const locationData = JSON.parse(storedRequestPickup);
          if (locationData && locationData.lat && locationData.lng && locationData.address) {
            const addressParts = parseAddress(locationData.address);
            const location: Location = {
              lat: locationData.lat,
              lng: locationData.lng,
              address: locationData.address,
              street: addressParts.street,
              city: addressParts.city
            };
            updateField('pickup', location);
            localStorage.removeItem('selectedRequestPickupLocation');
          }
        } catch (error) {
          console.error('Error parsing stored request pickup location:', error);
          localStorage.removeItem('selectedRequestPickupLocation');
        }
      }

      // Check for request delivery location
      const storedRequestDelivery = localStorage.getItem('selectedRequestDeliveryLocation');
      if (storedRequestDelivery) {
        try {
          const locationData = JSON.parse(storedRequestDelivery);
          if (locationData && locationData.lat && locationData.lng && locationData.address) {
            const addressParts = parseAddress(locationData.address);
            const location: Location = {
              lat: locationData.lat,
              lng: locationData.lng,
              address: locationData.address,
              street: addressParts.street,
              city: addressParts.city
            };
            updateField('delivery', location);
            localStorage.removeItem('selectedRequestDeliveryLocation');
          }
        } catch (error) {
          console.error('Error parsing stored request delivery location:', error);
          localStorage.removeItem('selectedRequestDeliveryLocation');
        }
      }

      // Check for regular pickup location (fallback)
      const storedPickup = localStorage.getItem('selectedPickupLocation');
      if (storedPickup) {
        try {
          const locationData = JSON.parse(storedPickup);
          if (locationData && locationData.lat && locationData.lng && locationData.address) {
            const addressParts = parseAddress(locationData.address);
            const location: Location = {
              lat: locationData.lat,
              lng: locationData.lng,
              address: locationData.address,
              street: addressParts.street,
              city: addressParts.city
            };
            updateField('pickup', location);
            localStorage.removeItem('selectedPickupLocation');
          }
        } catch (error) {
          console.error('Error parsing stored pickup location:', error);
          localStorage.removeItem('selectedPickupLocation');
        }
      }

      // Also check old key for backward compatibility
      const storedLocation = localStorage.getItem('selectedDeliveryLocation');
      if (storedLocation) {
        try {
          const locationData = JSON.parse(storedLocation);
          if (locationData && locationData.lat && locationData.lng && locationData.address) {
            const addressParts = parseAddress(locationData.address);
            const location: Location = {
              lat: locationData.lat,
              lng: locationData.lng,
              address: locationData.address,
              street: addressParts.street,
              city: addressParts.city
            };
            // Determine type based on existing data or default to pickup
            if (locationData.type === 'pickup' || !formData.pickup) {
              updateField('pickup', location);
            }
            localStorage.removeItem('selectedDeliveryLocation');
          }
        } catch (error) {
          console.error('Error parsing stored location:', error);
          localStorage.removeItem('selectedDeliveryLocation');
        }
      }
    };

    checkStoredLocation();
  }, []); // Run only on component mount

  // Calculate estimated cost and time locally using backend data
  useEffect(() => {
    if (formData.pickup && formData.packageType && packageTypes.length > 0) {
      const selectedType = packageTypes.find(type => type.id === formData.packageType);
      const selectedSize = packageSizes.find(size => size.id === formData.packageSize);
      
      if (selectedType && selectedSize) {
        // Calculate cost using the same logic as backend
        const baseCost = selectedType.basePrice;
        const sizeMultiplier = selectedSize.multiplier;
        const fragileFee = formData.isFragile ? 3 : 0;
        const insuranceFee = formData.packageValue && formData.packageValue > 0 
          ? Math.max(5, Number(formData.packageValue) * 0.02) 
          : 0;
        
        // Calculate total cost and round to nearest 0.5
        let totalCost = baseCost * sizeMultiplier + fragileFee + insuranceFee;
        totalCost = formatNumberToHalfStep(totalCost);
        
        // Security: Validate cost is reasonable (same as backend)
        const minCost = 5;
        const maxCost = 1000;
        if (totalCost < minCost) totalCost = minCost;
        if (totalCost > maxCost) totalCost = maxCost;
        
        setEstimatedCost(totalCost);
        setEstimatedTime(selectedType.estimatedTime);
        
        // Set cost breakdown with rounded values
        setCostBreakdown({
          baseCost: formatNumberToHalfStep(baseCost),
          sizeMultiplier,
          fragileFee: formatNumberToHalfStep(fragileFee),
          insuranceFee: formatNumberToHalfStep(insuranceFee),
          total: totalCost
        });
      }
    }
  }, [formData.pickup, formData.packageType, formData.packageSize, formData.packageValue, formData.isFragile, packageTypes]);

  // Helper function to parse address into components
  const parseAddress = (address: string): { street: string; city: string } => {
    const parts = address.split(',').map(part => part.trim());
    if (parts.length >= 2) {
      return {
        street: parts.slice(0, -1).join(', '),
        city: parts[parts.length - 1]
      };
    }
    return {
      street: address,
      city: 'Nablus' // Default city
    };
  };

  // Enhanced field update with validation
  const updateField = (field: keyof FormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      return newData;
    });
  };

  // Enhanced numeric field handlers using shared utilities
  const handleNumericInput = (field: keyof FormData, value: string, validationFn?: (value: string) => string) => {
    const processedValue = validationFn ? validationFn(value) : validateNumericInput(value);
    updateField(field, processedValue);
  };

  const handleWeightInput = (value: string) => {
    const validated = validateWeight(value);
    updateField('packageWeight', validated);
  };

  const handleValueInput = (value: string) => {
    const numValue = validatePackageValue(value);
    updateField('packageValue', numValue);
  };

  const isFormValid = () => {
    return formData.senderName.trim() &&
           formData.senderPhone.trim() &&
           formData.receiverName.trim() &&
           formData.receiverPhone.trim() &&
           formData.pickup &&
           formData.delivery &&
           formData.packageType.trim() &&
           formData.packageDescription.trim() &&
           formData.packageWeight.trim() &&
           (formData.scheduledPickupTime && formData.scheduledPickupTime instanceof Date);
  };

  const getFormProgress = () => {
    const fields = [
      formData.senderName.trim(),
      formData.senderPhone.trim(),
      formData.receiverName.trim(),
      formData.receiverPhone.trim(),
      formData.pickup,
      formData.delivery,
      formData.packageType.trim(),
      formData.packageDescription.trim(),
      formData.packageWeight.trim(),
      (formData.scheduledPickupTime && formData.scheduledPickupTime instanceof Date) // Include scheduled pickup time in progress calculation
    ];
    const completedFields = fields.filter(Boolean).length;
    return (completedFields / fields.length) * 100;
  };

  const handleLocationSelect = (type: 'req-pickup' | 'req-delivery') => {
    // Store current form data in sessionStorage before navigation
    sessionStorage.setItem('requestPickupFormData', JSON.stringify(formData));
    navigate(`/customer/select-location?type=${type}`);
  };

    const handleSubmit = async () => {
    if (!isFormValid()) {
      setError('Please fill in all required fields');
      return;
    }

            // Additional validation
      if (!formData.packageDescription || formData.packageDescription.length < 2) {
        setError('Package description must be at least 2 characters long');
        return;
      }

      if (!formData.packageWeight || parseFloat(formData.packageWeight.replace(/[^\d.]/g, '')) < 0.1) {
        setError('Package weight must be at least 0.1 kg');
        return;
      }

    // Validate pickup time
    if (formData.scheduledPickupTime && formData.scheduledPickupTime instanceof Date) {
      const now = new Date();
      const minTime = new Date(now.getTime() + 30 * 60 * 1000); // 30 minutes from now
      const maxTime = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days from now
      
      if (formData.scheduledPickupTime < minTime) {
        setError('Pickup time must be at least 30 minutes in the future');
        return;
      }
      
      if (formData.scheduledPickupTime > maxTime) {
        setError('Pickup time cannot be more than 7 days in the future');
        return;
      }
                }

      setIsSubmitting(true);
      try {
        // Prepare pickup data for backend API
        const pickupData = {
          pickupAddress: {
            street: formData.pickup?.street || formData.pickup?.address || '',
            city: formData.pickup?.city || 'Nablus',
            coordinates: {
              lat: formData.pickup?.lat || 32.2211,
              lng: formData.pickup?.lng || 35.2544
            }
          },
          deliveryAddress: {
            street: formData.delivery?.street || formData.delivery?.address || '',
            city: formData.delivery?.city || 'Nablus',
            coordinates: {
              lat: formData.delivery?.lat || 32.2211,
              lng: formData.delivery?.lng || 35.2544
            }
          },
          senderName: formData.senderName,
          senderPhone: formData.senderPhone,
          recipientName: formData.receiverName,
          recipientPhone: formData.receiverPhone,
          packageDetails: {
            type: formData.packageType,
            description: formData.packageDescription,
            size: formData.packageSize,
            weight: parseFloat(formData.packageWeight.replace(/[^\d.]/g, '')) || 0.1, // Remove non-numeric characters and convert to number
            value: parseFloat(formData.packageValue.toString()) || 0, // Ensure value is a number
            fragile: Boolean(formData.isFragile) // Ensure fragile is a boolean
          },
          preferredTime: formData.scheduledPickupTime && formData.scheduledPickupTime instanceof Date ? formData.scheduledPickupTime.toISOString() : undefined,
          notes: formData.notes || ''
        };

        let response;
        try {
          // Create pickup request via backend API
          response = await requestPickup(pickupData);
        } catch (apiError) {
          console.error('❌ handleSubmit - API call failed:', apiError);
          throw apiError; // Re-throw to be caught by the outer try-catch
        }

      // Store the successful pickup request data in Zustand store
      if (response) {
        const pickupRequestData = {
          ...response,
          // Use packageId as the tracking number since backend doesn't have trackingNumber field
          trackingNumber: response.packageId || response._id,
          // Add any additional fields that might be needed
          status: response.status || 'pending',
          createdAt: response.createdAt || new Date().toISOString(),
          updatedAt: response.updatedAt || new Date().toISOString()
        };
        
        // Add to Zustand store
        addPackage(pickupRequestData);
        setCurrentPackage(pickupRequestData);
      }

      // Navigate to confirmation page with packageId as tracking number
      navigate('/customer/request-pickup-confirmation', { 
        state: {
          trackingNumber: response?.packageId || response?._id || 'Unknown',
          pickupRequest: response
        }
      });
    } catch (error) {
      console.error('Failed to submit pickup request:', error);
      let errorMessage = 'Failed to submit pickup request';

      if (error instanceof Error) {
        // Enhanced error handling with specific messages
        if (error.message.includes('Validation failed')) {
          errorMessage = 'Please check your form data and try again';
        } else if (error.message.includes('rate limit') || error.message.includes('Too many')) {
          errorMessage = 'Too many requests. Please wait before trying again';
        } else if (error.message.includes('not authenticated') || error.message.includes('401')) {
          errorMessage = 'Please log in again to continue';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again';
        } else if (error.message.includes('timeout')) {
          errorMessage = 'Request timed out. Please try again';
        } else {
          // Use the error message if it's user-friendly, otherwise use generic message
          errorMessage = error.message.length < 100 ? error.message : 'An unexpected error occurred';
        }
      }

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePackageTypeSelect = (typeId: string) => {
    updateField('packageType', typeId);
    setShowPackageTypes(false);
    setCurrentStep(4);
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-orange-500/30 to-red-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute top-1/3 right-0 w-80 h-80 bg-gradient-to-br from-purple-500/30 to-blue-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
          className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-green-500/20 to-teal-600/20 rounded-full blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [0, -100, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 5,
            }}
            className="absolute w-1 h-1 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>

      {/* Sticky Header with Scroll Animation */}
      <motion.div
        className="fixed left-0 right-0 transition-all duration-500"
        animate={{
          top: isHeaderCompact ? "0px" : "64px",
          zIndex: isHeaderCompact ? 55 : 35,
          backgroundColor: isHeaderCompact
            ? "rgba(15, 23, 42, 0.95)"
            : "rgba(15, 23, 42, 0)",
          backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
          borderBottom: isHeaderCompact
            ? "1px solid rgba(255, 255, 255, 0.1)"
            : "1px solid rgba(255, 255, 255, 0)",
        }}
        transition={{ duration: 0.3 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            animate={{
              paddingTop: isHeaderCompact ? "1rem" : "2rem",
              paddingBottom: isHeaderCompact ? "1rem" : "2rem",
            }}
            transition={{ duration: 0.3 }}
            className="flex items-center justify-between"
          >
            <div className="flex items-center gap-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate(-1)}
                className="p-3 bg-white/10 backdrop-blur-xl rounded-xl border border-white/20 hover:bg-white/20 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </motion.button>

              <motion.div
                animate={{
                  fontSize: isHeaderCompact ? "1.5rem" : "2rem",
                }}
                transition={{ duration: 0.3 }}
                className="font-bold text-white"
              >
                Request Pickup
              </motion.div>
            </div>

            {/* Compact Search when header is compact */}
            <AnimatePresence>
              {isHeaderCompact && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="flex-1 max-w-md mx-8"
                >
                  <div className="bg-white/10 backdrop-blur-xl rounded-xl border border-white/20">
                    <div className="flex items-center gap-3 p-3">
                      <Search size={18} className="text-white/60" />
                      <input
                        type="text"
                        placeholder="Search..."
                        className="flex-1 bg-transparent text-white placeholder-white/50 outline-none text-sm"
                      />
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </motion.div>

      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="relative z-10 pt-24 pb-24"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-8"
          >
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-white via-orange-100 to-red-100 bg-clip-text text-transparent mb-4">
              Request Pickup
            </h1>
            <p className="text-white/70 text-xl font-medium max-w-2xl mx-auto">
              Fast, reliable, and secure package pickup service at your doorstep
            </p>
          </motion.div>

          {/* Quick Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto mb-12"
          >
            {[
              { icon: Zap, label: 'Fast Pickup', value: '< 2 Hours' },
              { icon: Shield, label: 'Insured', value: 'Up to ₪500' },
              { icon: Star, label: 'Rating', value: '4.9/5' }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <stat.icon className="w-8 h-8 text-orange-400 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{stat.value}</div>
                <div className="text-white/60 text-sm">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen px-4 sm:px-6 lg:px-8 pb-20">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Main Form */}
            <div className="xl:col-span-2 space-y-6">
              {/* Pickup & Delivery Addresses */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl"
                  >
                    <MapPin className="w-6 h-6 text-white" />
                  </motion.div>
                  Pickup & Delivery Locations
                </h2>

                <div className="space-y-6">
                  {/* Pickup Location */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <label className="block text-sm font-semibold text-white/90 mb-3">
                      Pickup Location <span className="text-red-400">*</span>
                    </label>
                    <div className="flex gap-3">
                      <div className="flex-1 relative">
                        <input
                          type="text"
                          value={formData.pickup?.address || ''}
                          placeholder="Click 'Select on Map' to choose pickup location"
                          readOnly
                          className="w-full px-4 py-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent backdrop-blur-sm cursor-pointer"
                          onClick={() => handleLocationSelect('req-pickup')}
                        />
                        {formData.pickup && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="absolute top-3 right-3"
                          >
                            <CheckCircle className="text-orange-400" size={20} />
                          </motion.div>
                        )}
                      </div>
                      <motion.button
                        onClick={() => handleLocationSelect('req-pickup')}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-6 py-4 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold rounded-xl transition-all duration-200 flex items-center gap-2"
                      >
                        <Map size={18} />
                        Select on Map
                      </motion.button>
                    </div>
                    {formData.pickup && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 text-orange-400 text-sm flex items-center gap-2"
                      >
                        <MapPin size={14} />
                        <span>Pickup location confirmed</span>
                      </motion.div>
                    )}
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <label className="block text-sm font-semibold text-white/90 mb-3">
                      Delivery Location <span className="text-red-400">*</span>
                    </label>
                    <div className="flex gap-3">
                      <div className="flex-1 relative">
                        <input
                          type="text"
                          value={formData.delivery?.address || ''}
                          placeholder="Click 'Select on Map' to choose delivery location"
                          readOnly
                          className="w-full px-4 py-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent backdrop-blur-sm cursor-pointer"
                          onClick={() => handleLocationSelect('req-delivery')}
                        />
                        {formData.delivery && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="absolute top-3 right-3"
                          >
                            <CheckCircle className="text-green-400" size={20} />
                          </motion.div>
                        )}
                      </div>
                      <motion.button
                        onClick={() => handleLocationSelect('req-delivery')}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-6 py-4 bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600 text-white font-semibold rounded-xl transition-all duration-200 flex items-center gap-2"
                      >
                        <Map size={18} />
                        Select on Map
                      </motion.button>
                    </div>
                    {formData.delivery && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 text-green-400 text-sm flex items-center gap-2"
                      >
                        <MapPin size={14} />
                        <span>Delivery location confirmed</span>
                      </motion.div>
                    )}
                  </motion.div>


                </div>
              </motion.div>

              {/* Contact Information */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl"
                  >
                    <User className="w-6 h-6 text-white" />
                  </motion.div>
                  Contact Information
                </h2>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    className="space-y-4"
                  >
                    <h3 className="font-semibold text-white/90 text-lg">Sender Details</h3>
                    <div>
                      <label className="block text-sm font-semibold text-white/90 mb-3">
                        Sender Name *
                      </label>
                      <input
                        type="text"
                        value={formData.senderName}
                        onChange={(e) => updateField('senderName', e.target.value)}
                        placeholder="Full name"
                        className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                        required
                      />
                    </div>
                    <div>
                      <PhoneInput
                        value={formData.senderPhone}
                        onChange={(value) => updateField('senderPhone', value)}
                        label="Sender Phone"
                        placeholder="Enter sender phone number"
                        required
                      />
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    className="space-y-4"
                  >
                    <h3 className="font-semibold text-white/90 text-lg">Receiver Details</h3>
                    <div>
                      <label className="block text-sm font-semibold text-white/90 mb-3">
                        Receiver Name *
                      </label>
                      <input
                        type="text"
                        value={formData.receiverName}
                        onChange={(e) => updateField('receiverName', e.target.value)}
                        placeholder="Full name"
                        className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                        required
                      />
                    </div>
                    <div>
                      <PhoneInput
                        value={formData.receiverPhone}
                        onChange={(value) => updateField('receiverPhone', value)}
                        label="Receiver Phone"
                        placeholder="Enter receiver phone number"
                        required
                      />
                    </div>
                  </motion.div>
                </div>
              </motion.div>

              {/* Package Details */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
                  <motion.div
                    animate={{ rotateY: [0, 180, 360] }}
                    transition={{ duration: 3, repeat: Infinity }}
                    className="p-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl"
                  >
                    <Package className="w-6 h-6 text-white" />
                  </motion.div>
                  Package Details
                </h2>

                <div className="space-y-6">
                  <div className="space-y-6">
                    {/* Package Type Selection */}
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                    >
                      <label className="block text-sm font-semibold text-white/90 mb-4">
                        Package Type <span className="text-red-400">*</span>
                      </label>

                      {formData.packageType ? (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="mb-4"
                        >
                          {(() => {
                            const selectedType = packageTypes.find(type => type.id === formData.packageType);
                            return selectedType ? (
                              <div className="bg-gradient-to-r from-green-500/20 to-teal-500/20 border border-green-400/30 rounded-xl p-4">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-teal-500 rounded-xl flex items-center justify-center">
                                      {selectedType.icon}
                                    </div>
                                    <div>
                                      <div className="text-white font-semibold">{selectedType.name}</div>
                                      <div className="text-white/70 text-sm">{selectedType.description}</div>
                                    </div>
                                  </div>
                                  <motion.button
                                    onClick={() => setShowPackageTypes(true)}
                                    whileHover={{ scale: 1.05 }}
                                    className="text-white/80 hover:text-white text-sm font-medium"
                                  >
                                    Change
                                  </motion.button>
                                </div>
                              </div>
                            ) : null;
                          })()}
                        </motion.div>
                      ) : (
                        <motion.button
                          onClick={() => setShowPackageTypes(true)}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          className="w-full p-4 bg-white/10 border border-white/30 rounded-xl text-white/70 hover:text-white hover:border-white/50 transition-all duration-200 flex items-center justify-between"
                        >
                          <span>Select package type</span>
                          <ChevronDown size={20} />
                        </motion.button>
                      )}

                      {/* Package Types Grid */}
                      <AnimatePresence>
                        {showPackageTypes && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-3"
                          >
                            {packageTypes.map((type, index) => (
                              <motion.button
                                key={type.id}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.1 }}
                                onClick={() => handlePackageTypeSelect(type.id)}
                                whileHover={{ scale: 1.05, y: -2 }}
                                whileTap={{ scale: 0.95 }}
                                className="bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/40 rounded-xl p-4 transition-all duration-200 text-left"
                              >
                                <div className="flex flex-col items-center text-center">
                                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-500 rounded-xl flex items-center justify-center mb-3">
                                    {type.icon}
                                  </div>
                                  <div className="text-white font-semibold text-sm">{type.name}</div>
                                  <div className="text-white/60 text-xs mt-1">{type.description}</div>
                                  <div className="text-green-400 text-xs font-medium mt-1">
                                    ₪{type.basePrice} • {type.estimatedTime}
                                  </div>
                                </div>
                              </motion.button>
                            ))}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>

                    {/* Package Description */}
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.5 }}
                    >
                      <label className="block text-sm font-semibold text-white/90 mb-3">
                        Package Description *
                      </label>
                      <textarea
                        value={formData.packageDescription}
                        onChange={(e) => updateField('packageDescription', e.target.value)}
                        placeholder="Describe what's inside the package (e.g., documents, electronics, clothing, etc.)"
                        className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                        rows={3}
                        required
                      />
                    </motion.div>

                    {/* Package Weight */}
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.6 }}
                    >
                      <label className="block text-sm font-semibold text-white/90 mb-3">
                        Package Weight (kg) *
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          value={formData.packageWeight}
                          onChange={(e) => handleWeightInput(e.target.value)}
                          placeholder="e.g., 2.5 kg"
                          className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                          required
                        />
                        <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 text-sm">
                          kg
                        </div>
                      </div>
                      {formData.packageWeight && parseFloat(formData.packageWeight) < 0.1 && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-2 text-orange-400 text-sm flex items-center gap-2"
                        >
                          <AlertCircle size={14} />
                          Minimum weight is 0.1 kg
                        </motion.div>
                      )}
                    </motion.div>



                    {/* Fragile Handling - Enhanced */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.8 }}
                      className={`relative p-6 rounded-2xl border-2 transition-all duration-500 ${
                        formData.isFragile
                          ? 'bg-gradient-to-r from-orange-500/20 to-red-500/20 border-orange-400/50 shadow-lg shadow-orange-500/25'
                          : 'bg-white/5 border-white/20 hover:border-orange-400/30 hover:bg-orange-500/5'
                      }`}
                    >
                      {/* Animated background pattern when checked */}
                      {formData.isFragile && (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 0.1 }}
                          className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl"
                        />
                      )}
                      
                      <div className="relative z-10">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <motion.div
                              animate={formData.isFragile ? { 
                                scale: [1, 1.2, 1],
                                rotate: [0, 5, -5, 0]
                              } : {}}
                              transition={{ duration: 0.5, repeat: formData.isFragile ? Infinity : 0 }}
                              className={`p-3 rounded-xl ${
                                formData.isFragile 
                                  ? 'bg-gradient-to-r from-orange-500 to-red-500 shadow-lg shadow-orange-500/50' 
                                  : 'bg-white/10'
                              }`}
                            >
                              <Shield className="w-6 h-6 text-white" />
                            </motion.div>
                            <div>
                              <h3 className="text-lg font-bold text-white">Fragile Items</h3>
                              <p className="text-white/70 text-sm">Special handling required</p>
                            </div>
                          </div>
                          
                          <motion.div
                            animate={formData.isFragile ? { scale: [1, 1.1, 1] } : {}}
                            transition={{ duration: 0.3 }}
                            className="flex items-center gap-2"
                          >
                            <label className="relative cursor-pointer">
                              <input
                                type="checkbox"
                                checked={formData.isFragile}
                                onChange={(e) => updateField('isFragile', e.target.checked)}
                                className="sr-only"
                              />
                              <div className={`w-12 h-6 rounded-full transition-all duration-300 ${
                                formData.isFragile 
                                  ? 'bg-gradient-to-r from-orange-500 to-red-500' 
                                  : 'bg-white/20'
                              }`}>
                                <motion.div
                                  animate={{ x: formData.isFragile ? 24 : 0 }}
                                  className="w-6 h-6 bg-white rounded-full shadow-lg transform translate-x-0.5 translate-y-0.5"
                                />
                              </div>
                            </label>
                            {formData.isFragile && (
                              <motion.div
                                initial={{ opacity: 0, scale: 0 }}
                                animate={{ opacity: 1, scale: 1 }}
                                className="text-orange-400 font-semibold text-sm"
                              >
                                +₪3
                              </motion.div>
                            )}
                          </motion.div>
                        </div>
                        
                        <div className="space-y-3">
                          <div className="flex items-center gap-3 text-white/80 text-sm">
                            <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                            <span>Extra careful handling</span>
                          </div>
                          <div className="flex items-center gap-3 text-white/80 text-sm">
                            <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                            <span>Special packaging requirements</span>
                          </div>
                          <div className="flex items-center gap-3 text-white/80 text-sm">
                            <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                            <span>Priority loading and unloading</span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.6 }}
                  >
                    <label className="block text-sm font-semibold text-white/90 mb-4">
                      Package Size *
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {packageSizes.map((size, index) => (
                        <motion.label
                          key={size.id}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.4, delay: 0.7 + index * 0.1 }}
                          className={`relative p-6 border-2 rounded-2xl cursor-pointer transition-all duration-300 ${
                            formData.packageSize === size.id
                              ? 'border-green-400 bg-green-500/20 shadow-lg shadow-green-500/25'
                              : 'border-white/20 bg-white/5 hover:border-white/40 hover:bg-white/10'
                          }`}
                        >
                          <input
                            type="radio"
                            name="packageSize"
                            value={size.id}
                            checked={formData.packageSize === size.id}
                            onChange={(e) => updateField('packageSize', e.target.value)}
                            className="sr-only"
                          />
                          <div className="flex items-center gap-4">
                            <div className={`p-3 rounded-xl bg-gradient-to-r ${size.color}`}>
                              <size.icon className="w-6 h-6 text-white" />
                            </div>
                            <div className="flex-1">
                              <div className="font-semibold text-white text-lg">{size.name}</div>
                              <div className="text-white/60 text-sm">{size.description}</div>
                              <div className="text-white/80 text-sm mt-1">{size.estimatedTime}</div>
                            </div>
                            <div className="text-right">
                              <div className="text-2xl font-bold text-white">₪{size.multiplier * (packageTypes.find(type => type.id === formData.packageType)?.basePrice || 0)}</div>
                              {formData.packageSize === size.id && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="absolute top-3 right-3"
                                >
                                  <CheckCircle className="w-6 h-6 text-green-400" />
                                </motion.div>
                              )}
                            </div>
                          </div>
                        </motion.label>
                      ))}
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                  >
                    <label className="block text-sm font-semibold text-white/90 mb-3">
                      Package Value (₪)
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={formData.packageValue || ''}
                        onChange={(e) => handleValueInput(e.target.value)}
                        placeholder="Estimated value in ₪"
                        className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                      />
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 text-sm">
                        ₪
                      </div>
                    </div>
                  </motion.div>
                </div>
              </motion.div>

              {/* Timing & Instructions */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                    className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl"
                  >
                    <Clock className="w-6 h-6 text-white" />
                  </motion.div>
                  Timing & Special Instructions
                </h2>

                <div className="space-y-6">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    <label className="block text-sm font-semibold text-white/90 mb-4 flex items-center gap-2">
                      <Clock className="w-5 h-5" />
                      Preferred Pickup Time
                    </label>
                          <div className="relative">
                            <input
                              type="datetime-local"
                              min={new Date(Date.now() + 30 * 60 * 1000).toISOString().slice(0, 16)} // Minimum 30 minutes from now
                              max={new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16)} // Maximum 7 days from now
                              value={formData.scheduledPickupTime && formData.scheduledPickupTime instanceof Date ? 
                                (() => {
                                  const date = new Date(formData.scheduledPickupTime);
                                  const year = date.getFullYear();
                                  const month = String(date.getMonth() + 1).padStart(2, '0');
                                  const day = String(date.getDate()).padStart(2, '0');
                                  const hours = String(date.getHours()).padStart(2, '0');
                                  const minutes = String(date.getMinutes()).padStart(2, '0');
                                  return `${year}-${month}-${day}T${hours}:${minutes}`;
                                })() : 
                                (() => {
                                  const defaultDate = new Date(Date.now() + 60 * 60 * 1000);
                                  const year = defaultDate.getFullYear();
                                  const month = String(defaultDate.getMonth() + 1).padStart(2, '0');
                                  const day = String(defaultDate.getDate()).padStart(2, '0');
                                  const hours = String(defaultDate.getHours()).padStart(2, '0');
                                  const minutes = String(defaultDate.getMinutes()).padStart(2, '0');
                                  return `${year}-${month}-${day}T${hours}:${minutes}`;
                                })()
                              }
                              onChange={(e) => {
                                if (e.target.value) {
                                  const selectedDate = new Date(e.target.value);
                                  const now = new Date();
                                  const minTime = new Date(now.getTime() + 30 * 60 * 1000); // 30 minutes from now
                                  
                                  if (selectedDate < minTime) {
                                    // If selected date is too early, set it to 30 minutes from now
                                    updateField('scheduledPickupTime', minTime);
                                  } else {
                                    updateField('scheduledPickupTime', selectedDate);
                                  }
                                } else {
                                  updateField('scheduledPickupTime', null);
                                }
                              }}
                              className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 placeholder:text-white/50 [&::-webkit-calendar-picker-indicator]:filter [&::-webkit-calendar-picker-indicator]:invert [&::-webkit-calendar-picker-indicator]:brightness-0 [&::-webkit-calendar-picker-indicator]:contrast-100"
                              placeholder="Select pickup date and time"
                              required
                            />
                            {/* Help text */}
                            <div className="absolute -bottom-6 left-0 text-xs text-white/60">
                              Available: 30 min from now - 7 days ahead
                            </div>
                          </div>
                  </motion.div>

                  {/* Insurance Options - Enhanced */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.6 }}
                    className={`relative p-6 rounded-2xl border-2 transition-all duration-500 ${
                      formData.insurance
                        ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-400/50 shadow-lg shadow-blue-500/25'
                        : 'bg-white/5 border-white/20 hover:border-blue-400/30 hover:bg-blue-500/5'
                    }`}
                  >
                    {/* Animated background pattern when checked */}
                    {formData.insurance && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 0.1 }}
                        className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl"
                      />
                    )}
                    
                    <div className="relative z-10">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <motion.div
                            animate={formData.insurance ? { 
                              scale: [1, 1.2, 1],
                              rotate: [0, -5, 5, 0]
                            } : {}}
                            transition={{ duration: 0.5, repeat: formData.insurance ? Infinity : 0 }}
                            className={`p-3 rounded-xl ${
                              formData.insurance 
                                ? 'bg-gradient-to-r from-blue-500 to-purple-500 shadow-lg shadow-blue-500/50' 
                                : 'bg-white/10'
                            }`}
                          >
                            <Shield className="w-6 h-6 text-white" />
                          </motion.div>
                          <div>
                            <h3 className="text-lg font-bold text-white">Insurance Coverage</h3>
                            <p className="text-white/70 text-sm">Protect your valuable items</p>
                          </div>
                        </div>
                        
                        <motion.div
                          animate={formData.insurance ? { scale: [1, 1.1, 1] } : {}}
                          transition={{ duration: 0.3 }}
                          className="flex items-center gap-2"
                        >
                          <label className="relative cursor-pointer">
                            <input
                              type="checkbox"
                              checked={formData.insurance}
                              onChange={(e) => updateField('insurance', e.target.checked)}
                              className="sr-only"
                            />
                            <div className={`w-12 h-6 rounded-full transition-all duration-300 ${
                              formData.insurance 
                                ? 'bg-gradient-to-r from-blue-500 to-purple-500' 
                                : 'bg-white/20'
                            }`}>
                              <motion.div
                                animate={{ x: formData.insurance ? 24 : 0 }}
                                className="w-6 h-6 bg-white rounded-full shadow-lg transform translate-x-0.5 translate-y-0.5"
                              />
                            </div>
                          </label>
                          {formData.insurance && formData.packageValue > 0 && (
                            <motion.div
                              initial={{ opacity: 0, scale: 0 }}
                              animate={{ opacity: 1, scale: 1 }}
                              className="text-blue-400 font-semibold text-sm"
                            >
                              +₪{Math.max(5, Math.round(formData.packageValue * 0.02))}
                            </motion.div>
                          )}
                        </motion.div>
                      </div>
                      
                      <div className="space-y-3">
                        <div className="flex items-center gap-3 text-white/80 text-sm">
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                          <span>Full coverage up to package value</span>
                        </div>
                        <div className="flex items-center gap-3 text-white/80 text-sm">
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                          <span>2% of declared value (minimum ₪5)</span>
                        </div>
                        <div className="flex items-center gap-3 text-white/80 text-sm">
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                          <span>Peace of mind for valuable items</span>
                        </div>
                      </div>
                      
                      {formData.packageValue > 0 && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-4 p-3 bg-white/10 rounded-xl border border-white/20"
                        >
                          <div className="text-white/90 text-sm">
                            <span className="font-semibold">Coverage:</span> Up to ₪{formData.packageValue}
                          </div>
                          <div className="text-white/70 text-xs mt-1">
                            Premium: ₪{Math.max(5, Math.round(formData.packageValue * 0.02))}
                          </div>
                        </motion.div>
                      )}
                    </div>
                  </motion.div>

                  {/* Payment Method */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.65 }}
                  >
                    <label className="block text-sm font-semibold text-white/90 mb-3 flex items-center gap-2">
                      <CreditCard className="w-5 h-5" />
                      Payment Method
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      {paymentMethods.map((method) => (
                        <label
                          key={method.id}
                          className={`flex items-center gap-3 p-4 rounded-xl cursor-pointer transition-all duration-300 ${
                            formData.paymentMethod === method.id
                              ? 'bg-purple-500/30 border-2 border-purple-400'
                              : 'bg-white/5 border-2 border-white/10 hover:bg-white/10'
                          }`}
                        >
                          <input
                            type="radio"
                            name="paymentMethod"
                            value={method.id}
                            checked={formData.paymentMethod === method.id}
                            onChange={(e) => updateField('paymentMethod', e.target.value)}
                            className="sr-only"
                          />
                          <div className="text-purple-400">{method.icon}</div>
                          <div>
                            <div className="text-white font-medium">{method.name}</div>
                            <div className="text-white/60 text-sm">{method.description}</div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <label className="block text-sm font-semibold text-white/90 mb-3 flex items-center gap-2">
                      <FileText className="w-5 h-5" />
                      Special Instructions (Optional)
                    </label>
                    <textarea
                      value={formData.notes}
                      onChange={(e) => updateField('notes', e.target.value)}
                      placeholder="Any special handling instructions, fragile items, access codes, etc..."
                      className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                      rows={4}
                    />
                  </motion.div>
                </div>
              </motion.div>
            </div>

            {/* Enhanced Summary Sidebar */}
            <div className="xl:col-span-1">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl sticky top-8"
              >
                <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl"
                  >
                    <Truck className="w-6 h-6 text-white" />
                  </motion.div>
                  Pickup Summary
                </h3>

                <div className="space-y-4 mb-8">
                  {/* Cost Breakdown */}
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.6 }}
                    className="p-4 bg-white/5 rounded-xl"
                  >
                    <h4 className="text-white/90 font-semibold mb-3 flex items-center gap-2">
                      <span className="text-white/90 text-lg">₪</span>
                      Cost Breakdown
                    </h4>
                    {costBreakdown.baseCost > 0 ? (
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between items-center">
                          <span className="text-white/70">Base Cost</span>
                          <span className="text-white">{formatCurrency(costBreakdown.baseCost)}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-white/70">Size Multiplier (x{costBreakdown.sizeMultiplier})</span>
                          <span className="text-white">{formatCurrency(costBreakdown.baseCost * costBreakdown.sizeMultiplier - costBreakdown.baseCost)}</span>
                        </div>
                        {costBreakdown.fragileFee > 0 && (
                          <div className="flex justify-between items-center">
                            <span className="text-white/70">Fragile Handling</span>
                            <span className="text-orange-400">+{formatCurrency(costBreakdown.fragileFee)}</span>
                          </div>
                        )}
                        {costBreakdown.insuranceFee > 0 && (
                          <div className="flex justify-between items-center">
                            <span className="text-white/70">Insurance Fee</span>
                            <span className="text-blue-400">+{formatCurrency(costBreakdown.insuranceFee)}</span>
                          </div>
                        )}
                        <div className="h-px bg-white/20 my-2"></div>
                        <div className="flex justify-between items-center font-semibold">
                          <span className="text-white">Subtotal</span>
                          <span className="text-white">{formatCurrency(costBreakdown.baseCost * costBreakdown.sizeMultiplier + costBreakdown.fragileFee + costBreakdown.insuranceFee)}</span>
                        </div>
                      </div>
                    ) : (
                      <div className="text-white/50 text-sm text-center py-4">
                        Select package type to see cost breakdown
                      </div>
                    )}
                  </motion.div>

                  <div className="h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.7 }}
                    className="flex justify-between items-center p-4 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-xl border border-orange-400/30"
                  >
                    <span className="text-white font-semibold">Total</span>
                    <span className="text-3xl font-bold text-white">{formatCurrency(estimatedCost)}</span>
                  </motion.div>
                  
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                    className="text-xs text-white/50 text-center p-2 bg-white/5 rounded-lg"
                  >
                    <Shield className="w-3 h-3 inline mr-1" />
                    Cost calculated server-side for security
                  </motion.div>
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                  className="space-y-3 text-sm text-white/70 mb-8"
                >
                  {[
                    { icon: Zap, text: 'Free pickup within 2 hours' },
                    { icon: Truck, text: 'Real-time tracking included' },
                    { icon: Shield, text: 'Insurance up to ₪500' },
                    { icon: Star, text: '24/7 customer support' }
                  ].map((feature, index) => (
                    <motion.div
                      key={feature.text}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.9 + index * 0.1 }}
                      className="flex items-center gap-3 p-3 bg-white/5 rounded-lg"
                    >
                      <feature.icon className="w-4 h-4 text-orange-400" />
                      <span>{feature.text}</span>
                    </motion.div>
                  ))}
                </motion.div>

                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1.0 }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleSubmit}
                  disabled={!isFormValid()}
                  className={`w-full py-4 rounded-xl font-bold text-lg transition-all duration-300 ${
                    isFormValid()
                      ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white hover:from-orange-600 hover:to-red-600 shadow-lg shadow-orange-500/25'
                      : 'bg-white/10 text-white/50 cursor-not-allowed'
                  }`}
                >
                  {isFormValid() ? (
                    <span className="flex items-center justify-center gap-2">
                      <Truck className="w-5 h-5" />
                      Request Pickup - {formatCurrency(estimatedCost)}
                    </span>
                  ) : (
                    'Complete form to continue'
                  )}
                </motion.button>

                {/* Progress Indicator */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 1.1 }}
                  className="mt-6 p-4 bg-white/5 rounded-xl"
                >
                  <div className="text-white/60 text-xs mb-2">Form Completion</div>
                  <div className="w-full bg-white/10 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{
                        width: `${getFormProgress()}%`
                      }}
                      transition={{ duration: 0.5 }}
                      className="bg-gradient-to-r from-orange-500 to-red-500 h-2 rounded-full"
                    />
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestPickupPage;