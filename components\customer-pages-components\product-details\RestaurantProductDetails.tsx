import { useState, useRef } from 'react'
import { ScrollView, View, StyleSheet, Animated } from 'react-native'
import {
  YStack,
  Text,
  Paragraph,
  Button,
  Image
} from 'tamagui'
import { AdditionsChecklist } from './AdditionsCheckList';
import { CheckboxList } from './CheckboxList';
import { QtySelector } from './QtySelector';
import { Section } from './Section';
import { MotiView } from 'moti';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useCartStore } from '../CartStore';

type Addition = { id: string; name: string; price: number }

type RestaurantProductDetailsProps = {
    product: {
            id: string;
            name: string;
            image: string;
            price: number;
            category: string;
            restaurantOptions?: {
                additions?: Addition[]
                without?: string[]
                sides?: Addition[]
                /* options can be changed for each meal - resturant put sections it want */ 
            }
            clothingOptions?: {
                sizes: string[]
                colors: string[]
                gallery: string[]
            }
    };
    supplierId: string;
    supplierName: string;
}

const styles = StyleSheet.create({
  heroContainer: {
    position: 'relative',
    height: 250,
    borderRadius: 12,
    overflow: 'hidden',
  },
  heroImage: {
    height: '100%',
    width: '100%',
    borderRadius: 12,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.45)',
  },
  textOverlay: {
    position: 'absolute',
    bottom: 16,
    left: 16,
  },
});

export function RestaurantProductDetails({ product, supplierId, supplierName } : RestaurantProductDetailsProps) {
  const [qty, setQty] = useState(1);
  const [selectedAdditions, setSelAdditions] = useState<string[]>([]);
  const [without, setWithout] = useState<string[]>([]);
  const [selectedSides, setSelSides] = useState<string[]>([]);

  const base = product.price;
  const addPrice = selectedAdditions.reduce((s,id) => {
    const add = product.restaurantOptions?.additions?.find(a => a.id === id)
    return s + (add?.price ?? 0);
  }, 0);
  const sidePrice = selectedSides.reduce((s,id) => {
    const side = product.restaurantOptions?.sides?.find(a => a.id === id)
    return s + (side?.price ?? 0);
  }, 0);

  const finalPrice = (base + addPrice + sidePrice);
  const total = finalPrice * qty;

  const insets = useSafeAreaInsets();

  const scrollY = useRef(new Animated.Value(0)).current;

  const imageScale = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 1.4],
    extrapolate: 'clamp',
  });

  const imageOpacity = scrollY.interpolate({
    inputRange: [0, 120],
    outputRange: [1, 0.4],
    extrapolate: 'clamp',
  });

  const { addItem } = useCartStore();

  const [showSuccess, setShowSuccess] = useState(false);

  return (
    <>
      {showSuccess && (
      <MotiView
        from={{ opacity: 0, translateY: 20 }}
        animate={{ opacity: 1, translateY: 0 }}
        exit={{ opacity: 0, translateY: 20 }}
        transition={{ type: 'timing', duration: 300 }}
        style={{
          position: 'absolute',
          bottom: 100,
          alignSelf: 'center',
          backgroundColor: '#4caf50',
          paddingVertical: 10,
          paddingHorizontal: 20,
          borderRadius: 10,
          zIndex: 999,
          shadowColor: '#000',
          shadowOpacity: 0.2,
          shadowRadius: 10,
        }}
      >
        <Text color="white" fontWeight="600">
          Added to cart ✅
        </Text>
      </MotiView>
      )}
      <Animated.ScrollView
        style={{ flex: 1, width: '100%' }}
        contentContainerStyle={{ paddingBottom: 120, paddingTop: insets.top + 12 }}
        scrollEventThrottle={16}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: true }
        )}
      >
          <YStack gap="$4" pt="$4" px="$0" width="100%">
            <View style={styles.heroContainer}>
              <Animated.Image
                source={{ uri: product.image }}
                style={[
                  styles.heroImage,
                  {
                    transform: [{ scale: imageScale }],
                    opacity: imageOpacity,
                  },
                ]}
              />
              <View style={styles.overlay} />
              <View style={styles.textOverlay}>
                <Text fontSize="$7" fontWeight="bold" color="white">
                  {product.name}
                </Text>
                <Paragraph color="$gray4">₪{product.price.toFixed(2)}</Paragraph>
              </View>
            </View>

            {/* Additions */}
            {product.restaurantOptions?.additions?.length ? (
              <YStack px="$4">
                <Section title="Additions">
                  <AdditionsChecklist
                    items={product.restaurantOptions.additions}
                    selected={selectedAdditions}
                    onToggle={id =>
                      setSelAdditions(prev =>
                        prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id],
                      )
                    }
                  />
                </Section>
              </YStack>
            ) : null}

            {/* Without */}
            {product.restaurantOptions?.without?.length ? (
              <YStack px="$4">
                <Section title="Without">
                  <CheckboxList
                    items={product.restaurantOptions.without}
                    selected={without}
                    onToggle={id =>
                      setWithout(prev =>
                        prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id],
                      )
                    }
                  />
                </Section>
              </YStack>
            ) : null}

            {/* Sides */}
            {product.restaurantOptions?.sides?.length ? (
              <YStack px="$4">
                <MotiView
                  from={{ scale: 0.95, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ type: 'timing', duration: 250 }}
                >
                  <Section title="Side Orders">
                    <AdditionsChecklist
                      items={product.restaurantOptions.sides}
                      selected={selectedSides}
                      onToggle={id =>
                        setSelSides(prev =>
                          prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id],
                        )
                      }
                    />
                  </Section>
                </MotiView>
              </YStack>
            ) : null}

            {/* Qty & Add */}
            <YStack px="$4">
              <Text fontWeight="700" fontSize="$5">Quantity</Text>
              <QtySelector qty={qty} setQty={setQty} />
            </YStack>
            
          </YStack>
      </Animated.ScrollView>
      <YStack position="absolute" zIndex={2} bottom={0} left={0} right={0} px="$4" pb={40}>
            <Button
              bg="$primary"
              size="$5"
              onPress={() => {
                try {
                  const cartPayload = {
                    product,               // full product object
                    qty,                   // user-selected quantity
                    finalPrice: finalPrice,
                    supplierId: supplierId,
                    supplierName: supplierName,
                    supplierCategory: "restaurants",
                    selectedAdditions: product.restaurantOptions?.additions
                      ?.filter(a => selectedAdditions.includes(a.id)) ?? [],
                    selectedSides: product.restaurantOptions?.sides
                      ?.filter(s => selectedSides.includes(s.id)) ?? [],
                    without,               // string[] already picked
                  }

                  console.log('Adding to cart:', cartPayload);
                  addItem(cartPayload)
                  setShowSuccess(true)
                  setTimeout(() => setShowSuccess(false), 2000)
                } catch (error) {
                  console.error('Error adding to cart:', error);
                }
              }}
              br="$6"
              elevation="$3"
              hoverStyle={{ bg: "$third" }}
              pressStyle={{ bg: "$third" }}
            >
              <MotiView
                from={{ opacity: 0.7, scale: 0.97 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ type: 'timing', duration: 200 }}
              >
                <Text color="white" fontWeight="600">
                  Add {qty} • ₪{total.toFixed(2)}
                </Text>
              </MotiView>
            </Button>
        </YStack>
      </>
  )
}
