# Language System Implementation

This document describes the comprehensive language system implemented for the Wasel web application, providing Arabic and English translation support with RTL (Right-to-Left) layout support.

## Features

- 🌐 **Multi-language Support**: English and Arabic translations
- 🔄 **RTL Support**: Automatic Right-to-Left layout for Arabic
- 💾 **Persistent Storage**: Language preference saved in localStorage
- 🎯 **Device Detection**: Automatic language detection based on browser settings
- 🎨 **Multiple UI Variants**: Different language switcher components for various use cases
- ⚡ **Smooth Animations**: Framer Motion animations for enhanced UX

## Components

### 1. LanguageSwitcher

A versatile language switcher component with multiple variants:

#### Variants

- **`floating`**: Floating button in corner positions
- **`compact`**: Compact switcher with both languages visible
- **`dropdown`**: Dropdown with detailed language information
- **`button`**: Simple toggle button

#### Usage Examples

```tsx
// Floating variant for login pages
<LanguageSwitcher variant="floating" position="top-right" />

// Compact variant for headers
<LanguageSwitcher variant="compact" />

// Dropdown variant for settings
<LanguageSwitcher variant="dropdown" />

// Simple button variant
<LanguageSwitcher variant="button" />
```

### 2. LanguageIndicator

A simple indicator showing the current language:

```tsx
<LanguageIndicator size="md" showIcon={true} showText={true} />
```

### 3. LanguageSettings

A comprehensive settings component for language selection:

```tsx
// Card variant
<LanguageSettings variant="card" />

// List variant
<LanguageSettings variant="list" />

// Grid variant
<LanguageSettings variant="grid" />
```

## File Structure

```
web/src/
├── i18n/
│   ├── index.ts                 # i18n configuration
│   └── translations/
│       ├── en.json             # English translations
│       └── ar.json             # Arabic translations
├── stores/
│   └── languageStore.ts        # Zustand store for language state
├── components/common/
│   ├── LanguageSwitcher.tsx    # Main language switcher component
│   ├── LanguageIndicator.tsx   # Language indicator component
│   ├── LanguageSettings.tsx    # Language settings component
│   └── LanguageProvider.tsx    # Language provider wrapper
└── pages/demo/
    └── LanguageDemoPage.tsx    # Demo page showcasing all variants
```

## Translation Files

### English (en.json)
```json
{
  "common": {
    "welcome": "Welcome",
    "back": "Back",
    "next": "Next"
  },
  "auth": {
    "login": "Login",
    "signup": "Sign Up",
    "welcomeBack": "Welcome Back!"
  }
}
```

### Arabic (ar.json)
```json
{
  "common": {
    "welcome": "مرحباً",
    "back": "رجوع",
    "next": "التالي"
  },
  "auth": {
    "login": "تسجيل الدخول",
    "signup": "إنشاء حساب",
    "welcomeBack": "مرحباً بعودتك!"
  }
}
```

## Usage in Components

### Using Translations

```tsx
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('auth.welcomeBack')}</h1>
      <p>{t('auth.signInToContinue')}</p>
    </div>
  );
};
```

### Using Language Store

```tsx
import { useLanguageStore } from '../stores/languageStore';

const MyComponent = () => {
  const { currentLanguage, setLanguage, isRTL } = useLanguageStore();
  
  const handleLanguageChange = async () => {
    await setLanguage('ar'); // Switch to Arabic
  };
  
  return (
    <div>
      <p>Current Language: {currentLanguage}</p>
      <p>Is RTL: {isRTL ? 'Yes' : 'No'}</p>
    </div>
  );
};
```

## RTL Support

The system automatically handles RTL layout for Arabic:

- Sets `document.documentElement.dir = 'rtl'` for Arabic
- Sets `document.documentElement.lang = 'ar'` for Arabic
- CSS classes automatically adjust for RTL layout
- Custom CSS rules for RTL support in `index.css`
- **Input fields remain LTR**: Email, password, and other input fields maintain left-to-right direction for better UX, as users typically type in English characters

## CSS RTL Rules

```css
/* RTL Support */
[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

[dir="rtl"] .ml-auto {
  margin-left: unset;
  margin-right: auto;
}

/* Input fields should always be LTR for better UX */
[dir="rtl"] input[type="text"],
[dir="rtl"] input[type="email"],
[dir="rtl"] input[type="password"],
[dir="rtl"] input[type="number"],
[dir="rtl"] input[type="tel"],
[dir="rtl"] input[type="url"],
[dir="rtl"] input[type="search"],
[dir="rtl"] textarea {
  direction: ltr !important;
  text-align: left !important;
}

/* Placeholder text should also be LTR */
[dir="rtl"] input::placeholder,
[dir="rtl"] textarea::placeholder {
  direction: ltr !important;
  text-align: left !important;
}
```

## Demo Page

Visit `/demo/language` to see all language switcher variants in action.

## Installation & Setup

1. **Install Dependencies**:
   ```bash
   npm install i18next react-i18next i18next-browser-languagedetector
   ```

2. **Import i18n Configuration**:
   ```tsx
   import './i18n';
   ```

3. **Wrap App with LanguageProvider**:
   ```tsx
   import { LanguageProvider } from './components/common/LanguageProvider';

   function App() {
     return (
       <LanguageProvider>
         <AuthProvider>
           <AppRouter />
         </AuthProvider>
       </LanguageProvider>
     );
   }
   ```

## API Reference

### LanguageStore

```tsx
interface LanguageState {
  currentLanguage: Language;        // 'en' | 'ar'
  isRTL: boolean;                  // true for Arabic
  isLoading: boolean;              // loading state
  setLanguage: (language: Language) => Promise<void>;
  initializeLanguage: () => Promise<void>;
  toggleLanguage: () => Promise<void>;
}
```

### LanguageSwitcher Props

```tsx
interface LanguageSwitcherProps {
  variant?: 'button' | 'dropdown' | 'compact' | 'floating';
  className?: string;
  onLanguageChange?: () => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}
```

### LanguageIndicator Props

```tsx
interface LanguageIndicatorProps {
  showIcon?: boolean;
  showText?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}
```

### LanguageSettings Props

```tsx
interface LanguageSettingsProps {
  className?: string;
  showTitle?: boolean;
  variant?: 'card' | 'list' | 'grid';
}
```

## Best Practices

1. **Always use translation keys**: Use `t('key')` instead of hardcoded text
2. **Group translations logically**: Organize translations by feature (auth, common, etc.)
3. **Test RTL layout**: Always test Arabic layout to ensure proper RTL support
4. **Use appropriate variants**: Choose the right language switcher variant for each context
5. **Handle loading states**: The LanguageProvider shows a loading state during initialization

## Browser Support

- Modern browsers with ES6+ support
- localStorage for language persistence
- CSS Grid and Flexbox for layouts
- Framer Motion for animations

## Performance Considerations

- Translations are loaded on demand
- Language preference is cached in localStorage
- RTL layout changes are optimized
- Animations use hardware acceleration

## Troubleshooting

### Common Issues

1. **Translations not loading**: Ensure i18n configuration is imported
2. **RTL not working**: Check if `document.documentElement.dir` is set correctly
3. **Language not persisting**: Verify localStorage is available and working
4. **Animations not smooth**: Ensure Framer Motion is properly installed

### Debug Mode

Enable debug mode in development:

```tsx
// In i18n/index.ts
debug: process.env.NODE_ENV === 'development',
```

This will log language changes and initialization to the console. 