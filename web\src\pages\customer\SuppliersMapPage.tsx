import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup } from 'react-leaflet';
import L from 'leaflet';
import {
  MapPin,
  Star,
  Clock,
  Phone,
  Eye,
  Navigation,
  Filter,
  Search,
  X,
  Locate,
  ChevronDown,
  Layers,
  Zap,
  Loader,
  AlertCircle,
  Store,
  Package
} from 'lucide-react';
import Logo from '../../components/common/Logo';
import { apiService, type Supplier, comprehensiveSearch, type ComprehensiveSearchResults } from '../../services/api';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Create custom marker with supplier logo
const createCustomMarker = (supplier: Supplier) => {
  const categoryColor = getCategoryColor(supplier.category);
  const statusColor = supplier.isActive ? '#22c55e' : '#ef4444';

  return L.divIcon({
    html: `
      <div style="
        width: 50px;
        height: 60px;
        position: relative;
      ">
        <!-- Main marker circle -->
        <div style="
          width: 50px;
          height: 50px;
          background: ${categoryColor};
          border: 4px solid white;
          border-radius: 50%;
          box-shadow: 0 2px 10px rgba(0,0,0,0.3);
          position: relative;
        ">
          <!-- Perfectly centered image -->
          <img
            src="${supplier.logoUrl}"
            alt="${supplier.name}"
            style="
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 36px;
              height: 36px;
              object-fit: cover;
              border-radius: 50%;
              background: white;
              border: 2px solid white;
            "
            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
          />
          <!-- Fallback text (hidden by default, shown if image fails) -->
          <div style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 36px;
            height: 36px;
            background: white;
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            color: ${categoryColor};
            font-weight: bold;
            font-size: 18px;
            border: 2px solid white;
            z-index: -1;
          ">
            ${supplier.name.charAt(0).toUpperCase()}
          </div>
        </div>

        <!-- Pointer triangle -->
        <div style="
          position: absolute;
          bottom: 8px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 6px solid transparent;
          border-right: 6px solid transparent;
          border-top: 10px solid ${categoryColor};
          filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        "></div>

        <!-- Status indicator -->
        <div style="
          position: absolute;
          top: -2px;
          right: -2px;
          width: 16px;
          height: 16px;
          background: ${statusColor};
          border: 3px solid white;
          border-radius: 50%;
          box-shadow: 0 1px 4px rgba(0,0,0,0.3);
        "></div>
      </div>
    `,
    className: 'supplier-marker',
    iconSize: [50, 60],
    iconAnchor: [25, 50],
    popupAnchor: [0, -50]
  });
};

const getCategoryColor = (category: string) => {
  switch (category.toLowerCase()) {
    case 'restaurant': return '#f97316';
    case 'pharmacy': return '#22c55e';
    case 'electronics': return '#3b82f6';
    case 'grocery': return '#8b5cf6';
    case 'clothing': return '#ec4899';
    default: return '#6b7280';
  }
};

const getCategoryLabel = (category: string) => {
  switch (category.toLowerCase()) {
    case 'restaurant': return 'Restaurants';
    case 'pharmacy': return 'Pharmacies';
    case 'electronics': return 'Electronics';
    case 'grocery': return 'Grocery';
    case 'clothing': return 'Clothing';
    default: return category.charAt(0).toUpperCase() + category.slice(1);
  }
};

const categories = [
  { key: 'all', label: 'All Suppliers', color: 'bg-gray-100 text-gray-700' },
  { key: 'restaurant', label: 'Restaurants', color: 'bg-orange-100 text-orange-700' },
  { key: 'pharmacy', label: 'Pharmacies', color: 'bg-green-100 text-green-700' },
  { key: 'electronics', label: 'Electronics', color: 'bg-blue-100 text-blue-700' },
  { key: 'grocery', label: 'Grocery', color: 'bg-purple-100 text-purple-700' },
  { key: 'clothing', label: 'Clothing', color: 'bg-pink-100 text-pink-700' }
];

const SuppliersMapPage: React.FC = () => {
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [userLocation, setUserLocation] = useState<{lat: number, lng: number} | null>(null);
  const [mapCenter, setMapCenter] = useState<[number, number]>([32.2211, 35.2544]); // Default to Nablus
  const [isLocating, setIsLocating] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  
  // Backend integration states
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchLoading, setSearchLoading] = useState(false);

  // Enhanced search states
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [searchResults, setSearchResults] = useState<ComprehensiveSearchResults | null>(null);
  const [isSearching, setIsSearching] = useState(false);

  // Add marker styles - ATTRACTIVE HOVER WITHOUT MOVEMENT
  useEffect(() => {
    const styleId = 'attractive-marker-styles';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        .supplier-marker {
          cursor: pointer !important;
          transition: filter 0.3s ease !important;
        }

        .supplier-marker:hover {
          filter: brightness(1.1) saturate(1.2) drop-shadow(0 4px 15px rgba(0,0,0,0.4)) !important;
        }

        .supplier-marker:active {
          filter: brightness(0.95) saturate(1.1) drop-shadow(0 2px 8px rgba(0,0,0,0.5)) !important;
        }

        /* Subtle glow effect on hover for the main circle */
        .supplier-marker:hover > div > div:first-child {
          box-shadow:
            0 2px 10px rgba(0,0,0,0.3),
            0 0 20px rgba(255,255,255,0.3),
            inset 0 1px 0 rgba(255,255,255,0.4) !important;
        }

        /* Enhanced status indicator on hover */
        .supplier-marker:hover > div > div:last-child {
          box-shadow:
            0 1px 4px rgba(0,0,0,0.3),
            0 0 8px rgba(255,255,255,0.5) !important;
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  // Handle scroll for header animation with debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsHeaderCompact(currentScrollY > 200);
      }, 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Enhanced comprehensive search functionality
  useEffect(() => {
    const performSearch = async () => {
      if (!searchQuery.trim()) {
        setSearchResults(null);
        setShowSearchResults(false);
        setIsSearching(false);
        setSearchLoading(false);
        return;
      }

      setIsSearching(true);
      setSearchLoading(true);
      
      try {
        // Use comprehensive search for enhanced results
        const results = await comprehensiveSearch(searchQuery);
        setSearchResults(results);
        setShowSearchResults(true);
      } catch (error) {
        console.error('Search error:', error);
        // Fallback to basic supplier search
        try {
          const params: any = {
            search: searchQuery.trim(),
            page: 1,
            limit: 100
          };

          if (userLocation) {
            params.lat = userLocation.lat;
            params.lng = userLocation.lng;
            params.radius = 50;
          }

          const response = await apiService.getSuppliers(params);
          
          if (response.success && response.data) {
            const fallbackResults: ComprehensiveSearchResults = {
              services: [],
              categories: [],
              suppliers: response.data.map(supplier => ({
                type: 'supplier' as const,
                id: supplier._id,
                title: supplier.name,
                subtitle: getCategoryLabel(supplier.category),
                description: supplier.description,
                route: `/customer/supplier-details/${supplier._id}`,
                data: supplier
              })),
              products: [],
              total: response.data.length
            };
            setSearchResults(fallbackResults);
            setShowSearchResults(true);
          } else {
            setSearchResults(null);
            setShowSearchResults(false);
          }
        } catch (fallbackError) {
          console.error('Fallback search error:', fallbackError);
          setSearchResults(null);
          setShowSearchResults(false);
        }
      } finally {
        setIsSearching(false);
        setSearchLoading(false);
      }
    };

    const timeoutId = setTimeout(performSearch, 300);
    return () => clearTimeout(timeoutId);
  }, [searchQuery, userLocation]);

  // Load suppliers from backend (for map display)
  const loadSuppliers = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params: any = {
        page: 1,
        limit: 100, // Get more suppliers for map
        sortBy: 'rating',
        sortOrder: 'desc'
      };

      // Add category filter if not 'all'
      if (selectedCategory !== 'all') {
        params.category = selectedCategory;
      }

      // Add location-based filtering if user location is available
      if (userLocation) {
        params.lat = userLocation.lat;
        params.lng = userLocation.lng;
        params.radius = 50; // 50km radius
      }

      const response = await apiService.getSuppliers(params);
      
      if (response.success && response.data) {
        setSuppliers(response.data);
      } else {
        setError(response.message || 'Failed to load suppliers');
        setSuppliers([]);
      }
    } catch (err) {
      console.error('Error loading suppliers:', err);
      setError('Failed to load suppliers. Please try again.');
      setSuppliers([]);
    } finally {
      setLoading(false);
    }
  };

  // Load suppliers when component mounts or filters change
  useEffect(() => {
    if (!showSearchResults) {
      loadSuppliers();
    }
  }, [selectedCategory, userLocation, showSearchResults]);

  // Get user's current location
  const getCurrentLocation = async () => {
    setIsLocating(true);
    try {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const location = {
              lat: position.coords.latitude,
              lng: position.coords.longitude
            };
            setUserLocation(location);
            setMapCenter([location.lat, location.lng]);
            setIsLocating(false);
          },
          (error) => {
            console.error('Error getting location:', error);
            setError('Unable to get your location. Please check your browser permissions.');
            setIsLocating(false);
          }
        );
      } else {
        setError('Geolocation is not supported by your browser.');
        setIsLocating(false);
      }
    } catch (error) {
      console.error('Geolocation error:', error);
      setError('Failed to get your location.');
      setIsLocating(false);
    }
  };

  useEffect(() => {
    getCurrentLocation();
  }, []);

  // Clear search and return to map view
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults(null);
    setShowSearchResults(false);
    setIsSearching(false);
    setSearchLoading(false);
  };

  // Get suppliers to display on map (either search results or all suppliers)
  const getDisplaySuppliers = () => {
    // If we have search results and search is active, show search results
    if (showSearchResults && searchResults && searchQuery.trim()) {
      // Extract suppliers from search results
      return searchResults.suppliers.map(result => result.data as Supplier);
    }
    // Otherwise show all suppliers (when search is empty or no search results)
    return suppliers;
  };

  const displaySuppliers = getDisplaySuppliers();

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-green-500/30 to-blue-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-br from-yellow-500/20 to-orange-600/20 rounded-full blur-3xl"
          />

          {/* Floating particles */}
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -30, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Main Header Content - Static */}
        <div className="relative z-30 pt-16 pb-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="flex justify-center mb-6"
              >
                <Logo size="lg" />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <h1 className="text-4xl font-bold text-white mb-4">
                  Discover Suppliers
                </h1>
                <p className="text-white/80 text-lg max-w-lg mx-auto">
                  Find the best suppliers near your location
                </p>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Sticky Compact Header - Only appears when scrolling */}
        <motion.div
          className="fixed top-0 left-0 right-0 z-50"
          animate={{
            opacity: isHeaderCompact ? 1 : 0,
            y: isHeaderCompact ? 0 : -100,
            backgroundColor: isHeaderCompact
              ? "rgba(15, 23, 42, 0.95)"
              : "rgba(15, 23, 42, 0)",
            backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
            borderBottom: isHeaderCompact
              ? "1px solid rgba(255, 255, 255, 0.1)"
              : "1px solid rgba(255, 255, 255, 0)",
          }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          style={{ pointerEvents: isHeaderCompact ? 'auto' : 'none' }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center gap-2"
                  >
                    {/* Simplified logo for compact header */}
                    <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                      <Zap size={16} className="text-white" />
                    </div>
                    <span className="text-white font-bold text-lg">BolTalab</span>
                  </motion.div>
                  <div className="border-l border-white/20 pl-3">
                    <h2 className="text-white font-semibold text-sm">Suppliers Map</h2>
                    <p className="text-white/60 text-xs">Find suppliers near you</p>
                  </div>
                </div>
                <button
                  onClick={getCurrentLocation}
                  disabled={isLocating}
                  className="px-3 py-2 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-200 flex items-center gap-2 disabled:opacity-50 text-sm"
                >
                  {isLocating ? (
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  ) : (
                    <Locate size={16} />
                  )}
                  {isLocating ? 'Locating...' : 'My Location'}
                </button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Search and Filter Panel - Positioned above map */}
        <div className="relative z-40">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6"
          >
            <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-6">
              {/* Enhanced Search Bar */}
              <div className="mb-6">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    {searchLoading || isSearching ? (
                      <Loader size={20} className="text-gray-400 animate-spin" />
                    ) : (
                      <Search size={20} className="text-gray-400" />
                    )}
                  </div>
                  <input
                    type="text"
                    placeholder="Search suppliers, categories, products, or tags..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-12 py-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-lg"
                  />
                  {searchQuery && (
                    <button
                      onClick={clearSearch}
                      className="absolute inset-y-0 right-0 pr-4 flex items-center"
                    >
                      <X size={20} className="text-gray-400 hover:text-gray-600" />
                    </button>
                  )}
                </div>
              </div>

              {/* Enhanced Category Filter with horizontal scroll */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <Filter size={20} />
                    Categories
                  </h3>
                  <button
                    onClick={getCurrentLocation}
                    disabled={isLocating}
                    className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-colors duration-200 flex items-center gap-2 disabled:opacity-50"
                  >
                    {isLocating ? (
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    ) : (
                      <Locate size={18} />
                    )}
                    {isLocating ? 'Locating...' : 'My Location'}
                  </button>
                </div>

                <div className="flex gap-3 overflow-x-auto pb-2 scrollbar-hide">
                  {categories.map((category) => (
                    <motion.button
                      key={category.key}
                      onClick={() => setSelectedCategory(category.key)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`flex-shrink-0 px-6 py-3 rounded-full text-sm font-medium transition-all duration-200 ${
                        selectedCategory === category.key
                          ? 'bg-primary-600 text-white shadow-lg shadow-primary-600/30'
                          : category.color + ' hover:scale-105 shadow-md'
                      }`}
                    >
                      {category.label}
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Enhanced Results Summary */}
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>
                  {loading ? (
                    <span className="flex items-center gap-2">
                      <Loader size={14} className="animate-spin" />
                      Loading suppliers...
                    </span>
                  ) : error ? (
                    <span className="flex items-center gap-2 text-red-600">
                      <AlertCircle size={14} />
                      {error}
                    </span>
                  ) : showSearchResults && searchResults && searchQuery.trim() ? (
                    <span>
                      Found {searchResults.total} result{searchResults.total !== 1 ? 's' : ''}
                      {searchResults.suppliers.length > 0 && ` (${searchResults.suppliers.length} suppliers)`}
                      {searchResults.products.length > 0 && ` (${searchResults.products.length} products)`}
                    </span>
                  ) : (
                    `Found ${displaySuppliers.length} supplier${displaySuppliers.length !== 1 ? 's' : ''}`
                  )}
                  {selectedCategory !== 'all' && !loading && !error && !showSearchResults && ` in ${categories.find(c => c.key === selectedCategory)?.label}`}
                </span>
                {!loading && !error && (
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>Live results</span>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Enhanced Map Container */}
        <div className="relative z-30 mx-4 sm:mx-6 lg:mx-8 mb-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 overflow-hidden"
            style={{ height: '70vh' }}
          >
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Loader size={48} className="animate-spin text-primary-600 mx-auto mb-4" />
                  <p className="text-gray-600">Loading suppliers...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <AlertCircle size={48} className="text-red-500 mx-auto mb-4" />
                  <p className="text-red-600 mb-2">Failed to load suppliers</p>
                  <p className="text-gray-500 text-sm">{error}</p>
                  <button
                    onClick={loadSuppliers}
                    className="mt-4 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            ) : displaySuppliers.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Search size={48} className="text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-2">No suppliers found</p>
                  <p className="text-gray-500 text-sm">
                    {searchQuery.trim() ? 'Try adjusting your search terms' : 'No suppliers available in this area'}
                  </p>
                </div>
              </div>
            ) : (
              <MapContainer
                center={mapCenter}
                zoom={13}
                style={{ height: '100%', width: '100%' }}
                className="rounded-2xl"
              >
                <TileLayer
                  attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                  url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                />

                {/* User Location Marker */}
                {userLocation && (
                  <Marker position={[userLocation.lat, userLocation.lng]}>
                    <Popup>
                      <div className="text-center">
                        <strong>Your Location</strong>
                      </div>
                    </Popup>
                  </Marker>
                )}

                {/* Supplier Markers */}
                {displaySuppliers.map((supplier) => (
                  <Marker
                    key={supplier._id}
                    position={[supplier.lat, supplier.lng]}
                    icon={createCustomMarker(supplier)}
                    eventHandlers={{
                      click: () => setSelectedSupplier(supplier),
                    }}
                  >
                    <Popup>
                      <div className="text-center min-w-[200px]">
                        <div className="flex items-center gap-2 mb-2">
                          <img
                            src={supplier.logoUrl}
                            alt={supplier.name}
                            className="w-8 h-8 rounded-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                            }}
                          />
                          <div>
                            <h3 className="font-bold text-sm">{supplier.name}</h3>
                            <p className="text-xs text-gray-600 capitalize">{supplier.category}</p>
                          </div>
                        </div>
                        <div className="flex items-center justify-between text-xs text-gray-600 mb-2">
                          <div className="flex items-center gap-1">
                            <Star size={12} className="text-yellow-500 fill-current" />
                            <span>{supplier.rating}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock size={12} />
                            <span>{supplier.deliveryTime}</span>
                          </div>
                        </div>
                        <div className={`text-xs px-2 py-1 rounded-full ${
                          supplier.isActive
                            ? 'bg-green-100 text-green-700'
                            : 'bg-red-100 text-red-700'
                        }`}>
                          {supplier.isActive ? 'Open' : 'Closed'}
                        </div>
                      </div>
                    </Popup>
                  </Marker>
                ))}
              </MapContainer>
            )}

            {/* Enhanced Legend Overlay */}
            {!loading && !error && displaySuppliers.length > 0 && (
              <div className="absolute bottom-6 left-6 bg-white/95 backdrop-blur-xl rounded-xl shadow-lg border border-white/20 p-4 z-[1000]">
                <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <Zap size={16} className="text-primary-600" />
                  Legend
                </h4>
                <div className="space-y-2">
                  {categories.slice(1).map((category) => (
                    <div key={category.key} className="flex items-center gap-3">
                      <div
                        className="w-4 h-4 rounded-full border-2 border-white shadow-md"
                        style={{
                          backgroundColor: getCategoryColor(category.key)
                        }}
                      ></div>
                      <span className="text-sm text-gray-700 font-medium">{category.label}</span>
                    </div>
                  ))}
                  <div className="flex items-center gap-3 pt-2 border-t border-gray-200">
                    <div className="w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-md"></div>
                    <span className="text-sm text-gray-700 font-medium">Your Location</span>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </div>

        {/* Enhanced Supplier Details Modal */}
        <AnimatePresence>
          {selectedSupplier && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/60 backdrop-blur-md z-[9999] flex items-end md:items-center justify-center p-4"
              onClick={() => setSelectedSupplier(null)}
            >
              <motion.div
                initial={{ y: 100, opacity: 0, scale: 0.9 }}
                animate={{ y: 0, opacity: 1, scale: 1 }}
                exit={{ y: 100, opacity: 0, scale: 0.9 }}
                transition={{ type: "spring", damping: 20, stiffness: 300 }}
                onClick={(e) => e.stopPropagation()}
                className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 max-w-md w-full max-h-[85vh] overflow-hidden flex flex-col"
              >
                {/* Enhanced Supplier Header */}
                <div className="relative flex-shrink-0">
                  <div className="relative h-40 bg-gradient-to-br from-primary-500 to-secondary-500 overflow-hidden">
                    <img
                      src={selectedSupplier.banner}
                      alt={selectedSupplier.name}
                      className="w-full h-full object-cover mix-blend-overlay"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=200&fit=crop';
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                  </div>

                  <div className="absolute top-4 right-4">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setSelectedSupplier(null)}
                      className="w-10 h-10 bg-white/90 backdrop-blur-md rounded-full flex items-center justify-center hover:bg-white transition-colors shadow-lg"
                    >
                      <X size={18} className="text-gray-600" />
                    </motion.button>
                  </div>

                  <div className="absolute -bottom-8 left-6">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                      className="w-16 h-16 bg-white rounded-2xl border-4 border-white shadow-xl overflow-hidden"
                    >
                      <img
                        src={selectedSupplier.logoUrl}
                        alt={selectedSupplier.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.parentElement!.innerHTML = `
                            <div class="w-full h-full flex items-center justify-center text-primary-600 font-bold text-lg bg-gradient-to-br from-primary-50 to-secondary-50">
                              ${selectedSupplier.name.charAt(0)}
                            </div>
                          `;
                        }}
                      />
                    </motion.div>
                  </div>
                </div>

                {/* Enhanced Supplier Details */}
                <div className="flex-1 overflow-y-auto p-6 pt-12">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="space-y-6"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-2xl font-bold text-gray-900 mb-2">{selectedSupplier.name}</h3>
                        <p className="text-gray-600 capitalize mb-3 text-lg">{getCategoryLabel(selectedSupplier.category)}</p>
                        <div className="flex items-center gap-6 text-sm">
                          <div className="flex items-center gap-2">
                            <Star size={16} className="text-yellow-500 fill-current" />
                            <span className="font-semibold text-gray-900">{selectedSupplier.rating}</span>
                            <span className="text-gray-500">rating</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock size={16} className="text-blue-500" />
                            <span className="font-semibold text-gray-900">{selectedSupplier.deliveryTime}</span>
                          </div>
                        </div>
                      </div>
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.4, type: "spring" }}
                        className={`px-4 py-2 rounded-full text-sm font-semibold shadow-md ${
                          selectedSupplier.isActive
                            ? 'bg-green-100 text-green-700 border border-green-200'
                            : 'bg-red-100 text-red-700 border border-red-200'
                        }`}
                      >
                        {selectedSupplier.isActive ? '🟢 Open' : '🔴 Closed'}
                      </motion.div>
                    </div>

                    {/* Enhanced Tags */}
                    {selectedSupplier.tags && selectedSupplier.tags.length > 0 && (
                      <div className="space-y-3">
                        <h4 className="font-semibold text-gray-900">Specialties</h4>
                        <div className="flex flex-wrap gap-2">
                          {selectedSupplier.tags.map((tag, index) => (
                            <motion.span
                              key={tag}
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: 0.5 + index * 0.1 }}
                              className="px-3 py-1 bg-gradient-to-r from-primary-100 to-secondary-100 text-primary-700 text-sm rounded-full font-medium border border-primary-200"
                            >
                              {tag}
                            </motion.span>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Enhanced Contact Info */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-gray-900">Contact Information</h4>
                      <div className="space-y-3">
                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.6 }}
                          className="flex items-center gap-4 p-3 bg-gray-50 rounded-xl"
                        >
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Clock size={18} className="text-blue-600" />
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Opening Hours</p>
                            <p className="font-semibold text-gray-900">{selectedSupplier.openHours}</p>
                          </div>
                        </motion.div>

                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.7 }}
                          className="flex items-center gap-4 p-3 bg-gray-50 rounded-xl"
                        >
                          <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                            <Phone size={18} className="text-green-600" />
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Phone</p>
                            <p className="font-semibold text-gray-900">{selectedSupplier.phone}</p>
                          </div>
                        </motion.div>

                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.8 }}
                          className="flex items-center gap-4 p-3 bg-gray-50 rounded-xl"
                        >
                          <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                            <MapPin size={18} className="text-purple-600" />
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Location</p>
                            <p className="font-semibold text-gray-900">
                              {selectedSupplier.address || 'Nablus, Palestine'}
                            </p>
                          </div>
                        </motion.div>
                      </div>
                    </div>

                    {/* Enhanced Action Buttons */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.9 }}
                      className="flex gap-3 pt-4"
                    >
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => {
                          window.location.href = `/customer/supplier-details/${selectedSupplier._id}`;
                        }}
                        disabled={!selectedSupplier.isActive}
                        className="flex-1 px-6 py-4 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold rounded-xl transition-all duration-200 flex items-center justify-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                      >
                        <Eye size={20} />
                        {selectedSupplier.isActive ? 'View Menu' : 'Closed'}
                      </motion.button>

                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => {
                          const url = `https://www.google.com/maps/dir/?api=1&destination=${selectedSupplier.lat},${selectedSupplier.lng}`;
                          window.open(url, '_blank');
                        }}
                        className="px-6 py-4 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold rounded-xl transition-all duration-200 flex items-center justify-center gap-3 shadow-md"
                      >
                        <Navigation size={20} />
                        Directions
                      </motion.button>
                    </motion.div>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  );
};

export default SuppliersMapPage;