import React from 'react';
import { useTranslation } from 'react-i18next';
import { Globe } from 'lucide-react';
import { useLanguageStore } from '../../stores/languageStore';
import { motion } from 'framer-motion';

interface LanguageIndicatorProps {
  showIcon?: boolean;
  showText?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const LanguageIndicator: React.FC<LanguageIndicatorProps> = ({
  showIcon = true,
  showText = true,
  className = '',
  size = 'md'
}) => {
  const { t } = useTranslation();
  const { currentLanguage, isRTL } = useLanguageStore();

  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const iconSizes = {
    sm: 14,
    md: 16,
    lg: 18
  };

  const languages = {
    en: { name: 'English', flag: '🇺🇸', short: 'EN' },
    ar: { name: 'العربية', flag: '🇵🇸', short: 'ع' }
  };

  const currentLang = languages[currentLanguage as keyof typeof languages];

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`flex items-center gap-2 px-3 py-1.5 bg-white/80 backdrop-blur-sm border border-white/30 rounded-full shadow-sm ${className}`}
    >
      {showIcon && (
        <motion.div
          animate={{ rotate: [0, 360] }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        >
          <Globe size={iconSizes[size]} className="text-gray-600" />
        </motion.div>
      )}
      
      {showText && (
        <div className="flex items-center gap-1">
          <span className={`font-medium text-gray-700 ${sizeClasses[size]}`}>
            {currentLang.short}
          </span>
          <span className={`text-gray-500 ${sizeClasses[size]}`}>
            {currentLang.flag}
          </span>
        </div>
      )}
    </motion.div>
  );
}; 