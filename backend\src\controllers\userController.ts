import { Response } from 'express';
import User from '../models/User';
import { Order } from '../models/Order';
import { Package } from '../models/Package';
import { EmailService } from '../services/emailService';
import { 
  AuthenticatedRequest, 
  UpdateProfileRequest, 
  ChangePasswordRequest,
  ApiResponse 
} from '../types';

export class UserController {
  static async getProfile(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const user = req.user;

      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Profile retrieved successfully',
        data: { user: user.toJSON() },
      });
    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async updateProfile(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const user = req.user;
      const updates: UpdateProfileRequest = req.body;

      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Update allowed fields - expanded to include all profile fields
      const allowedUpdates = [
        'firstName',
        'lastName',
        'phoneNumber',
        'address',
        'city',
        'country',
        'dateOfBirth',
        'gender'
      ];
      const updateData: any = {};

      allowedUpdates.forEach(field => {
        if (updates[field as keyof UpdateProfileRequest] !== undefined) {
          updateData[field] = updates[field as keyof UpdateProfileRequest];
        }
      });

      const updatedUser = await User.findByIdAndUpdate(
        user._id,
        updateData,
        { new: true, runValidators: true }
      );

      if (!updatedUser) {
        res.status(404).json({
          success: false,
          message: 'User not found',
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Profile updated successfully',
        data: { user: updatedUser.toJSON() },
      });
    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async changePassword(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const user = req.user;
      const { currentPassword, newPassword }: ChangePasswordRequest = req.body;

      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Get user with password for comparison
      const userWithPassword = await User.findById(user._id).select('+password');
      if (!userWithPassword) {
        res.status(404).json({
          success: false,
          message: 'User not found',
        });
        return;
      }

      // Verify current password
      const isCurrentPasswordValid = await userWithPassword.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        res.status(400).json({
          success: false,
          message: 'Current password is incorrect',
        });
        return;
      }

      // Update password
      userWithPassword.password = newPassword;
      userWithPassword.refreshTokens = []; // Logout from all devices
      await userWithPassword.save();

      // Send notification email
      try {
        await EmailService.sendPasswordChangeNotification(userWithPassword);
      } catch (emailError) {
        console.error('Failed to send password change notification:', emailError);
        // Don't fail the request if email fails
      }

      res.status(200).json({
        success: true,
        message: 'Password changed successfully',
      });
    } catch (error) {
      console.error('Change password error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async deleteAccount(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const user = req.user;

      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Soft delete - deactivate account
      await User.findByIdAndUpdate(user._id, { 
        isActive: false,
        refreshTokens: [] // Logout from all devices
      });

      res.status(200).json({
        success: true,
        message: 'Account deactivated successfully',
      });
    } catch (error) {
      console.error('Delete account error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async getUserById(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const { userId } = req.params;
      const currentUser = req.user;

      if (!currentUser) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Only allow users to view their own profile or admins to view any profile
      if (currentUser._id !== userId && currentUser.role !== 'admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied',
        });
        return;
      }

      const user = await User.findById(userId);
      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found',
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'User retrieved successfully',
        data: { user: user.toJSON() },
      });
    } catch (error) {
      console.error('Get user by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async getUserStats(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const userId = req.user?._id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Get user data
      const user = await User.findById(userId);
      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found',
        });
        return;
      }

      // Convert userId to string for comparison
      const userIdString = userId.toString();

      // Get order statistics
      const orderStats = await Order.aggregate([
        { $match: { userId: userIdString } },
        {
          $group: {
            _id: null,
            totalOrders: { $sum: 1 },
            completedOrders: { 
              $sum: { 
                $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] 
              } 
            },
            totalSpent: { $sum: '$totalAmount' },
            averageOrderValue: { $avg: '$totalAmount' },
            firstOrderDate: { $min: '$createdAt' }
          }
        }
      ]);

      // Get the 10th order date if user has 10+ orders
      let tenthOrderDate = null;
      if (orderStats[0]?.totalOrders >= 10) {
        const tenthOrder = await Order.findOne(
          { userId: userIdString },
          { createdAt: 1 },
          { sort: { createdAt: 1 }, skip: 9 }
        );
        tenthOrderDate = tenthOrder?.createdAt || null;
      }

      // Get the date when user reached ₪100 spending milestone
      let hundredIlsOrderDate = null;
      if (orderStats[0]?.totalSpent >= 100) {
        // Find the order that pushed total spending over ₪100
        const orders = await Order.find(
          { userId: userIdString },
          { totalAmount: 1, createdAt: 1 },
          { sort: { createdAt: 1 } }
        );
        
        let runningTotal = 0;
        for (const order of orders) {
          runningTotal += order.totalAmount;
          if (runningTotal >= 100) {
            hundredIlsOrderDate = order.createdAt;
            break;
          }
        }
      }

      // Get package statistics
      const packageStats = await Package.aggregate([
        { $match: { userId: userIdString } },
        {
          $group: {
            _id: null,
            totalPackages: { $sum: 1 },
            sentPackages: { 
              $sum: { 
                $cond: [{ $eq: ['$serviceType', 'send_package'] }, 1, 0] 
              } 
            },
            requestedPickups: { 
              $sum: { 
                $cond: [{ $eq: ['$serviceType', 'request_pickup'] }, 1, 0] 
              } 
            },
            completedPackages: { 
              $sum: { 
                $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] 
              } 
            }
          }
        }
      ]);

      // Get the first package date if user has sent packages
      let firstPackageDate = null;
      if (packageStats[0]?.sentPackages > 0) {
        const firstPackage = await Package.findOne(
          { userId: userIdString, serviceType: 'send_package' },
          { createdAt: 1 },
          { sort: { createdAt: 1 } }
        );
        firstPackageDate = firstPackage?.createdAt || null;
      }

      // Calculate member since year
      const memberSince = user.createdAt ? new Date(user.createdAt).getFullYear().toString() : 'N/A';

      // Calculate streak days based on user engagement
      const calculateStreak = async () => {
        try {
          // Get all user activities (orders and packages) sorted by date
          const userActivities = await Promise.all([
            Order.find(
              { userId: userIdString },
              { createdAt: 1, status: 1 },
              { sort: { createdAt: -1 } }
            ),
            Package.find(
              { userId: userIdString },
              { createdAt: 1, status: 1 },
              { sort: { createdAt: -1 } }
            )
          ]);

          const [orders, packages] = userActivities;
          
          // Combine and sort all activities by date (most recent first)
          const allActivities = [
            ...orders.map(order => ({
              date: new Date(order.createdAt).toDateString(),
              type: 'order',
              status: order.status
            })),
            ...packages.map(pack => ({
              date: new Date(pack.createdAt).toDateString(),
              type: 'package',
              status: pack.status
            }))
          ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

          // Remove duplicates (same date) and keep only unique dates
          const uniqueDates = [...new Set(allActivities.map(activity => activity.date))];
          
          if (uniqueDates.length === 0) return 0;

          // Calculate current streak
          let currentStreak = 0;
          const today = new Date().toDateString();
          const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString();
          
          // Check if user has activity today or yesterday to maintain streak
          const hasRecentActivity = uniqueDates.includes(today) || uniqueDates.includes(yesterday);
          
          if (!hasRecentActivity) return 0;

          // Calculate consecutive days with activity
          let currentDate = new Date();
          let consecutiveDays = 0;
          
          for (let i = 0; i < 365; i++) { // Check up to 1 year back
            const checkDate = new Date(currentDate.getTime() - i * 24 * 60 * 60 * 1000).toDateString();
            
            if (uniqueDates.includes(checkDate)) {
              consecutiveDays++;
            } else {
              break; // Streak broken
            }
          }
          
          return consecutiveDays;
        } catch (error) {
          console.error('Error calculating streak:', error);
          return 0;
        }
      };

      const streakDays = await calculateStreak();

      // Prepare response
      const stats = {
        // User info
        memberSince,
        streakDays,
        
        // Order statistics
        totalOrders: orderStats[0]?.totalOrders || 0,
        completedOrders: orderStats[0]?.completedOrders || 0,
        totalSpent: orderStats[0]?.totalSpent || 0,
        averageOrderValue: orderStats[0]?.averageOrderValue || 0,
        firstOrderDate: orderStats[0]?.firstOrderDate || null,
        tenthOrderDate: tenthOrderDate,
        hundredIlsOrderDate: hundredIlsOrderDate,
        
        // Package statistics
        totalPackages: packageStats[0]?.totalPackages || 0,
        sentPackages: packageStats[0]?.sentPackages || 0,
        requestedPickups: packageStats[0]?.requestedPickups || 0,
        completedPackages: packageStats[0]?.completedPackages || 0,
        firstPackageDate: firstPackageDate,
        
        // Calculated fields
        orderCompletionRate: orderStats[0]?.totalOrders > 0 
          ? Math.round((orderStats[0].completedOrders / orderStats[0].totalOrders) * 100) 
          : 0,
        packageCompletionRate: packageStats[0]?.totalPackages > 0 
          ? Math.round((packageStats[0].completedPackages / packageStats[0].totalPackages) * 100) 
          : 0
      };

      res.status(200).json({
        success: true,
        message: 'User statistics retrieved successfully',
        data: stats,
      });
    } catch (error) {
      console.error('Get user stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
}
