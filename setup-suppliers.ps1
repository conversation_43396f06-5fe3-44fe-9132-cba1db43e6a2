# Setup Suppliers with User Accounts Script

Write-Host "🏪 Setting up suppliers with user accounts..." -ForegroundColor Green

# Step 1: Check current suppliers
Write-Host "`n📋 Step 1: Checking current suppliers in database..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/api/suppliers" -Method GET
    $data = $response.Content | ConvertFrom-Json
    
    Write-Host "✅ Found $($data.data.Count) suppliers in database:" -ForegroundColor Green
    foreach($supplier in $data.data) {
        Write-Host "  - $($supplier.name) ($($supplier.id)) - Category: $($supplier.category)" -ForegroundColor White
    }
} catch {
    Write-Host "❌ Failed to get suppliers: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Create user accounts for existing suppliers
Write-Host "`n👥 Step 2: Creating user accounts for existing suppliers..." -ForegroundColor Yellow

# Create account for "3a kefak" supplier
Write-Host "`n📝 Creating account for '3a kefak'..." -ForegroundColor Cyan
$kefakData = @{
    firstName = "Ahmad"
    lastName = "Kefak"
    email = "<EMAIL>"
    phoneNumber = "+************"
    password = "Kefak123!"
    username = "ahmad_kefak"
    address = "Downtown Nablus"
    city = "Nablus"
    country = "Palestine"
    role = "supplier"
    storeName = "3a kefak"
    businessType = "restaurants"
    openHours = "9:00 AM - 11:00 PM"
    location = @(35.2544, 32.2211)
} | ConvertTo-Json

try {
    $kefakResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/auth/signup" -Method POST -Body $kefakData -ContentType "application/json"
    Write-Host "✅ Created account for '3a kefak' successfully!" -ForegroundColor Green
} catch {
    if ($_.Exception.Message -like "*already exists*") {
        Write-Host "⚠️  Account for '3a kefak' already exists" -ForegroundColor Yellow
    } else {
        Write-Host "❌ Failed to create account for '3a kefak': $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Create account for "Style Point" supplier
Write-Host "`n📝 Creating account for 'Style Point'..." -ForegroundColor Cyan
$styleData = @{
    firstName = "Sara"
    lastName = "Fashion"
    email = "<EMAIL>"
    phoneNumber = "+************"
    password = "Style123!"
    username = "sara_style"
    address = "Fashion District, Ramallah"
    city = "Ramallah"
    country = "Palestine"
    role = "supplier"
    storeName = "Style Point"
    businessType = "clothings"
    openHours = "10:00 AM - 9:00 PM"
    location = @(35.2066, 31.9073)
} | ConvertTo-Json

try {
    $styleResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/auth/signup" -Method POST -Body $styleData -ContentType "application/json"
    Write-Host "✅ Created account for 'Style Point' successfully!" -ForegroundColor Green
} catch {
    if ($_.Exception.Message -like "*already exists*") {
        Write-Host "⚠️  Account for 'Style Point' already exists" -ForegroundColor Yellow
    } else {
        Write-Host "❌ Failed to create account for 'Style Point': $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Step 3: Test login for both suppliers
Write-Host "`n🔐 Step 3: Testing login for both suppliers..." -ForegroundColor Yellow

# Test login for 3a kefak
Write-Host "`n🔑 Testing login for '3a kefak'..." -ForegroundColor Cyan
$kefakLogin = @{
    email = "<EMAIL>"
    password = "Kefak123!"
} | ConvertTo-Json

try {
    $kefakLoginResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/auth/login" -Method POST -Body $kefakLogin -ContentType "application/json"
    $kefakLoginData = $kefakLoginResponse.Content | ConvertFrom-Json
    Write-Host "✅ Login successful for '3a kefak'!" -ForegroundColor Green
    Write-Host "   Token: $($kefakLoginData.data.accessToken.Substring(0,20))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ Login failed for '3a kefak': $($_.Exception.Message)" -ForegroundColor Red
}

# Test login for Style Point
Write-Host "`n🔑 Testing login for 'Style Point'..." -ForegroundColor Cyan
$styleLogin = @{
    email = "<EMAIL>"
    password = "Style123!"
} | ConvertTo-Json

try {
    $styleLoginResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/auth/login" -Method POST -Body $styleLogin -ContentType "application/json"
    $styleLoginData = $styleLoginResponse.Content | ConvertFrom-Json
    Write-Host "✅ Login successful for 'Style Point'!" -ForegroundColor Green
    Write-Host "   Token: $($styleLoginData.data.accessToken.Substring(0,20))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ Login failed for 'Style Point': $($_.Exception.Message)" -ForegroundColor Red
}

# Step 4: Final verification
Write-Host "`n🔍 Step 4: Final verification..." -ForegroundColor Yellow

try {
    $finalResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/suppliers" -Method GET
    $finalData = $finalResponse.Content | ConvertFrom-Json
    
    Write-Host "✅ Final check - Total suppliers visible: $($finalData.data.Count)" -ForegroundColor Green
    
    # Check restaurant category
    $restaurantResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/suppliers/category/restaurants" -Method GET
    $restaurantData = $restaurantResponse.Content | ConvertFrom-Json
    Write-Host "🍽️  Restaurants: $($restaurantData.data.Count)" -ForegroundColor Green
    
    # Check clothing category
    $clothingResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/suppliers/category/clothings" -Method GET
    $clothingData = $clothingResponse.Content | ConvertFrom-Json
    Write-Host "👕 Clothing stores: $($clothingData.data.Count)" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Final verification failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 Setup completed!" -ForegroundColor Green
Write-Host "`n📋 SUPPLIER ACCOUNTS CREATED:" -ForegroundColor White
Write-Host "1. 3a kefak:" -ForegroundColor Yellow
Write-Host "   📧 Email: <EMAIL>" -ForegroundColor White
Write-Host "   🔑 Password: Kefak123!" -ForegroundColor White
Write-Host "   👤 Username: ahmad_kefak" -ForegroundColor White
Write-Host ""
Write-Host "2. Style Point:" -ForegroundColor Yellow
Write-Host "   📧 Email: <EMAIL>" -ForegroundColor White
Write-Host "   🔑 Password: Style123!" -ForegroundColor White
Write-Host "   👤 Username: sara_style" -ForegroundColor White
Write-Host ""
Write-Host "✅ Both suppliers now have user accounts and can:" -ForegroundColor Green
Write-Host "   - Login to mobile app" -ForegroundColor White
Write-Host "   - Manage their products" -ForegroundColor White
Write-Host "   - Update their profiles" -ForegroundColor White
Write-Host "   - Appear in customer pages" -ForegroundColor White
