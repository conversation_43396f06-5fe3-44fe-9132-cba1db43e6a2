import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import { getPackageTypes, createPackage } from '../../services/api';

// Import the web-specific Zustand store
import { useSendPackageStore } from '../../stores/useSendPackageStore';
import {
  MapPin,
  User,
  Package,
  Phone,
  FileText,
  Send,
  ArrowRight,
  Truck,
  Clock,
  DollarSign,
  Shield,
  Star,
  CheckCircle,
  AlertCircle,
  Navigation,
  Zap,
  Gift,
  Smartphone,
  Book,
  ShoppingBag,
  Heart,
  Coffee,
  Camera,
  Headphones,
  Monitor,
  Search,
  ChevronDown,
  ChevronRight,
  Map,
  Route,
  Timer,
  CreditCard,
  X,
  Target
} from 'lucide-react';
import Logo from '../../components/common/Logo';
import {
  formatNumberToHalfStep,
  formatCurrency,
  validateNumericInput,
  validateWeight,
  validatePackageValue
} from '../../utils/numericInputUtils';
import PhoneInput from '../../components/common/PhoneInput';

interface Location {
  lat: number;
  lng: number;
  address: string;
  street: string;
  city: string;
  buildingNumber?: string;
  apartmentNumber?: string;
  landmarks?: string;
}

interface PackageTypeOption {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  estimatedTime: string;
  basePrice: number;
}

interface FormData {
  // Sender Information
  senderName: string;
  senderPhone: string;

  // Locations
  pickup: Location | null;
  dropoff: Location | null;

  // Receiver Information
  receiverName: string;
  receiverPhone: string;

  // Package Details
  packageType: string;
  packageDescription: string;
  packageSize: 'small' | 'medium' | 'large';
  packageWeight: string;
  packageValue: number;
  isFragile: boolean;

  // Service Options
  urgency: 'standard' | 'express' | 'urgent';
  paymentMethod: 'cash' | 'card' | 'wallet';
  scheduledPickupTime: Date | null;

  // Additional
  notes: string;
  insurance: boolean;
}

const SendPackagePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [formData, setFormData] = useState<FormData>({
    // Sender Information
    senderName: '',
    senderPhone: '',

    // Locations
    pickup: null,
    dropoff: null,

    // Receiver Information
    receiverName: '',
    receiverPhone: '',

    // Package Details
    packageType: '',
    packageDescription: '',
    packageSize: 'medium',
    packageWeight: '',
    packageValue: 0,
    isFragile: false,

    // Service Options
    urgency: 'standard',
    paymentMethod: 'cash',
    scheduledPickupTime: null,

    // Additional
    notes: '',
    insurance: false
  });

  // Fetch package types from backend
  useEffect(() => {
    const fetchPackageTypes = async () => {
      try {
        setLoading(true);
        setError(null);
        const types = await getPackageTypes();
        const typesWithIcons = types.map(type => ({
          ...type,
          icon: getPackageIcon(type.icon)
        }));
        setPackageTypes(typesWithIcons);
      } catch (err) {
        console.error('Error fetching package types:', err);
        setError('Failed to load package types');
        // Fallback to default types
        setPackageTypes([
          {
            id: 'general',
            name: 'General Items',
            icon: getPackageIcon('Package'),
            description: 'Other items and packages',
            estimatedTime: '3-5 hours',
            basePrice: 18
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchPackageTypes();
  }, []);

  // Debug: Log when component mounts
  useEffect(() => {
    console.log('SendPackagePage component mounted');
    console.log('Location state:', location.state);

    // First, restore form data from sessionStorage if available
    const savedFormData = sessionStorage.getItem('sendPackageFormData');
    if (savedFormData) {
      try {
        const parsedFormData = JSON.parse(savedFormData);
        console.log('Restoring form data from sessionStorage:', parsedFormData);
        setFormData(parsedFormData);
        // Clear the saved data after restoring
        sessionStorage.removeItem('sendPackageFormData');
      } catch (error) {
        console.error('Error parsing saved form data:', error);
        sessionStorage.removeItem('sendPackageFormData');
      }
    }

    // Check if we have location data from navigation state
    if (location.state && location.state.locationData) {
      const locationData = location.state.locationData;
      console.log('Found location data in navigation state:', locationData);

      if (locationData.type === 'pickup') {
        const addressParts = parseAddress(locationData.address);
        updateField('pickup', {
          lat: locationData.lat,
          lng: locationData.lng,
          address: locationData.address,
          street: addressParts.street,
          city: addressParts.city
        });
      } else if (locationData.type === 'dropoff') {
        const addressParts = parseAddress(locationData.address);
        updateField('dropoff', {
          lat: locationData.lat,
          lng: locationData.lng,
          address: locationData.address,
          street: addressParts.street,
          city: addressParts.city
        });
      }

      // Clear the navigation state to prevent reprocessing
      window.history.replaceState({}, document.title);
    }

    return () => {
      console.log('SendPackagePage component unmounted');
    };
  }, []);

  // Debug: Log when formData changes
  useEffect(() => {
    console.log('FormData changed:', formData);
  }, [formData]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [showPackageTypes, setShowPackageTypes] = useState(false);
  const [estimatedCost, setEstimatedCost] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState('');
  const [packageTypes, setPackageTypes] = useState<PackageTypeOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Zustand store
  const { addPackage, setCurrentPackage } = useSendPackageStore();


  // Icon mapping for package types
  const getPackageIcon = (iconName: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      'FileText': <FileText size={24} />,
      'Smartphone': <Smartphone size={24} />,
      'ShoppingBag': <ShoppingBag size={24} />,
      'Coffee': <Coffee size={24} />,
      'Gift': <Gift size={24} />,
      'Package': <Package size={24} />
    };
    return iconMap[iconName] || <Package size={24} />;
  };

  // Package size options
  const packageSizes = [
    { id: 'small', name: 'Small', description: 'Up to 2kg, fits in envelope', multiplier: 1 },
    { id: 'medium', name: 'Medium', description: 'Up to 10kg, shoebox size', multiplier: 1.2 },
    { id: 'large', name: 'Large', description: 'Up to 25kg, large box', multiplier: 1.5 }
  ];

  // Payment method options
  const paymentMethods = [
    { id: 'cash', name: 'Cash on Delivery', icon: <DollarSign size={20} />, description: 'Pay when package is delivered' },
    { id: 'card', name: 'Credit Card', icon: <CreditCard size={20} />, description: 'Pay now with card' },
    { id: 'wallet', name: 'Digital Wallet', icon: <Smartphone size={20} />, description: 'Pay with mobile wallet' }
  ];

  // Handle scroll for header animation
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsHeaderCompact(currentScrollY > 200);
      }, 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Listen for location selection from SelectLocationPage
  useEffect(() => {
    const handleLocationUpdate = (event: CustomEvent) => {
      console.log('Location update event received:', event.detail);
      const locationData = event.detail;

      if (locationData && locationData.lat && locationData.lng && locationData.address) {
        const addressParts = parseAddress(locationData.address);
        const location: Location = {
          lat: locationData.lat,
          lng: locationData.lng,
          address: locationData.address,
          street: addressParts.street,
          city: addressParts.city
        };

        console.log(`Processing ${locationData.type} location:`, location);

        if (locationData.type === 'pickup') {
          console.log('Setting pickup location');
          updateField('pickup', location);
          // Clear only the pickup location from localStorage after successful update
          localStorage.removeItem('selectedPickupLocation');
          if (!formData.dropoff) {
            setCurrentStep(2);
          }
        } else if (locationData.type === 'dropoff') {
          console.log('Setting dropoff location');
          updateField('dropoff', location);
          // Clear only the dropoff location from localStorage after successful update
          localStorage.removeItem('selectedDropoffLocation');
          if (formData.pickup) {
            setCurrentStep(3);
          }
        }

        // Also remove old key for backward compatibility
        localStorage.removeItem('selectedDeliveryLocation');
      }
    };

    window.addEventListener('locationSelected', handleLocationUpdate as EventListener);

    return () => {
      window.removeEventListener('locationSelected', handleLocationUpdate as EventListener);
    };
  }, []); // Remove dependencies to prevent re-running when locations change

  // Separate effect to check localStorage on component mount only
  useEffect(() => {
    const checkStoredLocation = () => {
      // Check for pickup location
      const storedPickup = localStorage.getItem('selectedPickupLocation');
      if (storedPickup) {
        try {
          const locationData = JSON.parse(storedPickup);
          console.log('Found stored pickup location:', locationData);
          if (locationData && locationData.lat && locationData.lng && locationData.address) {
            const addressParts = parseAddress(locationData.address);
            const location: Location = {
              lat: locationData.lat,
              lng: locationData.lng,
              address: locationData.address,
              street: addressParts.street,
              city: addressParts.city
            };
            updateField('pickup', location);
            localStorage.removeItem('selectedPickupLocation');
            if (!formData.dropoff) {
              setCurrentStep(2);
            }
          }
        } catch (error) {
          console.error('Error parsing stored pickup location:', error);
          localStorage.removeItem('selectedPickupLocation');
        }
      }

      // Check for dropoff location
      const storedDropoff = localStorage.getItem('selectedDropoffLocation');
      if (storedDropoff) {
        try {
          const locationData = JSON.parse(storedDropoff);
          console.log('Found stored dropoff location:', locationData);
          if (locationData && locationData.lat && locationData.lng && locationData.address) {
            const addressParts = parseAddress(locationData.address);
            const location: Location = {
              lat: locationData.lat,
              lng: locationData.lng,
              address: locationData.address,
              street: addressParts.street,
              city: addressParts.city
            };
            updateField('dropoff', location);
            localStorage.removeItem('selectedDropoffLocation');
            if (formData.pickup) {
              setCurrentStep(3);
            }
          }
        } catch (error) {
          console.error('Error parsing stored dropoff location:', error);
          localStorage.removeItem('selectedDropoffLocation');
        }
      }

      // Also check old key for backward compatibility
      const storedLocation = localStorage.getItem('selectedDeliveryLocation');
      if (storedLocation) {
        try {
          const locationData = JSON.parse(storedLocation);
          console.log('Found stored location (old key):', locationData);
          if (locationData && locationData.lat && locationData.lng && locationData.address) {
            const addressParts = parseAddress(locationData.address);
            const location: Location = {
              lat: locationData.lat,
              lng: locationData.lng,
              address: locationData.address,
              street: addressParts.street,
              city: addressParts.city
            };
            // Determine type based on existing data or default to pickup
            if (locationData.type === 'pickup' || !formData.pickup) {
              updateField('pickup', location);
            } else if (locationData.type === 'dropoff' || !formData.dropoff) {
              updateField('dropoff', location);
            }
            localStorage.removeItem('selectedDeliveryLocation');
          }
        } catch (error) {
          console.error('Error parsing stored location:', error);
          localStorage.removeItem('selectedDeliveryLocation');
        }
      }
    };

    checkStoredLocation();
  }, []); // Run only on component mount

  // Calculate estimated cost and time
  useEffect(() => {
    if (formData.pickup && formData.dropoff && formData.packageType) {
      const selectedType = packageTypes.find(type => type.id === formData.packageType);
      const selectedSize = packageSizes.find(size => size.id === formData.packageSize);

      if (selectedType && selectedSize) {
        let cost = selectedType.basePrice * selectedSize.multiplier;

        // Add urgency multiplier
        if (formData.urgency === 'express') cost *= 1.5;
        if (formData.urgency === 'urgent') cost *= 2;

        // Add insurance based on package value
        if (formData.insurance) {
          const insuranceFee = Math.max(5, formData.packageValue * 0.02); // 2% of value, minimum $5
          cost += insuranceFee;
        }

        // Add fragile handling fee
        if (formData.isFragile) cost += 3;

        setEstimatedCost(formatNumberToHalfStep(cost)); // Round to nearest 0.5
        setEstimatedTime(selectedType.estimatedTime);
      }
    }
  }, [formData.pickup, formData.dropoff, formData.packageType, formData.packageSize, formData.urgency, formData.insurance, formData.packageValue, formData.isFragile, packageTypes, packageSizes]);

  // Helper function to parse address into components
  const parseAddress = (address: string): { street: string; city: string } => {
    const parts = address.split(',').map(part => part.trim());
    if (parts.length >= 2) {
      return {
        street: parts.slice(0, -1).join(', '),
        city: parts[parts.length - 1]
      };
    }
    return {
      street: address,
      city: 'Nablus' // Default city
    };
  };

  const updateField = (field: keyof FormData, value: any) => {
    console.log(`UpdateField called: ${field}`, value);
    setFormData(prev => {
      console.log('Previous formData:', prev);
      const newData = { ...prev, [field]: value };
      console.log('New formData:', newData);
      return newData;
    });
  };

  // Enhanced numeric field handlers


  const handleWeightInput = (value: string) => {
    const validated = validateWeight(value);
    updateField('packageWeight', validated);
  };

  const handleValueInput = (value: string) => {
    const numValue = validatePackageValue(value);
    updateField('packageValue', numValue);
  };

  const isFormValid = () => {
    return formData.senderName.trim() &&
           formData.senderPhone.trim() &&
           formData.pickup &&
           formData.dropoff &&
           formData.receiverName.trim() &&
           formData.receiverPhone.trim() &&
           formData.packageType.trim() &&
           formData.packageDescription.trim() &&
           formData.packageWeight.trim();
  };

  const getFormProgress = () => {
    const fields = [
      formData.senderName.trim(),
      formData.senderPhone.trim(),
      formData.pickup,
      formData.dropoff,
      formData.receiverName.trim(),
      formData.receiverPhone.trim(),
      formData.packageType.trim(),
      formData.packageDescription.trim(),
      formData.packageWeight.trim()
    ];
    const completedFields = fields.filter(Boolean).length;
    return (completedFields / fields.length) * 100;
  };

  const handleLocationSelect = (type: 'pickup' | 'dropoff') => {
    console.log(`Navigating to location selection for ${type}`);
    // Store current form data in sessionStorage before navigation
    sessionStorage.setItem('sendPackageFormData', JSON.stringify(formData));
    navigate(`/customer/select-location?type=${type}`);
  };

  const handleSubmit = async () => {
    if (!isFormValid()) return;

    setIsSubmitting(true);
    try {
      // Prepare package data for backend API
      const packageData = {
        senderName: formData.senderName,
        senderPhone: formData.senderPhone,
        recipientInfo: {
          name: formData.receiverName,
          phone: formData.receiverPhone
        },
        pickupAddress: {
          street: formData.pickup?.street || formData.pickup?.address || '',
          city: formData.pickup?.city || 'Nablus',
          coordinates: {
            lat: formData.pickup?.lat || 32.2211,
            lng: formData.pickup?.lng || 35.2544
          },
          notes: formData.pickup?.landmarks || ''
        },
        deliveryAddress: {
          street: formData.dropoff?.street || formData.dropoff?.address || '',
          city: formData.dropoff?.city || 'Nablus',
          coordinates: {
            lat: formData.dropoff?.lat || 32.2211,
            lng: formData.dropoff?.lng || 35.2544
          },
          notes: formData.dropoff?.landmarks || ''
        },
        packageDetails: {
          type: formData.packageType,
          description: formData.packageDescription,
          size: formData.packageSize,
          weight: formData.packageWeight,
          value: formData.packageValue,
          fragile: formData.isFragile
        },
        priority: formData.urgency,
        paymentMethod: formData.paymentMethod,
        scheduledPickupTime: formData.scheduledPickupTime?.toISOString(),
        notes: formData.notes
      };

      console.log('Submitting package request:', packageData);
      console.log('Sender phone:', packageData.senderPhone);
      console.log('Receiver phone:', packageData.recipientInfo.phone);

      // Create package via backend API
      const response = await createPackage(packageData);

      console.log('Package created successfully:', response);
      console.log('Full API response:', response);

      // Store the successful package data in Zustand store
      if (response) {
        const packageData = {
          ...response,
          // Ensure we have the tracking number for easy retrieval
          trackingNumber: response.packageId || response._id,
          // Add any additional fields that might be needed
          status: response.status || 'pending',
          createdAt: response.createdAt || new Date().toISOString(),
          updatedAt: response.updatedAt || new Date().toISOString()
        };
        
        // Add to Zustand store
        addPackage(packageData);
        setCurrentPackage(packageData);
      }

      // Navigate to confirmation page with tracking number
      navigate('/customer/send-package-confirmation', {
        state: {
          trackingNumber: response?.packageId || response?._id || 'Unknown',
          packageData: response
        }
      });
    } catch (error) {
      console.error('Failed to submit package request:', error);
      let errorMessage = 'Failed to submit package request';

      if (error instanceof Error) {
        errorMessage = error.message;
        // If it's a validation error, try to extract more details
        if (error.message.includes('Validation failed')) {
          errorMessage = 'Please check your form data and try again';
        }
      }

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePackageTypeSelect = (typeId: string) => {
    updateField('packageType', typeId);
    setShowPackageTypes(false);
    setCurrentStep(4);
  };

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-emerald-500/30 to-cyan-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-orange-500/30 to-pink-600/30 rounded-full blur-3xl"
          />

          {/* Floating particles */}
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, -100, -20],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Sticky Header with Scroll Animation */}
        <motion.div
          className="fixed top-0 left-0 right-0 z-50 transition-all duration-500"
          animate={{
            backgroundColor: isHeaderCompact
              ? "rgba(15, 23, 42, 0.95)"
              : "rgba(15, 23, 42, 0)",
            backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
            borderBottom: isHeaderCompact
              ? "1px solid rgba(255, 255, 255, 0.1)"
              : "1px solid rgba(255, 255, 255, 0)",
          }}
          transition={{ duration: 0.3 }}
        >
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              animate={{
                paddingTop: isHeaderCompact ? "1rem" : "2rem",
                paddingBottom: isHeaderCompact ? "1rem" : "2rem",
              }}
              transition={{ duration: 0.3 }}
            >
              {/* Compact Header Content */}
              <motion.div
                animate={{
                  opacity: isHeaderCompact ? 1 : 0,
                  height: isHeaderCompact ? "auto" : 0,
                }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                    >
                      <Logo size="sm" />
                    </motion.div>
                    <div>
                      <p className="text-white/60 text-xs">Fast & Reliable Delivery</p>
                    </div>
                  </div>

                  {/* Progress indicator in header */}
                  <div className="flex items-center gap-3">
                    <div className="text-white/80 text-sm font-medium">
                      {Math.round(getFormProgress())}% Complete
                    </div>
                    <div className="w-24 h-2 bg-white/20 rounded-full overflow-hidden">
                      <motion.div
                        className="h-full bg-gradient-to-r from-emerald-400 to-cyan-400"
                        animate={{ width: `${getFormProgress()}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Main Content */}
        <div className="relative z-10 min-h-screen">
          {/* Hero Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.2, ease: "easeInOut" }}
            className="min-h-screen flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8"
          >
            <div className="text-center max-w-4xl mx-auto mb-16">
              {/* Logo and Brand */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="mb-8"
              >
                <div className="flex items-center justify-center gap-6 mb-6">
                  <div className="relative">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                    >
                      <Logo size="lg" />
                    </motion.div>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                      className="absolute -inset-4 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full opacity-20 blur-xl"
                    />
                  </div>
                  <div className="text-left">
                    <motion.h1
                      className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent mb-2"
                      animate={{
                        backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
                      }}
                      transition={{ duration: 5, repeat: Infinity }}
                    >
                      Send Package
                    </motion.h1>
                    <p className="text-white/70 text-lg font-medium">Fast & Reliable Delivery Service</p>
                  </div>
                </div>
              </motion.div>

              {/* Enhanced Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
              >
                {[
                  { icon: <Zap className="text-yellow-400" size={24} />, label: "Express Delivery", value: "Same Day" },
                  { icon: <Shield className="text-green-400" size={24} />, label: "Secure & Insured", value: "100% Safe" },
                  { icon: <Star className="text-purple-400" size={24} />, label: "Customer Rating", value: "4.9/5" }
                ].map((stat, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.05, y: -5 }}
                    className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20"
                  >
                    <div className="flex items-center justify-center mb-3">
                      {stat.icon}
                    </div>
                    <div className="text-white/90 font-bold text-lg mb-1">{stat.value}</div>
                    <div className="text-white/60 text-sm">{stat.label}</div>
                  </motion.div>
                ))}
              </motion.div>

              {/* Scroll Indicator */}
              <motion.div
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="flex flex-col items-center gap-2 text-white/60"
              >
                <span className="text-sm font-medium">Fill out the form below</span>
                <ChevronRight className="rotate-90" size={20} />
              </motion.div>
            </div>
          </motion.div>

          {/* Form Section */}
          <div className="px-4 sm:px-6 lg:px-8 pb-20">
            <div className="max-w-4xl mx-auto">{/* Form content will continue here */}

              {/* Enhanced Form Sections */}
              <div className="space-y-8">
                {/* Progress Steps */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20"
                >
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-white font-bold text-lg">Package Request Progress</h3>
                    <div className="text-white/80 text-sm font-medium">
                      {Math.round(getFormProgress())}% Complete
                    </div>
                  </div>
                  <div className="w-full h-3 bg-white/20 rounded-full overflow-hidden">
                    <motion.div
                      className="h-full bg-gradient-to-r from-emerald-400 to-cyan-400"
                      animate={{ width: `${getFormProgress()}%` }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>
                  <div className="flex justify-between mt-3 text-xs text-white/60">
                    <span>Sender</span>
                    <span>Locations</span>
                    <span>Receiver</span>
                    <span>Package</span>
                    <span>Payment</span>
                  </div>
                </motion.div>

                {/* Sender Information Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.15 }}
                >
                  <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden">
                    <div className="p-6 border-b border-white/20">
                      <div className="flex items-center gap-4">
                        <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-blue-500 to-indigo-500 flex items-center justify-center">
                          <User className="text-white" size={28} />
                        </div>
                        <div>
                          <h3 className="font-bold text-xl text-white mb-1">👤 Sender Information</h3>
                          <p className="text-white/70 text-sm">Your contact details for pickup coordination</p>
                        </div>
                      </div>
                    </div>

                    <div className="p-6 space-y-6">
                      <div>
                        <label className="block text-sm font-semibold text-white mb-3">
                          Your Full Name <span className="text-red-400">*</span>
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            placeholder="e.g. Ahmad Samer"
                            value={formData.senderName}
                            onChange={(e) => updateField('senderName', e.target.value)}
                            className="w-full px-4 py-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent backdrop-blur-sm"
                          />
                          {formData.senderName.trim() && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="absolute right-3 top-1/2 -translate-y-1/2"
                            >
                              <CheckCircle className="text-blue-400" size={20} />
                            </motion.div>
                          )}
                        </div>
                      </div>

                      <div>
                        <PhoneInput
                          value={formData.senderPhone}
                          onChange={(value) => updateField('senderPhone', value)}
                          label="Your Phone Number"
                          placeholder="Enter your phone number"
                          required
                        />
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Locations Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.25 }}
                >
                  <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden">
                    <div className="p-6 border-b border-white/20">
                      <div className="flex items-center gap-4">
                        <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-emerald-500 to-cyan-500 flex items-center justify-center">
                          <MapPin className="text-white" size={28} />
                        </div>
                        <div>
                          <h3 className="font-bold text-xl text-white mb-1">📍 Delivery Locations</h3>
                          <p className="text-white/70 text-sm">Set your pickup and delivery destinations</p>
                        </div>
                      </div>
                    </div>

                    <div className="p-6 space-y-6">
                      {/* Pickup Location */}
                      <div>
                        <label className="block text-sm font-semibold text-white mb-3">
                          Pickup Location <span className="text-red-400">*</span>
                        </label>
                        <div className="flex gap-3">
                          <div className="flex-1 relative">
                            <input
                              type="text"
                              value={formData.pickup?.address || ''}
                              placeholder="Click 'Select on Map' to choose pickup location"
                              readOnly
                              className="w-full px-4 py-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-transparent backdrop-blur-sm cursor-pointer"
                              onClick={() => handleLocationSelect('pickup')}
                            />
                            {formData.pickup && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="absolute right-3 top-1/2 -translate-y-1/2"
                              >
                                <CheckCircle className="text-emerald-400" size={20} />
                              </motion.div>
                            )}
                          </div>
                          <motion.button
                            onClick={() => handleLocationSelect('pickup')}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="px-6 py-4 bg-gradient-to-r from-emerald-500 to-cyan-500 hover:from-emerald-600 hover:to-cyan-600 text-white font-semibold rounded-xl transition-all duration-200 flex items-center gap-2"
                          >
                            <Map size={18} />
                            Select on Map
                          </motion.button>
                        </div>
                        {formData.pickup && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="mt-2 text-emerald-400 text-sm flex items-center gap-2"
                          >
                            <MapPin size={14} />
                            <span>Pickup location confirmed</span>
                          </motion.div>
                        )}
                      </div>

                      {/* Dropoff Location */}
                      <div>
                        <label className="block text-sm font-semibold text-white mb-3">
                          Drop-off Location <span className="text-red-400">*</span>
                        </label>
                        <div className="flex gap-3">
                          <div className="flex-1 relative">
                            <input
                              type="text"
                              value={formData.dropoff?.address || ''}
                              placeholder="Click 'Select on Map' to choose delivery location"
                              readOnly
                              className="w-full px-4 py-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent backdrop-blur-sm cursor-pointer"
                              onClick={() => handleLocationSelect('dropoff')}
                            />
                            {formData.dropoff && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="absolute right-3 top-1/2 -translate-y-1/2"
                              >
                                <CheckCircle className="text-purple-400" size={20} />
                              </motion.div>
                            )}
                          </div>
                          <motion.button
                            onClick={() => handleLocationSelect('dropoff')}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="px-6 py-4 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold rounded-xl transition-all duration-200 flex items-center gap-2"
                          >
                            <Map size={18} />
                            Select on Map
                          </motion.button>
                        </div>
                        {formData.dropoff && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="mt-2 text-purple-400 text-sm flex items-center gap-2"
                          >
                            <MapPin size={14} />
                            <span>Drop-off location confirmed</span>
                          </motion.div>
                        )}
                      </div>

                      {/* Enhanced Route Visualization */}
                      <AnimatePresence>
                        {formData.pickup && formData.dropoff && (
                          <motion.div
                            initial={{ opacity: 0, height: 0, scale: 0.9 }}
                            animate={{ opacity: 1, height: 'auto', scale: 1 }}
                            exit={{ opacity: 0, height: 0, scale: 0.9 }}
                            transition={{ duration: 0.5 }}
                            className="bg-gradient-to-r from-emerald-500/20 to-purple-500/20 border border-emerald-400/30 rounded-xl p-6"
                          >
                            <div className="flex items-center justify-between mb-4">
                              <div className="flex items-center gap-3">
                                <div className="w-4 h-4 bg-emerald-400 rounded-full animate-pulse"></div>
                                <span className="text-white font-semibold">Route Confirmed</span>
                                <Route className="text-white/70" size={16} />
                              </div>
                              <div className="flex items-center gap-2 text-white/80 text-sm">
                                <Timer size={16} />
                                <span>Est. {estimatedTime || '2-4 hours'}</span>
                              </div>
                            </div>

                            {/* Route Details */}
                            <div className="space-y-3">
                              <div className="flex items-center gap-3 text-sm">
                                <div className="w-3 h-3 bg-emerald-400 rounded-full"></div>
                                <span className="text-white/90">From: {formData.pickup.address}</span>
                              </div>
                              <div className="flex items-center gap-3 text-sm">
                                <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
                                <span className="text-white/90">To: {formData.dropoff.address}</span>
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </div>
                </motion.div>

                {/* Receiver Info Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.35 }}
                >
                  <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden">
                    <div className="p-6 border-b border-white/20">
                      <div className="flex items-center gap-4">
                        <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center">
                          <User className="text-white" size={28} />
                        </div>
                        <div>
                          <h3 className="font-bold text-xl text-white mb-1">👤 Receiver Information</h3>
                          <p className="text-white/70 text-sm">Who will receive this package?</p>
                        </div>
                      </div>
                    </div>

                    <div className="p-6 space-y-6">
                      <div>
                        <label className="block text-sm font-semibold text-white mb-3">
                          Full Name <span className="text-red-400">*</span>
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            placeholder="e.g. Ahmad Jaber"
                            value={formData.receiverName}
                            onChange={(e) => updateField('receiverName', e.target.value)}
                            className="w-full px-4 py-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent backdrop-blur-sm"
                          />
                          {formData.receiverName.trim() && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="absolute right-3 top-1/2 -translate-y-1/2"
                            >
                              <CheckCircle className="text-blue-400" size={20} />
                            </motion.div>
                          )}
                        </div>
                      </div>

                      <div>
                        <PhoneInput
                          value={formData.receiverPhone}
                          onChange={(value) => updateField('receiverPhone', value)}
                          label="Phone Number"
                          placeholder="Enter receiver phone number"
                          required
                        />
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Package Details Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden">
                    <div className="p-6 border-b border-white/20">
                      <div className="flex items-center gap-4">
                        <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-orange-500 to-pink-500 flex items-center justify-center">
                          <Package className="text-white" size={28} />
                        </div>
                        <div>
                          <h3 className="font-bold text-xl text-white mb-1">📦 Package Details</h3>
                          <p className="text-white/70 text-sm">Tell us about your package</p>
                        </div>
                      </div>
                    </div>

                    <div className="p-6 space-y-6">
                      {/* Package Type Selection */}
                      <div>
                        <label className="block text-sm font-semibold text-white mb-3">
                          Package Type <span className="text-red-400">*</span>
                        </label>

                        {/* Selected Package Type Display */}
                        {formData.packageType ? (
                          <motion.div
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="mb-4"
                          >
                            {(() => {
                              const selectedType = packageTypes.find(type => type.id === formData.packageType);
                              return selectedType ? (
                                <div className="bg-gradient-to-r from-orange-500/20 to-pink-500/20 border border-orange-400/30 rounded-xl p-4">
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                      <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-pink-500 rounded-xl flex items-center justify-center">
                                        {selectedType.icon}
                                      </div>
                                      <div>
                                        <div className="text-white font-semibold">{selectedType.name}</div>
                                        <div className="text-white/70 text-sm">{selectedType.description}</div>
                                      </div>
                                    </div>
                                    <motion.button
                                      onClick={() => setShowPackageTypes(true)}
                                      whileHover={{ scale: 1.05 }}
                                      className="text-white/80 hover:text-white text-sm font-medium"
                                    >
                                      Change
                                    </motion.button>
                                  </div>
                                </div>
                              ) : null;
                            })()}
                          </motion.div>
                        ) : (
                          <motion.button
                            onClick={() => setShowPackageTypes(true)}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            className="w-full p-4 bg-white/10 border border-white/30 rounded-xl text-white/70 hover:text-white hover:border-white/50 transition-all duration-200 flex items-center justify-between"
                          >
                            <span>Select package type</span>
                            <ChevronDown size={20} />
                          </motion.button>
                        )}

                        {/* Package Types Grid */}
                        <AnimatePresence>
                          {showPackageTypes && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              className="mt-4"
                            >
                              {loading ? (
                                <div className="flex items-center justify-center py-8">
                                  <motion.div
                                    animate={{ rotate: 360 }}
                                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                    className="w-8 h-8 border-3 border-white/30 border-t-white rounded-full"
                                  />
                                  <span className="ml-3 text-white/70">Loading package types...</span>
                                </div>
                              ) : packageTypes.length > 0 ? (
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                                  {packageTypes.map((type, index) => (
                                <motion.button
                                  key={type.id}
                                  initial={{ opacity: 0, y: 20 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ delay: index * 0.1 }}
                                  onClick={() => handlePackageTypeSelect(type.id)}
                                  whileHover={{ scale: 1.05, y: -2 }}
                                  whileTap={{ scale: 0.95 }}
                                  className="bg-white/10 hover:bg-white/20 border border-white/30 hover:border-white/50 rounded-xl p-4 text-left transition-all duration-200"
                                >
                                  <div className="flex flex-col items-center text-center gap-2">
                                    <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-pink-500 rounded-xl flex items-center justify-center">
                                      {type.icon}
                                    </div>
                                    <div>
                                      <div className="text-white font-semibold text-sm">{type.name}</div>
                                      <div className="text-white/60 text-xs">{type.description}</div>
                                      <div className="text-orange-400 text-xs font-medium mt-1">
                                        ${type.basePrice} • {type.estimatedTime}
                                      </div>
                                    </div>
                                  </div>
                                </motion.button>
                              ))}
                                </div>
                              ) : (
                                <div className="text-center py-8 text-white/60">
                                  No package types available
                                </div>
                              )}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>

                      {/* Package Description */}
                      <div>
                        <label className="block text-sm font-semibold text-white mb-3">
                          Package Description <span className="text-red-400">*</span>
                        </label>
                        <div className="relative">
                          <textarea
                            placeholder="Describe what's inside the package (e.g., laptop, documents, clothes...)"
                            value={formData.packageDescription}
                            onChange={(e) => updateField('packageDescription', e.target.value)}
                            rows={3}
                            className="w-full px-4 py-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent backdrop-blur-sm resize-none"
                          />
                          {formData.packageDescription.trim() && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="absolute right-3 top-4"
                            >
                              <CheckCircle className="text-orange-400" size={20} />
                            </motion.div>
                          )}
                        </div>
                      </div>

                      {/* Package Size and Weight */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Package Size */}
                        <div>
                          <label className="block text-sm font-semibold text-white mb-3">
                            Package Size <span className="text-red-400">*</span>
                          </label>
                          <div className="space-y-2">
                            {packageSizes.map((size) => (
                              <motion.button
                                key={size.id}
                                onClick={() => updateField('packageSize', size.id)}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                className={`w-full p-3 rounded-xl border transition-all duration-200 text-left ${
                                  formData.packageSize === size.id
                                    ? 'bg-gradient-to-r from-orange-500/30 to-pink-500/30 border-orange-400 text-white'
                                    : 'bg-white/10 border-white/30 text-white/80 hover:text-white hover:border-white/50'
                                }`}
                              >
                                <div className="font-semibold text-sm">{size.name}</div>
                                <div className="text-xs opacity-80">{size.description}</div>
                              </motion.button>
                            ))}
                          </div>
                        </div>

                        {/* Package Weight */}
                        <div>
                          <label className="block text-sm font-semibold text-white mb-3">
                            Approximate Weight <span className="text-red-400">*</span>
                          </label>
                          <div className="relative">
                            <input
                              type="text"
                              placeholder="e.g., 2.5 kg"
                              value={formData.packageWeight}
                              onChange={(e) => handleWeightInput(e.target.value)}
                              className="w-full px-4 py-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-pink-400 focus:border-transparent backdrop-blur-sm"
                            />
                            <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 text-sm">
                              kg
                            </div>
                            {formData.packageWeight.trim() && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="absolute right-12 top-1/2 -translate-y-1/2"
                              >
                                <CheckCircle className="text-pink-400" size={20} />
                              </motion.div>
                            )}
                          </div>
                          {formData.packageWeight && parseFloat(formData.packageWeight) < 0.1 && (
                            <motion.div
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-orange-400 text-sm flex items-center gap-2"
                            >
                              <AlertCircle size={14} />
                              Minimum weight is 0.1 kg
                            </motion.div>
                          )}
                        </div>
                      </div>

                      {/* Package Value and Fragile */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Package Value */}
                        <div>
                          <label className="block text-sm font-semibold text-white mb-3">
                            Package Value (ILS)
                          </label>
                          <div className="relative">
                            <div className="absolute left-3 top-1/2 -translate-y-1/2 flex items-center">
                              <span className="text-white/50 text-lg">₪</span>
                            </div>
                            <input
                              type="text"
                              placeholder="Estimated value in ₪"
                              value={formData.packageValue || ''}
                              onChange={(e) => handleValueInput(e.target.value)}
                              className="w-full pl-12 pr-4 py-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-transparent backdrop-blur-sm"
                            />
                            <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 text-sm">
                              ₪
                            </div>
                          </div>
                          <p className="text-white/60 text-xs mt-2">Used for insurance calculation</p>
                        </div>

                        {/* Fragile Option - Enhanced */}
                        <div>
                          <label className="block text-sm font-semibold text-white mb-3">
                            Special Handling
                          </label>
                          <motion.div
                            className={`relative p-6 rounded-2xl border-2 transition-all duration-500 ${
                              formData.isFragile
                                ? 'bg-gradient-to-r from-orange-500/20 to-red-500/20 border-orange-400/50 shadow-lg shadow-orange-500/25'
                                : 'bg-white/5 border-white/20 hover:border-orange-400/30 hover:bg-orange-500/5'
                            }`}
                          >
                            {/* Animated background pattern when checked */}
                            {formData.isFragile && (
                              <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 0.1 }}
                                className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl"
                              />
                            )}
                            
                            <div className="relative z-10">
                              <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center gap-3">
                                  <motion.div
                                    animate={formData.isFragile ? { 
                                      scale: [1, 1.2, 1],
                                      rotate: [0, 5, -5, 0]
                                    } : {}}
                                    transition={{ duration: 0.5, repeat: formData.isFragile ? Infinity : 0 }}
                                    className={`p-3 rounded-xl ${
                                      formData.isFragile 
                                        ? 'bg-gradient-to-r from-orange-500 to-red-500 shadow-lg shadow-orange-500/50' 
                                        : 'bg-white/10'
                                    }`}
                                  >
                                    <AlertCircle size={20} className="text-white" />
                                  </motion.div>
                                  <div>
                                    <h3 className="text-lg font-bold text-white">Fragile Items</h3>
                                    <p className="text-white/70 text-sm">Special handling required</p>
                                  </div>
                                </div>
                                
                                <motion.div
                                  animate={formData.isFragile ? { scale: [1, 1.1, 1] } : {}}
                                  transition={{ duration: 0.3 }}
                                  className="flex items-center gap-2"
                                >
                                  <motion.button
                                    onClick={() => updateField('isFragile', !formData.isFragile)}
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    className="relative cursor-pointer"
                                  >
                                    <div className={`w-12 h-6 rounded-full transition-all duration-300 ${
                                      formData.isFragile 
                                        ? 'bg-gradient-to-r from-orange-500 to-red-500' 
                                        : 'bg-white/20'
                                    }`}>
                                      <motion.div
                                        animate={{ x: formData.isFragile ? 24 : 0 }}
                                        className="w-6 h-6 bg-white rounded-full shadow-lg transform translate-x-0.5 translate-y-0.5"
                                      />
                                    </div>
                                  </motion.button>
                                  {formData.isFragile && (
                                    <motion.div
                                      initial={{ opacity: 0, scale: 0 }}
                                      animate={{ opacity: 1, scale: 1 }}
                                      className="text-orange-400 font-semibold text-sm"
                                    >
                                      +₪3
                                    </motion.div>
                                  )}
                                </motion.div>
                              </div>
                              
                              <div className="space-y-3">
                                <div className="flex items-center gap-3 text-white/80 text-sm">
                                  <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                                  <span>Extra careful handling</span>
                                </div>
                                <div className="flex items-center gap-3 text-white/80 text-sm">
                                  <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                                  <span>Special packaging requirements</span>
                                </div>
                                <div className="flex items-center gap-3 text-white/80 text-sm">
                                  <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                                  <span>Priority loading and unloading</span>
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        </div>
                      </div>

                      {/* Delivery Options */}
                      <div>
                        <label className="block text-sm font-semibold text-white mb-3">
                          Delivery Speed
                        </label>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                          {[
                            { id: 'standard', name: 'Standard', time: '4-8 hours', multiplier: 1, icon: <Clock size={18} /> },
                            { id: 'express', name: 'Express', time: '2-4 hours', multiplier: 1.5, icon: <Zap size={18} /> },
                            { id: 'urgent', name: 'Urgent', time: '1-2 hours', multiplier: 2, icon: <Timer size={18} /> }
                          ].map((option) => (
                            <motion.button
                              key={option.id}
                              onClick={() => updateField('urgency', option.id)}
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                              className={`p-4 rounded-xl border transition-all duration-200 ${
                                formData.urgency === option.id
                                  ? 'bg-gradient-to-r from-orange-500/30 to-pink-500/30 border-orange-400 text-white'
                                  : 'bg-white/10 border-white/30 text-white/80 hover:text-white hover:border-white/50'
                              }`}
                            >
                              <div className="flex items-center gap-2 mb-2">
                                {option.icon}
                                <span className="font-semibold">{option.name}</span>
                              </div>
                              <div className="text-sm opacity-80">{option.time}</div>
                              <div className="text-xs mt-1">+{Math.round((option.multiplier - 1) * 100)}% cost</div>
                            </motion.button>
                          ))}
                        </div>
                      </div>

                      {/* Insurance Option - Enhanced */}
                      <div>
                        <motion.div
                          className={`relative p-6 rounded-2xl border-2 transition-all duration-500 ${
                            formData.insurance
                              ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-400/50 shadow-lg shadow-blue-500/25'
                              : 'bg-white/5 border-white/20 hover:border-blue-400/30 hover:bg-blue-500/5'
                          }`}
                        >
                          {/* Animated background pattern when checked */}
                          {formData.insurance && (
                            <motion.div
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 0.1 }}
                              className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl"
                            />
                          )}
                          
                          <div className="relative z-10">
                            <div className="flex items-center justify-between mb-4">
                              <div className="flex items-center gap-3">
                                <motion.div
                                  animate={formData.insurance ? { 
                                    scale: [1, 1.2, 1],
                                    rotate: [0, -5, 5, 0]
                                  } : {}}
                                  transition={{ duration: 0.5, repeat: formData.insurance ? Infinity : 0 }}
                                  className={`p-3 rounded-xl ${
                                    formData.insurance 
                                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 shadow-lg shadow-blue-500/50' 
                                      : 'bg-white/10'
                                  }`}
                                >
                                  <Shield size={20} className="text-white" />
                                </motion.div>
                                <div>
                                  <h3 className="text-lg font-bold text-white">Insurance Coverage</h3>
                                  <p className="text-white/70 text-sm">Protect your valuable items</p>
                                </div>
                              </div>
                              
                              <motion.div
                                animate={formData.insurance ? { scale: [1, 1.1, 1] } : {}}
                                transition={{ duration: 0.3 }}
                                className="flex items-center gap-2"
                              >
                                <motion.button
                                  onClick={() => updateField('insurance', !formData.insurance)}
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  className="relative cursor-pointer"
                                >
                                  <div className={`w-12 h-6 rounded-full transition-all duration-300 ${
                                    formData.insurance 
                                      ? 'bg-gradient-to-r from-blue-500 to-purple-500' 
                                      : 'bg-white/20'
                                  }`}>
                                    <motion.div
                                      animate={{ x: formData.insurance ? 24 : 0 }}
                                      className="w-6 h-6 bg-white rounded-full shadow-lg transform translate-x-0.5 translate-y-0.5"
                                    />
                                  </div>
                                </motion.button>
                                {formData.insurance && formData.packageValue > 0 && (
                                  <motion.div
                                    initial={{ opacity: 0, scale: 0 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    className="text-blue-400 font-semibold text-sm"
                                  >
                                    +₪{Math.max(5, Math.round(formData.packageValue * 0.02))}
                                  </motion.div>
                                )}
                              </motion.div>
                            </div>
                            
                            <div className="space-y-3">
                              <div className="flex items-center gap-3 text-white/80 text-sm">
                                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                <span>Full coverage up to package value</span>
                              </div>
                              <div className="flex items-center gap-3 text-white/80 text-sm">
                                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                <span>2% of declared value (minimum ₪5)</span>
                              </div>
                              <div className="flex items-center gap-3 text-white/80 text-sm">
                                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                <span>Peace of mind for valuable items</span>
                              </div>
                            </div>
                            
                            {formData.packageValue > 0 && (
                              <motion.div
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="mt-4 p-3 bg-white/10 rounded-xl border border-white/20"
                              >
                                <div className="text-white/90 text-sm">
                                  <span className="font-semibold">Coverage:</span> Up to ₪{formData.packageValue}
                                </div>
                                <div className="text-white/70 text-xs mt-1">
                                  Premium: ₪{Math.max(5, Math.round(formData.packageValue * 0.02))}
                                </div>
                              </motion.div>
                            )}
                          </div>
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Payment & Scheduling Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.45 }}
                >
                  <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden">
                    <div className="p-6 border-b border-white/20">
                      <div className="flex items-center gap-4">
                        <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center">
                          <CreditCard className="text-white" size={28} />
                        </div>
                        <div>
                          <h3 className="font-bold text-xl text-white mb-1">💳 Payment & Scheduling</h3>
                          <p className="text-white/70 text-sm">Choose payment method and pickup time</p>
                        </div>
                      </div>
                    </div>

                    <div className="p-6 space-y-6">
                      {/* Payment Method */}
                      <div>
                        <label className="block text-sm font-semibold text-white mb-3">
                          Payment Method <span className="text-red-400">*</span>
                        </label>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                          {paymentMethods.map((method) => (
                            <motion.button
                              key={method.id}
                              onClick={() => updateField('paymentMethod', method.id)}
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                              className={`p-4 rounded-xl border transition-all duration-200 ${
                                formData.paymentMethod === method.id
                                  ? 'bg-gradient-to-r from-green-500/30 to-emerald-500/30 border-green-400 text-white'
                                  : 'bg-white/10 border-white/30 text-white/80 hover:text-white hover:border-white/50'
                              }`}
                            >
                              <div className="flex items-center gap-2 mb-2">
                                {method.icon}
                                <span className="font-semibold text-sm">{method.name}</span>
                              </div>
                              <div className="text-xs opacity-80">{method.description}</div>
                            </motion.button>
                          ))}
                        </div>
                      </div>

                      {/* Scheduled Pickup Time */}
                      <div>
                        <label className="block text-sm font-semibold text-white mb-3">
                          Preferred Pickup Time (optional)
                        </label>
                        <div className="relative">
                          <input
                            type="datetime-local"
                            value={formData.scheduledPickupTime ? new Date(formData.scheduledPickupTime.getTime() - formData.scheduledPickupTime.getTimezoneOffset() * 60000).toISOString().slice(0, 16) : ''}
                            onChange={(e) => updateField('scheduledPickupTime', e.target.value ? new Date(e.target.value) : null)}
                            min={new Date().toISOString().slice(0, 16)}
                            className="w-full px-4 py-4 bg-white/10 border border-white/30 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-transparent backdrop-blur-sm"
                          />
                        </div>
                        <p className="text-white/60 text-xs mt-2">Leave empty for immediate pickup</p>
                      </div>

                      {/* Notes */}
                      <div>
                        <label className="block text-sm font-semibold text-white mb-3">
                          Special Instructions (optional)
                        </label>
                        <textarea
                          placeholder="Any special instructions for the driver..."
                          value={formData.notes}
                          onChange={(e) => updateField('notes', e.target.value)}
                          rows={3}
                          className="w-full px-4 py-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-transparent backdrop-blur-sm resize-none"
                        />
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Cost Estimation */}
                <AnimatePresence>
                  {formData.pickup && formData.dropoff && formData.packageType && (
                    <motion.div
                      initial={{ opacity: 0, y: 20, scale: 0.9 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -20, scale: 0.9 }}
                      transition={{ duration: 0.5 }}
                      className="bg-gradient-to-r from-emerald-500/20 to-cyan-500/20 backdrop-blur-md rounded-2xl border border-emerald-400/30 overflow-hidden"
                    >
                      <div className="p-6">
                        <div className="flex items-center gap-4 mb-4">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-emerald-500 to-cyan-500 flex items-center justify-center">
                            <DollarSign className="text-white" size={24} />
                          </div>
                          <div>
                            <h3 className="font-bold text-lg text-white">💰 Cost Estimation</h3>
                            <p className="text-white/70 text-sm">Transparent pricing breakdown</p>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="flex justify-between items-center text-white/80">
                            <span>Base delivery fee</span>
                            <span>${packageTypes.find(t => t.id === formData.packageType)?.basePrice || 0}</span>
                          </div>

                          {formData.packageSize !== 'small' && (
                            <div className="flex justify-between items-center text-white/80">
                              <span>Size adjustment ({formData.packageSize})</span>
                              <span>+${Math.round(((packageSizes.find(s => s.id === formData.packageSize)?.multiplier || 1) - 1) * (packageTypes.find(t => t.id === formData.packageType)?.basePrice || 0) * 100) / 100}</span>
                            </div>
                          )}

                          {formData.urgency !== 'standard' && (
                            <div className="flex justify-between items-center text-white/80">
                              <span>{formData.urgency === 'express' ? 'Express' : 'Urgent'} surcharge</span>
                              <span>+${Math.round((packageTypes.find(t => t.id === formData.packageType)?.basePrice || 0) * (packageSizes.find(s => s.id === formData.packageSize)?.multiplier || 1) * (formData.urgency === 'express' ? 0.5 : 1) * 100) / 100}</span>
                            </div>
                          )}

                          {formData.isFragile && (
                            <div className="flex justify-between items-center text-white/80">
                              <span>Fragile handling</span>
                              <span>+$3</span>
                            </div>
                          )}

                          {formData.insurance && (
                            <div className="flex justify-between items-center text-white/80">
                              <span>Package insurance</span>
                              <span>+${Math.max(5, Math.round(formData.packageValue * 0.02 * 100) / 100)}</span>
                            </div>
                          )}

                          <div className="border-t border-white/20 pt-3">
                            <div className="flex justify-between items-center text-white font-bold text-lg">
                              <span>Total Estimated Cost</span>
                              <span className="text-emerald-400">{formatCurrency(estimatedCost)}</span>
                            </div>
                            <div className="text-white/60 text-sm mt-1">
                              Estimated delivery time: {estimatedTime}
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Enhanced Submit Button */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                  className="pt-6"
                >
                  <motion.button
                    onClick={handleSubmit}
                    disabled={!isFormValid() || isSubmitting}
                    whileHover={isFormValid() && !isSubmitting ? { scale: 1.02, y: -2 } : {}}
                    whileTap={isFormValid() && !isSubmitting ? { scale: 0.98 } : {}}
                    className={`w-full px-8 py-6 font-bold text-xl rounded-2xl transition-all duration-300 flex items-center justify-center gap-4 relative overflow-hidden ${
                      isFormValid() && !isSubmitting
                        ? 'bg-gradient-to-r from-emerald-500 to-cyan-500 hover:from-emerald-600 hover:to-cyan-600 text-white shadow-2xl'
                        : 'bg-white/20 text-white/50 cursor-not-allowed'
                    }`}
                  >
                    {/* Animated background for active state */}
                    {isFormValid() && !isSubmitting && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-emerald-400 to-cyan-400 opacity-0"
                        whileHover={{ opacity: 0.2 }}
                        transition={{ duration: 0.3 }}
                      />
                    )}

                    <div className="relative z-10 flex items-center gap-4">
                      {isSubmitting ? (
                        <>
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                            className="w-8 h-8 border-3 border-white/30 border-t-white rounded-full"
                          />
                          <span>Processing Request...</span>
                        </>
                      ) : (
                        <>
                          <Send size={28} />
                          <span>Confirm Package Request</span>
                          {estimatedCost > 0 && (
                            <div className="bg-white/20 px-3 py-1 rounded-full text-sm">
                              {formatCurrency(estimatedCost)}
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </motion.button>

                  {/* Form validation and error messages */}
                  <AnimatePresence>
                    {error && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="text-center mt-4"
                      >
                        <div className="inline-flex items-center gap-2 bg-red-500/20 border border-red-400/30 rounded-xl px-4 py-2 text-red-200">
                          <AlertCircle size={16} />
                          <span className="text-sm font-medium">{error}</span>
                        </div>
                      </motion.div>
                    )}
                    {!isFormValid() && !error && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="text-center mt-4"
                      >
                        <div className="inline-flex items-center gap-2 bg-orange-500/20 border border-orange-400/30 rounded-xl px-4 py-2 text-orange-200">
                          <AlertCircle size={16} />
                          <span className="text-sm font-medium">
                            Please complete all required fields to continue
                          </span>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  {/* Payment method hint */}
                  {isFormValid() && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-center mt-4"
                    >
                      <div className="inline-flex items-center gap-2 text-white/60 text-sm">
                        <CreditCard size={16} />
                        <span>Payment will be collected upon delivery</span>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SendPackagePage;