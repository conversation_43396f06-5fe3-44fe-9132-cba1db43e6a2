{"common": {"welcome": "Welcome", "back": "Back", "next": "Next", "previous": "Previous", "continue": "Continue", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "retry": "Retry", "close": "Close", "search": "Search", "filter": "Filter", "sort": "Sort", "submit": "Submit", "reset": "Reset", "send": "Send", "verify": "Verify", "done": "Done", "ok": "OK", "yes": "Yes", "no": "No", "allow": "Allow", "sending": "Sending...", "verifying": "Verifying...", "allFieldsRequired": "* All fields are required"}, "auth": {"login": "<PERSON><PERSON>", "signup": "Sign Up", "logout": "Logout", "welcomeBack": "Welcome Back!", "signInToContinue": "Sign in to continue your journey", "usernameOrEmail": "Username or Email", "enterUsernameOrEmail": "Enter your username or email", "password": "Password", "enterPassword": "Enter your password", "forgotPassword": "Forgot Password? Click Here", "loginFailed": "Login Failed", "invalidCredentials": "Invalid email or password.", "networkError": "Network error occurred", "firstName": "First Name", "enterFirstName": "Enter your first name", "lastName": "Last Name", "enterLastName": "Enter your last name", "emailAddress": "Email Address", "enterEmailAddress": "Enter your email address", "phone": "Phone Number", "enterPhone": "Enter your phone number", "confirmPassword": "Confirm Password", "enterConfirmPassword": "Confirm your password", "username": "Username", "enterUsername": "Enter your username", "dateOfBirth": "Date of Birth", "gender": "Gender", "male": "Male", "female": "Female", "other": "Other", "address": "Address", "enterAddress": "Enter your address", "city": "City", "enterCity": "Enter your city", "country": "Country", "enterCountry": "Enter your country", "userType": "User Type", "customer": "Customer", "supplier": "Supplier", "customerDescription": "I want to order from suppliers", "supplierDescription": "I want to sell products/services", "storeName": "Store Name", "enterStoreName": "Enter your store name", "businessType": "Business Type", "enterBusinessType": "Enter your business type", "changePassword": "Change Password", "emailVerified": "<PERSON><PERSON>", "openHours": "Opening Hours", "enterOpenHours": "Enter your opening hours", "notifications": "Enable Notifications", "terms": "I accept the terms and conditions", "termsRequired": "Please accept the terms and conditions to continue.", "accountCreated": "Your account has been created successfully. Welcome to BolTalab!", "resetPassword": "Reset Password", "resetYourPassword": "Reset Your Password", "identityVerified": "Your identity has been verified. Enter your new password below.", "newPassword": "New Password", "enterNewPassword": "Enter your new password", "passwordResetSuccess": "Password reset successfully! Redirecting to login...", "forgotPasswordTitle": "Forgot Password", "forgotPasswordDescription": "Enter your email address and we'll send you a verification code to reset your password.", "enterEmail": "Enter your email address", "sendCode": "Send Verification Code", "verificationCode": "Verification Code", "enterVerificationCode": "Enter the 6-digit code sent to your email", "codeNotReceived": "Didn't receive the code?", "resendCode": "Resend Code", "verifyEmail": "<PERSON><PERSON><PERSON>", "emailVerificationTitle": "Email Verification", "emailVerificationDescription": "We've sent a verification code to your email address. Please enter it below.", "emailVerifiedSuccess": "Email verified successfully! You can now login to your account.", "signingIn": "Signing In...", "signIn": "Sign In", "createAccount": "Create Account", "newToBolTalab": "New to BolTalab?", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailRequired": "Email is required", "invalidEmail": "Invalid email address", "phoneRequired": "Phone number is required", "confirmPasswordRequired": "Please confirm your password", "passwordsDoNotMatch": "Passwords do not match", "usernameRequired": "Username is required", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 6 characters", "dateOfBirthRequired": "Date of birth is required", "genderRequired": "Gender is required", "selectGender": "Select gender", "addressRequired": "Address is required", "cityRequired": "City is required", "countryRequired": "Country is required", "storeNameRequired": "Store name is required for suppliers", "businessTypeRequired": "Business type is required for suppliers", "selectBusinessType": "Select business type", "restaurant": "Restaurant", "groceryStore": "Grocery Store", "pharmacy": "Pharmacy", "electronics": "Electronics", "clothing": "Clothing", "openHoursRequired": "Opening hours are required for suppliers", "passwordRecovery": "Password Recovery", "passwordRecoveryDescription": "Don't worry! We'll help you regain access to your account", "initializingSecurity": "Initializing Security...", "enterEmailForReset": "Enter your email to receive a secure reset code", "resetCodeSent": "Reset code sent! Please check your email.", "failedToSendCode": "Failed to send reset code. Please try again.", "unexpectedError": "An unexpected error occurred. Please try again.", "sendingResetCode": "Sending Reset Code...", "sendResetCode": "Send Reset Code", "rememberPassword": "Remember your password?", "backToLogin": "Back to Login", "securingAccount": "Securing Your Account...", "securePasswordReset": "Secure Password Reset", "almostThere": "Almost there! Let's create your new secure password", "enterCodeAndPassword": "Enter verification code and create your new password", "enterSixDigitCode": "Enter the 6-digit code sent to your email", "confirmNewPassword": "Confirm New Password", "confirmNewPasswordPlaceholder": "Confirm your new password", "passwordSecurity": "Password Security", "sixPlusCharacters": "6+ characters", "uppercaseLetter": "Uppercase letter", "number": "Number", "specialCharacter": "Special character", "completeSixDigitCode": "Please enter the complete 6-digit code", "invalidResetCode": "Invalid reset code", "failedToResetPassword": "Failed to reset password. Please try again.", "resettingPassword": "Resetting Password...", "resetPasswordButton": "Reset Password", "needToGoBack": "Need to go back?", "backToEmailVerification": "Back to Email Verification", "preparingVerification": "Preparing verification...", "emailVerificationReady": "Email Verification Ready! 📧", "verificationCodeWaiting": "Your verification code is waiting for you!", "verifyYourEmail": "Verify Your Email", "pleaseVerifyEmail": "Please verify your email address to continue logging in. We've sent a 6-digit verification code to", "weSentCodeTo": "We've sent a 6-digit verification code to", "verificationCodeLabel": "Verification Code", "enterSixDigitCodeSent": "Enter the 6-digit code sent to your email", "invalidVerificationCode": "Invalid verification code. Please try again.", "emailVerifiedSuccessRedirect": "Email verified successfully! Redirecting to login...", "verifyingEmail": "Verifying...", "verifyEmailButton": "<PERSON><PERSON><PERSON>", "didntReceiveCode": "Didn't receive the code?", "sendingCode": "Sending...", "resendCodeButton": "Resend Code", "verificationCodeSent": "Verification code sent! Please check your email.", "failedToResendCode": "Failed to resend code. Please try again.", "backToSignup": "Back to Signup"}, "navigation": {"home": "Home", "orders": "Orders", "packages": "Packages", "profile": "Profile", "suppliers": "Suppliers", "analytics": "Analytics", "notifications": "Notifications", "products": "Products", "settings": "Settings"}, "signup": {"personalInformation": "Personal Information", "personalInformationDesc": "Let's start with the basics about you", "firstNamePlaceholder": "<PERSON>", "lastNamePlaceholder": "<PERSON><PERSON>", "emailPlaceholder": "<EMAIL>", "phonePlaceholder": "+962 7X XXX XXXX", "usernamePlaceholder": "johndoe", "securitySetup": "Security Setup", "securitySetupDesc": "Create your secure login credentials", "personalDetails": "Personal Details", "personalDetailsDesc": "Tell us more about yourself", "businessInformation": "Business Information", "businessInformationDesc": "Tell us about your business", "chooseRole": "Choose Your Role", "chooseRoleDesc": "Select how you want to use BolTalab", "orderAndReceive": "Order and receive deliveries", "provideProducts": "Provide products and services", "enableLocationServices": "Enable Location Services 📍", "enableLocationServicesDesc": "Help us provide lightning-fast delivery services by sharing your location. This is optional but highly recommended for the best experience.", "locationDetected": "Location detected successfully! 🎉", "coordinates": "Coordinates", "readyForDeliveries": "Ready for ultra-fast deliveries!", "allowLocationAccess": "Allow Location Access", "gettingLocation": "Getting Location...", "skipForNow": "Skip for Now", "setStoreLocation": "Set Your Store Location 🏪", "setStoreLocationDesc": "Help customers find your store easily by sharing your business location. This will boost your visibility and attract more customers.", "storeLocationSet": "Store location set successfully! 🎯", "readyToAttract": "Ready to attract customers!", "setStoreLocationButton": "Set Store Location", "businessTips": "Business Tips", "businessTipsDesc1": "• Choose a clear, memorable store name", "businessTipsDesc2": "• Select the most accurate business category", "businessTipsDesc3": "• Provide accurate opening hours for better customer experience", "passwordSecurity": "Password Security", "characters": "6+ characters", "uppercaseLetter": "Uppercase letter", "number": "Number", "specialCharacter": "Special character", "stepOf": "Step {{current}} of {{total}} - Your journey to excellence", "createAccountTitle": "Create Account", "creatingAccount": "Creating Account...", "alreadyHaveAccount": "Already have an account?", "preparingJourney": "Preparing Your Journey...", "settingUpExperience": "Setting up the ultimate signup experience", "joinTheElite": "Join the Elite! 🚀", "unlockDeliveries": "Create your account and unlock lightning-fast deliveries", "nextStep": "Next Step", "previous": "Previous", "addressPlaceholder": "Street address", "cityPlaceholder": "Amman", "countryPlaceholder": "Jordan", "storeNamePlaceholder": "Your business name", "openHoursPlaceholder": "e.g., 9:00 AM - 10:00 PM"}, "language": {"english": "English", "arabic": "العربية", "changeLanguage": "Change Language", "selectLanguage": "Select Language"}}