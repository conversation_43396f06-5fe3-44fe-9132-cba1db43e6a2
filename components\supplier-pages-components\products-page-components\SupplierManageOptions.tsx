import { useEffect, useState, useCallback } from 'react';
import { Dimensions, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  ScrollView,
  YStack,
  XStack,
  Input,
  Text,
  Button,
  Separator,
  Card,
  Select,
  Sheet,
  H3,
  H4,
  <PERSON><PERSON>,
} from 'tamagui';
import { useSupplierProducts } from './useSupplierProducts';
import { useLocalSearchParams, useRouter } from 'expo-router';
// Simple UUID generator to avoid native module issues
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};
import { MotiView } from 'moti';

type CustomOption = {
  id: string;
  title: string;
  type: 'text' | 'number' | 'select' | 'multi-select';
  values?: string[];
};

type ValidationErrors = {
  name?: string;
  price?: string;
  value?: string;
  customTitle?: string;
  customValue?: string;
};

export default function SupplierManageOptions() {
  const { id: productId } = useLocalSearchParams<{ id: string }>();
  const { products, updateProduct } = useSupplierProducts();
  const product = products.find((p) => p.id === productId);
  const router = useRouter();
  const windowWidth = Dimensions.get('window').width;



  const [input, setInput] = useState({
    name: '',
    price: '',
    value: '',
    customTitle: '',
    customValue: '',
  });
  const [customOptions, setCustomOptions] = useState<CustomOption[]>([]);
  const [customType, setCustomType] = useState<CustomOption['type']>('text');
  const [typeDropdownOpen, setTypeDropdownOpen] = useState(false);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [deleteConfirmSheet, setDeleteConfirmSheet] = useState<{
    open: boolean;
    type: string;
    item: any;
    onConfirm: () => void;
  }>({ open: false, type: '', item: null, onConfirm: () => {} });

  const isRestaurant = !!product?.restaurantOptions; // later by backend
  const isClothing = !!product?.clothingOptions; // later by backend

  // Validation functions
  const validateInput = useCallback((type: string): ValidationErrors => {
    const newErrors: ValidationErrors = {};

    if (type === 'addition' || type === 'side') {
      if (!input.name.trim()) {
        newErrors.name = 'Name is required';
      } else if (input.name.trim().length < 2) {
        newErrors.name = 'Name must be at least 2 characters';
      } else if (input.name.trim().length > 50) {
        newErrors.name = 'Name must be less than 50 characters';
      }

      if (!input.price) {
        newErrors.price = 'Price is required';
      } else if (isNaN(parseFloat(input.price))) {
        newErrors.price = 'Price must be a valid number';
      } else if (parseFloat(input.price) < 0) {
        newErrors.price = 'Price cannot be negative';
      } else if (parseFloat(input.price) > 10000) {
        newErrors.price = 'Price seems too high (max: 10,000)';
      }
    } else if (['without', 'sizes', 'colors'].includes(type)) {
      if (!input.value.trim()) {
        newErrors.value = 'Value is required';
      } else if (input.value.trim().length < 1) {
        newErrors.value = 'Value must not be empty';
      } else if (input.value.trim().length > 30) {
        newErrors.value = 'Value must be less than 30 characters';
      }
    } else if (type === 'gallery') {
      if (!input.value.trim()) {
        newErrors.value = 'URL is required';
      } else if (!isValidUrl(input.value.trim())) {
        newErrors.value = 'Please enter a valid URL';
      }
    } else if (type === 'custom') {
      if (!input.customTitle.trim()) {
        newErrors.customTitle = 'Title is required';
      } else if (input.customTitle.trim().length < 2) {
        newErrors.customTitle = 'Title must be at least 2 characters';
      } else if (input.customTitle.trim().length > 30) {
        newErrors.customTitle = 'Title must be less than 30 characters';
      }

      if ((customType === 'select' || customType === 'multi-select')) {
        if (!input.customValue.trim()) {
          newErrors.customValue = 'Value is required for select types';
        } else if (input.customValue.trim().length > 20) {
          newErrors.customValue = 'Value must be less than 20 characters';
        }
      }
    }

    return newErrors;
  }, [input, customType]);

  // URL validation helper
  const isValidUrl = (string: string): boolean => {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  };

  const clearInput = useCallback(() => {
    setInput({
      name: '',
      price: '',
      value: '',
      customTitle: '',
      customValue: '',
    });
    setErrors({});
  }, []);

  const showToast = useCallback((message: string, type: 'success' | 'error' = 'success') => {
    Alert.alert(type === 'success' ? 'Success' : 'Error', message);
  }, []);

  useEffect(() => {
    clearInput();
    const mappedOptions = (product?.customOptions || []).map((opt) => ({
      id: opt.id,
      title: opt.title,
      type: opt.type,
      values: opt.values ?? [],
    }));
    setCustomOptions(mappedOptions);
  }, [productId, clearInput]);

  const saveCustomOptions = useCallback(async (updated: CustomOption[]) => {
    try {
      setCustomOptions(updated);
      await updateProduct(productId, { customOptions: updated });
      showToast('Custom options updated successfully');
    } catch (error) {
      showToast('Failed to save custom options', 'error');
    }
  }, [productId, updateProduct, showToast]);

  const handleAdd = useCallback(async (type: string) => {
    const validationErrors = validateInput(type);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsLoading(true);

    try {
      if (type === 'addition' || type === 'side') {
        const newItem = { id: generateId(), name: input.name.trim(), price: parseFloat(input.price) };
        const currentItems = product?.restaurantOptions?.[type === 'addition' ? 'additions' : 'sides'] || [];

        const updatedProduct = {
          ...product,
          restaurantOptions: {
            additions: product?.restaurantOptions?.additions || [],
            without: product?.restaurantOptions?.without || [],
            sides: product?.restaurantOptions?.sides || [],
            ...product?.restaurantOptions,
            [type === 'addition' ? 'additions' : 'sides']: [...currentItems, newItem],
          },
        };

        await updateProduct(productId, updatedProduct);
        showToast(`${type === 'addition' ? 'Addition' : 'Side dish'} added successfully`);

      } else if (type === 'without') {
        const trimmedValue = input.value.trim();
        const existingWithout = product?.restaurantOptions?.without || [];

        if (existingWithout.includes(trimmedValue)) {
          setErrors({ value: 'This item already exists' });
          setIsLoading(false);
          return;
        }

        await updateProduct(productId, {
          ...product,
          restaurantOptions: {
            additions: product?.restaurantOptions?.additions || [],
            sides: product?.restaurantOptions?.sides || [],
            ...product?.restaurantOptions,
            without: [...existingWithout, trimmedValue],
          },
        });
        showToast('Without option added successfully');

      } else if (['sizes', 'colors', 'gallery'].includes(type)) {
        const trimmedValue = input.value.trim();
        const currentOptions = product?.clothingOptions || { sizes: [], colors: [], gallery: [] };
        const existingValues = currentOptions[type as 'sizes' | 'colors' | 'gallery'] || [];

        if (existingValues.includes(trimmedValue)) {
          setErrors({ value: 'This item already exists' });
          setIsLoading(false);
          return;
        }

        await updateProduct(productId, {
          ...product,
          clothingOptions: {
            ...currentOptions,
            [type]: [...existingValues, trimmedValue],
          },
        });
        showToast(`${type.charAt(0).toUpperCase() + type.slice(1)} added successfully`);
      }

      clearInput();

    } catch (error) {
      showToast('Failed to add item', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [input, validateInput, productId, product, updateProduct, showToast, clearInput]);

  const confirmDelete = useCallback((type: string, item: any) => {
    setDeleteConfirmSheet({
      open: true,
      type,
      item,
      onConfirm: () => handleDelete(type, item.id || item)
    });
  }, []);

  const handleDelete = useCallback(async (type: string, idOrValue: string) => {
    try {
      if (type === 'addition' || type === 'side') {
        const currentItems = product?.restaurantOptions?.[type === 'addition' ? 'additions' : 'sides'] || [];
        const updated = currentItems.filter((a) => a.id !== idOrValue);

        await updateProduct(productId, {
          restaurantOptions: {
            ...product?.restaurantOptions,
            [type === 'addition' ? 'additions' : 'sides']: updated,
          },
        });

      } else if (['without', 'sizes', 'colors', 'gallery'].includes(type)) {
        if (isRestaurant && type === 'without') {
          const currentWithout = product?.restaurantOptions?.without || [];
          const updated = currentWithout.filter((v) => v !== idOrValue);

          await updateProduct(productId, {
            restaurantOptions: {
              ...product?.restaurantOptions,
              without: updated,
            },
          });
        } else {
          const currentOptions = product?.clothingOptions || { sizes: [], colors: [], gallery: [] };
          const currentValues = currentOptions[type as 'sizes' | 'colors' | 'gallery'] || [];
          const updated = currentValues.filter((v) => v !== idOrValue);

          await updateProduct(productId, {
            clothingOptions: {
              ...currentOptions,
              [type]: updated,
            },
          });
        }
      }

      showToast('Item deleted successfully');
      setDeleteConfirmSheet({ open: false, type: '', item: null, onConfirm: () => {} });

    } catch (error) {
      showToast('Failed to delete item', 'error');
    }
  }, [product, productId, updateProduct, showToast, isRestaurant]);

  const renderOptionSection = (label: string, type: string, list: any[]) => (
    <MotiView
      from={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ type: 'timing', duration: 300 }}
    >
      <Card
        elevate
        p="$5"
        mb="$4"
        br="$10"
        borderWidth={2}
        borderColor="$gray4"
        bg="white"
        shadowColor="$gray8"
        shadowOffset={{ width: 0, height: 4 }}
        shadowOpacity={0.1}
        shadowRadius={8}
      >
        <XStack ai="center" jc="space-between" mb="$4">
          <H4 color="$primary" fontWeight="700">{label}</H4>
          <Text color="$gray9" fontSize="$3">{list.length} items</Text>
        </XStack>

        {(type === 'addition' || type === 'side') ? (
          <YStack gap="$3" mb="$3">
            <XStack gap="$3" ai="flex-start">
              <YStack flex={2}>
                <Input
                  placeholder="Item name"
                  value={input.name}
                  onChangeText={(text) => setInput({ ...input, name: text })}
                  borderColor={errors.name ? "$red8" : "$gray6"}
                  focusStyle={{ borderColor: "$primary" }}
                  size="$4"
                />
                {errors.name && <Text color="$red10" fontSize="$2" mt="$1">{errors.name}</Text>}
              </YStack>
              <YStack flex={1}>
                <Input
                  placeholder="Price (₪)"
                  keyboardType="decimal-pad"
                  value={input.price}
                  onChangeText={(text) => setInput({ ...input, price: text })}
                  borderColor={errors.price ? "$red8" : "$gray6"}
                  focusStyle={{ borderColor: "$primary" }}
                  size="$4"
                />
                {errors.price && <Text color="$red10" fontSize="$2" mt="$1">{errors.price}</Text>}
              </YStack>
            </XStack>
          </YStack>
        ) : (
          <YStack mb="$3">
            <Input
              placeholder={`Enter ${label.toLowerCase()}`}
              value={input.value}
              onChangeText={(text) => setInput({ ...input, value: text })}
              borderColor={errors.value ? "$red8" : "$gray6"}
              focusStyle={{ borderColor: "$primary" }}
              size="$4"
            />
            {errors.value && <Text color="$red10" fontSize="$2" mt="$1">{errors.value}</Text>}
          </YStack>
        )}

        <Button
          icon={isLoading ? <Spinner size="small" color="white" /> : <Ionicons name="add" size={18} />}
          onPress={() => handleAdd(type)}
          bg="$secondary"
          hoverStyle={{ bg: '$secondary_strong' }}
          pressStyle={{ bg: '$secondary_strong' }}
          color="white"
          size="$4"
          br="$6"
          disabled={isLoading}
          opacity={isLoading ? 0.7 : 1}
        >
          {isLoading ? 'Adding...' : 'Add Item'}
        </Button>

        {list.length > 0 && (
          <YStack mt="$4" gap="$3">
            {list.map((item: any, index: number) => (
              <MotiView
                key={`${type}-${item.id || item}-${index}`}
                from={{ opacity: 0, translateX: -20 }}
                animate={{ opacity: 1, translateX: 0 }}
                transition={{ delay: index * 50 }}
              >
                <Card
                  p="$4"
                  br="$8"
                  borderWidth={1}
                  borderColor="$gray5"
                  bg="$gray1"
                  hoverStyle={{ bg: "$gray2" }}
                  pressStyle={{ scale: 0.98 }}
                >
                  <XStack jc="space-between" ai="center">
                    <YStack flex={1}>
                      <Text fontWeight="600" fontSize="$4" color="$gray12">
                        {item.name || item}
                      </Text>
                      {item.price !== undefined && (
                        <Text color="$secondary" fontWeight="700" fontSize="$3">
                          {item.price} ₪
                        </Text>
                      )}
                    </YStack>
                    <Button
                      chromeless
                      icon={<Ionicons name="trash-outline" size={20} color="#ef4444" />}
                      onPress={() => confirmDelete(type, item)}
                      hoverStyle={{ bg: "$red2" }}
                      pressStyle={{ bg: "$red3" }}
                      br="$6"
                      p="$2"
                    />
                  </XStack>
                </Card>
              </MotiView>
            ))}
          </YStack>
        )}

        {list.length === 0 && (
          <Card p="$4" mt="$3" bg="$gray2" br="$6">
            <XStack ai="center" jc="center" gap="$2">
              <Ionicons name="information-circle-outline" size={20} color="#6b7280" />
              <Text color="$gray10" fontSize="$3">No {label.toLowerCase()} added yet</Text>
            </XStack>
          </Card>
        )}
      </Card>
    </MotiView>
  );

  const handleAddCustomOption = useCallback(() => {
    try {
      const validationErrors = validateInput('custom');
      if (Object.keys(validationErrors).length > 0) {
        setErrors(validationErrors);
        return;
      }

      const existing = customOptions.find((opt) => opt.title === input.customTitle.trim());

      if (customType === 'select' || customType === 'multi-select') {
        if (!input.customValue.trim()) {
          setErrors({ customValue: 'Value is required for select types' });
          return;
        }

        // Check for duplicate values
        if (existing && existing.values?.includes(input.customValue.trim())) {
          setErrors({ customValue: 'This value already exists' });
          return;
        }

        const updated: CustomOption[] = existing
          ? customOptions.map((opt) =>
              opt.title === input.customTitle.trim()
                ? { ...opt, values: [...(opt.values ?? []), input.customValue.trim()] }
                : opt
            )
          : [
              ...customOptions,
              {
                id: generateId(),
                title: input.customTitle.trim(),
                type: customType,
                values: [input.customValue.trim()],
              },
            ];
        saveCustomOptions(updated);
        // For select/multi-select, only clear the value, keep the title for adding more values
        setInput({ ...input, customValue: '' });

      } else {
        if (!existing) {
          const newOption: CustomOption = {
            id: generateId(),
            title: input.customTitle.trim(),
            type: customType,
            values: [],
          };
          saveCustomOptions([...customOptions, newOption]);
          setInput({ ...input, customTitle: '', customValue: '' });
        } else {
          setErrors({ customTitle: 'An option with this title already exists' });
          return;
        }
      }

      setErrors({});

    } catch (error) {
      showToast('Failed to add custom option', 'error');
    }
  }, [input, customType, customOptions, saveCustomOptions, validateInput, showToast]);

  const renderCustomOptionEditor = () => (
    <MotiView
      from={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ type: 'timing', duration: 300 }}
    >
      <Card
        elevate
        p="$5"
        mb="$4"
        br="$10"
        borderWidth={2}
        borderColor="$primary"
        bg="white"
        shadowColor="$primary"
        shadowOffset={{ width: 0, height: 4 }}
        shadowOpacity={0.15}
        shadowRadius={12}
      >
        <XStack ai="center" jc="space-between" mb="$4">
          <H4 color="$primary" fontWeight="700">Custom Options</H4>
          <Text color="$gray9" fontSize="$3">{customOptions.length} options</Text>
        </XStack>

        <YStack gap="$3" mb="$4">
          <YStack>
            <Text color="$gray11" fontSize="$3" mb="$2" fontWeight="500">Option Title</Text>
            <Input
              placeholder="e.g., Spiciness Level, Size, Color"
              value={input.customTitle}
              onChangeText={(text) => setInput({ ...input, customTitle: text })}
              borderColor={errors.customTitle ? "$red8" : "$gray6"}
              focusStyle={{ borderColor: "$primary" }}
              size="$4"
            />
            {errors.customTitle && <Text color="$red10" fontSize="$2" mt="$1">{errors.customTitle}</Text>}
            {!errors.customTitle && input.customTitle && customOptions.find(opt => opt.title === input.customTitle.trim()) && (
              <Text color="$secondary" fontSize="$2" mt="$1">
                ✓ Adding values to existing option "{input.customTitle.trim()}"
              </Text>
            )}
          </YStack>

          <YStack>
            <Text color="$gray11" fontSize="$3" mb="$2" fontWeight="500">Option Type</Text>
            <Select
              open={typeDropdownOpen}
              onOpenChange={setTypeDropdownOpen}
              value={customType}
              onValueChange={(value) => setCustomType(value as CustomOption['type'])}
            >
              <Select.Trigger
                iconAfter={<Ionicons name="chevron-down" size={16} />}
                borderColor="$gray6"
                focusStyle={{ borderColor: "$primary" }}
                size="$4"
              />
              <Select.Content>
                <Select.Item index={0} value="text">Text Input</Select.Item>
                <Select.Item index={1} value="number">Number Input</Select.Item>
                <Select.Item index={2} value="select">Single Select</Select.Item>
                <Select.Item index={3} value="multi-select">Multi Select</Select.Item>
              </Select.Content>
            </Select>
          </YStack>

          {(customType === 'select' || customType === 'multi-select') && (
            <YStack>
              <XStack ai="center" jc="space-between" mb="$2">
                <Text color="$gray11" fontSize="$3" fontWeight="500">Add Option Value</Text>
                {customType === 'multi-select' && (
                  <Text color="$primary" fontSize="$2" fontStyle="italic">
                    Add multiple values
                  </Text>
                )}
              </XStack>
              <Input
                placeholder="e.g., Mild, Medium, Hot"
                value={input.customValue}
                onChangeText={(text) => setInput({ ...input, customValue: text })}
                borderColor={errors.customValue ? "$red8" : "$gray6"}
                focusStyle={{ borderColor: "$primary" }}
                size="$4"
              />
              {errors.customValue && <Text color="$red10" fontSize="$2" mt="$1">{errors.customValue}</Text>}
              {(customType === 'select' || customType === 'multi-select') && !errors.customValue && (
                <Text color="$gray9" fontSize="$2" mt="$1">
                  {customType === 'multi-select'
                    ? 'Keep the same title and add multiple values one by one'
                    : 'Add values that customers can choose from'
                  }
                </Text>
              )}
            </YStack>
          )}
        </YStack>

        <XStack gap="$3">
          <Button
            flex={1}
            icon={isLoading ? <Spinner size="small" color="white" /> : <Ionicons name="add-circle-outline" size={18} />}
            onPress={handleAddCustomOption}
            bg="$primary"
            hoverStyle={{ bg: '$third' }}
            pressStyle={{ bg: '$third' }}
            color="white"
            size="$4"
            br="$6"
            disabled={isLoading}
            opacity={isLoading ? 0.7 : 1}
          >
            {isLoading ? 'Adding...' : (customType === 'text' || customType === 'number' ? 'Create Option' : 'Add Value')}
          </Button>

          {(customType === 'select' || customType === 'multi-select') && input.customTitle && (
            <Button
              icon={<Ionicons name="refresh-outline" size={16} />}
              onPress={() => setInput({ ...input, customTitle: '', customValue: '' })}
              variant="outlined"
              size="$4"
              br="$6"
            >
              New Option
            </Button>
          )}
        </XStack>

        {customOptions.length > 0 && (
          <YStack mt="$5" gap="$3">
            <Text color="$gray11" fontSize="$4" fontWeight="600">Created Options</Text>
            {customOptions.map((opt, index) => (
              <MotiView
                key={opt.id}
                from={{ opacity: 0, translateY: 20 }}
                animate={{ opacity: 1, translateY: 0 }}
                transition={{ delay: index * 100 }}
              >
                <Card
                  p="$4"
                  br="$8"
                  borderWidth={1}
                  borderColor="$gray5"
                  bg="$gray1"
                  hoverStyle={{ bg: "$gray2" }}
                >
                  <XStack jc="space-between" ai="flex-start" mb="$2">
                    <YStack flex={1}>
                      <Text fontWeight="700" fontSize="$4" color="$gray12" mb="$1">
                        {opt.title}
                      </Text>
                      <Text color="$primary" fontSize="$3" fontWeight="500">
                        {opt.type === 'text' ? 'Text Input' :
                         opt.type === 'number' ? 'Number Input' :
                         opt.type === 'select' ? 'Single Select' : 'Multi Select'}
                      </Text>
                    </YStack>
                    <Button
                      chromeless
                      icon={<Ionicons name="trash-outline" size={18} color="#ef4444" />}
                      onPress={() => {
                        const updated = customOptions.filter((o) => o.id !== opt.id);
                        saveCustomOptions(updated);
                      }}
                      hoverStyle={{ bg: "$red2" }}
                      pressStyle={{ bg: "$red3" }}
                      br="$6"
                      p="$2"
                    />
                  </XStack>

                  {(opt.values || []).length > 0 && (
                    <YStack mt="$2" gap="$2">
                      <Text color="$gray10" fontSize="$3" fontWeight="500">Values:</Text>
                      {(opt.values || []).map((val, valIndex) => (
                        <XStack key={`${opt.id}-${val}-${valIndex}`} jc="space-between" ai="center" p="$2" bg="$gray3" br="$4">
                          <Text fontSize="$3" color="$gray11">{val}</Text>
                          <Button
                            chromeless
                            icon={<Ionicons name="close-circle" size={16} color="#ef4444" />}
                            onPress={() => {
                              const updatedValues = (opt.values || []).filter((v) => v !== val);
                              const updated = updatedValues.length
                                ? customOptions.map((o) => o.id === opt.id ? { ...o, values: updatedValues } : o)
                                : customOptions.filter((o) => o.id !== opt.id);
                              saveCustomOptions(updated);
                            }}
                            size="$2"
                          />
                        </XStack>
                      ))}
                    </YStack>
                  )}

                  {(opt.values || []).length === 0 && (opt.type === 'text' || opt.type === 'number') && (
                    <Text color="$gray9" fontSize="$3" fontStyle="italic" mt="$2">
                      Customers will enter their own {opt.type === 'number' ? 'number' : 'text'}
                    </Text>
                  )}
                </Card>
              </MotiView>
            ))}
          </YStack>
        )}
      </Card>
    </MotiView>
  );

  const renderDeleteConfirmationSheet = () => (
    <Sheet
      modal
      open={deleteConfirmSheet.open}
      onOpenChange={(open: boolean) => setDeleteConfirmSheet({ ...deleteConfirmSheet, open })}
      snapPoints={[30]}
      dismissOnSnapToBottom
    >
      <Sheet.Overlay />
      <Sheet.Frame p="$4" bg="white" br="$6">
        <YStack gap="$4" ai="center">
          <Ionicons name="warning" size={48} color="#ef4444" />
          <YStack ai="center" gap="$2">
            <H4 color="$gray12">Confirm Deletion</H4>
            <Text color="$gray10" textAlign="center" fontSize="$4">
              Are you sure you want to delete "{deleteConfirmSheet.item?.name || deleteConfirmSheet.item}"?
              This action cannot be undone.
            </Text>
          </YStack>
          <XStack gap="$3" width="100%">
            <Button
              flex={1}
              variant="outlined"
              onPress={() => setDeleteConfirmSheet({ ...deleteConfirmSheet, open: false })}
              br="$6"
            >
              Cancel
            </Button>
            <Button
              flex={1}
              bg="$red9"
              color="white"
              onPress={deleteConfirmSheet.onConfirm}
              br="$6"
              hoverStyle={{ bg: "$red10" }}
              pressStyle={{ bg: "$red10" }}
            >
              Delete
            </Button>
          </XStack>
        </YStack>
      </Sheet.Frame>
    </Sheet>
  );

  if (!productId) {
    return (
      <YStack flex={1} ai="center" jc="center" p="$4">
        <Ionicons name="alert-circle-outline" size={64} color="#ef4444" />
        <H3 color="$gray12" mt="$3">Invalid Product ID</H3>
        <Text color="$gray10" textAlign="center" mt="$2">
          No product ID was provided.
        </Text>
        <Button mt="$4" onPress={() => router.back()} bg="$primary">
          Go Back
        </Button>
      </YStack>
    );
  }

  if (!product) {
    return (
      <YStack flex={1} ai="center" jc="center" p="$4">
        <Ionicons name="alert-circle-outline" size={64} color="#ef4444" />
        <H3 color="$gray12" mt="$3">Product Not Found</H3>
        <Text color="$gray10" textAlign="center" mt="$2">
          The product you're trying to manage doesn't exist.
        </Text>
        <Text color="$gray8" textAlign="center" mt="$1" fontSize="$2">
          Product ID: {productId}
        </Text>
        <Button mt="$4" onPress={() => router.back()} bg="$primary">
          Go Back
        </Button>
      </YStack>
    );
  }

  return (
    <>
      <ScrollView
        p="$4"
        width={windowWidth}
        contentContainerStyle={{ paddingBottom: 120 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Header Section */}
        <MotiView
          from={{ opacity: 0, translateY: -30 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 600 }}
        >
          <Card
            p="$6"
            mb="$6"
            br="$12"
            bg="$primary"
            borderWidth={0}
            shadowColor="$primary"
            shadowOffset={{ width: 0, height: 8 }}
            shadowOpacity={0.3}
            shadowRadius={16}
          >
            <YStack gap="$2">
              <XStack ai="center" gap="$3">
                <Ionicons name="settings-outline" size={32} color="white" />
                <YStack flex={1}>
                  <H3 color="white" fontWeight="800">Manage Options</H3>
                  <Text color="white" opacity={0.9} fontSize="$4">
                    Customize your product with additional options
                  </Text>
                </YStack>
              </XStack>
              <Text color="white" opacity={0.8} fontSize="$3" mt="$2">
                Product: {product.name}
              </Text>
            </YStack>
          </Card>
        </MotiView>

        {/* Restaurant Options */}
        {isRestaurant && (
          <YStack gap="$4" mb="$4">
            <MotiView
              from={{ opacity: 0, translateX: -20 }}
              animate={{ opacity: 1, translateX: 0 }}
              transition={{ delay: 200 }}
            >
              <H4 color="$primary" mb="$3" fontWeight="700">Restaurant Options</H4>
            </MotiView>
            {renderOptionSection('Additions', 'addition', product?.restaurantOptions?.additions || [])}
            {renderOptionSection('Without Options', 'without', product?.restaurantOptions?.without || [])}
            {renderOptionSection('Side Dishes', 'side', product?.restaurantOptions?.sides || [])}
          </YStack>
        )}

        {/* Clothing Options */}
        {isClothing && (
          <YStack gap="$4" mb="$4">
            <MotiView
              from={{ opacity: 0, translateX: -20 }}
              animate={{ opacity: 1, translateX: 0 }}
              transition={{ delay: 200 }}
            >
              <H4 color="$primary" mb="$3" fontWeight="700">Clothing Options</H4>
            </MotiView>
            {renderOptionSection('Available Sizes', 'sizes', product?.clothingOptions?.sizes || [])}
            {renderOptionSection('Available Colors', 'colors', product?.clothingOptions?.colors || [])}
            {renderOptionSection('Gallery Images', 'gallery', product?.clothingOptions?.gallery || [])}
          </YStack>
        )}

        {/* Custom Options */}
        <MotiView
          from={{ opacity: 0, translateX: 20 }}
          animate={{ opacity: 1, translateX: 0 }}
          transition={{ delay: 400 }}
        >
          {renderCustomOptionEditor()}
        </MotiView>

        {/* Back Button */}
        <MotiView
          from={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 600 }}
        >
          <Separator my="$6" />
          <Button
            size="$5"
            br="$10"
            bg="$secondary"
            color="white"
            hoverStyle={{ bg: '$secondary_strong', scale: 1.02 }}
            pressStyle={{ bg: '$secondary_strong', scale: 0.98 }}
            icon={<Ionicons name="arrow-back-outline" size={20} />}
            onPress={() => router.back()}
            shadowColor="$secondary"
            shadowOffset={{ width: 0, height: 4 }}
            shadowOpacity={0.3}
            shadowRadius={8}
          >
            Back to Products Page
          </Button>
        </MotiView>
      </ScrollView>

      {/* Delete Confirmation Sheet */}
      {renderDeleteConfirmationSheet()}
    </>
  );
}
