import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Search, Check, X } from 'lucide-react';
import {
  type CountryCode,
  popularCountries,
  validatePhoneNumber,
  formatPhoneNumber,
  extractNationalNumber,
  findCountryByCode,
  searchCountries,
  getDefaultCountry,
  validatePhoneField
} from '../../utils/phoneNumberUtils';

interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  onCountryChange?: (countryCode: string) => void;
  countryCode?: string;
  placeholder?: string;
  label?: string;
  required?: boolean;
  error?: string;
  className?: string;
  disabled?: boolean;
}

const PhoneInput: React.FC<PhoneInputProps> = ({
  value,
  onChange,
  onCountryChange,
  countryCode = getDefaultCountry(),
  placeholder = "Enter phone number",
  label,
  required = false,
  error,
  className = "",
  disabled = false
}) => {
  const [selectedCountry, setSelectedCountry] = useState<CountryCode>(
    findCountryByCode(countryCode) || popularCountries[0]
  );
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [validation, setValidation] = useState({ isValid: true, message: '' });
  const [isFocused, setIsFocused] = useState(false);
  
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Filter countries based on search query
  const filteredCountries = searchQuery 
    ? searchCountries(searchQuery)
    : popularCountries;

  // Handle country selection
  const handleCountrySelect = (country: CountryCode) => {
    setSelectedCountry(country);
    setShowCountryDropdown(false);
    setSearchQuery('');
    
    // Update the phone number with new country code if it exists
    if (value) {
      const nationalNumber = extractNationalNumber(value, selectedCountry.code);
      const newFormattedNumber = formatPhoneNumber(nationalNumber, country.code);
      onChange(newFormattedNumber);
    }
    
    onCountryChange?.(country.code);
  };

  // Handle phone number input
  const handlePhoneChange = (inputValue: string) => {
    // Remove all non-digit characters except + and spaces
    const cleanedValue = inputValue.replace(/[^\d+\s]/g, '');
    
    // If user enters a full international number, parse it
    if (cleanedValue.startsWith('+')) {
      const validation = validatePhoneNumber(cleanedValue, selectedCountry.code);
      if (validation.isValid && validation.formattedNumber) {
        onChange(validation.formattedNumber);
        setValidation({ isValid: true, message: '' });
        return;
      }
    }
    
    // Otherwise, format as national number for selected country
    const formattedNumber = formatPhoneNumber(cleanedValue, selectedCountry.code);
    onChange(formattedNumber);
    
    // Validate the number
    const fieldValidation = validatePhoneField(formattedNumber, selectedCountry.code, required);
    setValidation(fieldValidation);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
        setShowCountryDropdown(false);
        setSearchQuery('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Validate on mount and when value changes
  useEffect(() => {
    const fieldValidation = validatePhoneField(value, selectedCountry.code, required);
    setValidation(fieldValidation);
  }, [value, selectedCountry.code, required]);

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label className="block text-sm font-semibold text-white/90 mb-3">
          {label} {required && <span className="text-red-400">*</span>}
        </label>
      )}
      
      <div className="relative">
        {/* Enhanced Country Code Selector */}
        <div className="absolute left-0 top-0 h-full z-20">
          <button
            ref={buttonRef}
            type="button"
            onClick={() => setShowCountryDropdown(!showCountryDropdown)}
            disabled={disabled}
            className="h-full px-4 flex items-center gap-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-r border-white/30 backdrop-blur-sm hover:from-blue-500/30 hover:to-purple-500/30 transition-all duration-300 rounded-l-xl"
          >
            <div className="flex items-center gap-2">
              <span className="text-xl text-white drop-shadow-lg">{selectedCountry.flag}</span>
              <div className="flex flex-col items-start">
                <span className="text-xs text-white/60 font-medium">Code</span>
                <span className="text-sm font-bold text-white">{selectedCountry.dialCode}</span>
              </div>
            </div>
            <div className="flex flex-col items-center">
              <ChevronDown 
                size={14} 
                className={`text-white/70 transition-all duration-300 ${showCountryDropdown ? 'rotate-180' : ''}`}
              />
            </div>
          </button>

                    {/* Enhanced Country Dropdown */}
          {showCountryDropdown && createPortal(
            <motion.div
              ref={dropdownRef}
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              className="fixed w-80 max-h-72 bg-white/95 backdrop-blur-xl border border-white/30 rounded-2xl shadow-2xl z-[9999] overflow-hidden"
              style={{
                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                top: buttonRef.current ? buttonRef.current.getBoundingClientRect().bottom + 8 : 0,
                left: buttonRef.current ? buttonRef.current.getBoundingClientRect().left : 0
              }}
            >
              {/* Enhanced Search Input */}
              <div className="p-4 border-b border-white/10 bg-gradient-to-r from-blue-50/50 to-purple-50/50">
                <div className="relative">
                  <Search size={18} className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search countries..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-4 py-3 bg-white/70 border border-white/30 rounded-xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                  />
                </div>
              </div>

              {/* Enhanced Countries List */}
              <div className="max-h-56 overflow-y-auto custom-scrollbar">
                {filteredCountries.map((country) => (
                  <motion.button
                    key={country.code}
                    onClick={() => handleCountrySelect(country)}
                    whileHover={{ backgroundColor: 'rgba(59, 130, 246, 0.1)' }}
                    whileTap={{ scale: 0.98 }}
                    className={`w-full px-4 py-4 flex items-center gap-4 transition-all duration-200 text-left border-b border-white/5 last:border-b-0 ${
                      selectedCountry.code === country.code 
                        ? 'bg-blue-500/10 border-l-4 border-l-blue-500' 
                        : 'hover:bg-white/10'
                    }`}
                  >
                    <div className="flex items-center gap-3 flex-1">
                      <span className="text-2xl drop-shadow-sm">{country.flag}</span>
                      <div className="flex-1">
                        <div className="text-gray-800 font-semibold text-sm">{country.name}</div>
                        <div className="text-gray-500 text-xs font-medium">{country.dialCode}</div>
                      </div>
                    </div>
                    {selectedCountry.code === country.code && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="flex items-center gap-2"
                      >
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <Check size={16} className="text-blue-500" />
                      </motion.div>
                    )}
                  </motion.button>
                ))}
                
                {filteredCountries.length === 0 && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="px-6 py-8 text-center"
                  >
                    <div className="text-gray-400 text-sm mb-2">🔍</div>
                    <div className="text-gray-500 text-sm">No countries found</div>
                  </motion.div>
                )}
              </div>
            </motion.div>,
            document.body
          )}
        </div>

        {/* Enhanced Phone Number Input */}
        <input
          ref={inputRef}
          type="tel"
          value={value}
          onChange={(e) => handlePhoneChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          disabled={disabled}
          className={`w-full pl-32 pr-12 py-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 ${
            error || !validation.isValid ? 'border-red-400 focus:ring-red-500' : ''
          } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        />

        {/* Enhanced Validation Status */}
        {value && (
          <motion.div 
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="absolute right-4 top-1/2 transform -translate-y-1/2"
          >
            {validation.isValid ? (
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <Check size={16} className="text-green-400" />
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                <X size={16} className="text-red-400" />
              </div>
            )}
          </motion.div>
        )}
      </div>

      {/* Error Message */}
      {(error || !validation.isValid) && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-2 text-red-400 text-sm flex items-center gap-2"
        >
          <X size={14} />
          <span>{error || validation.message}</span>
        </motion.div>
      )}

      {/* Helper Text */}
      {!error && validation.isValid && value && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-2 text-green-400 text-sm flex items-center gap-2"
        >
          <Check size={14} />
          <span>Valid phone number for {selectedCountry.name}</span>
        </motion.div>
      )}
    </div>
  );
};

export default PhoneInput; 