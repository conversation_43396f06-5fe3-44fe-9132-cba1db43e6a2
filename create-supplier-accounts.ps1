# Create Supplier Accounts Script - Links to existing supplier profiles

Write-Host "🏪 Creating User Accounts for Existing Suppliers..." -ForegroundColor Green

# Step 1: Check existing suppliers and their products
Write-Host "`n📋 Step 1: Checking existing suppliers..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/api/suppliers" -Method GET
    $data = $response.Content | ConvertFrom-Json
    
    Write-Host "✅ Found $($data.data.Count) suppliers in database:" -ForegroundColor Green
    foreach($supplier in $data.data) {
        $productCount = if ($supplier.products) { $supplier.products.Count } else { 0 }
        Write-Host "  - $($supplier.name) ($($supplier.id)) - Products: $productCount" -ForegroundColor White
    }
} catch {
    Write-Host "❌ Failed to get suppliers: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Create user account for "3a kefak" - IMPORTANT: Use existing supplier ID
Write-Host "`n👤 Step 2: Creating user account for '3a kefak'..." -ForegroundColor Yellow

$kefakData = @{
    firstName = "Ahmad"
    lastName = "Kefak"
    email = "<EMAIL>"
    phoneNumber = "+************"
    password = "Kefak123!"
    username = "ahmad_kefak"
    address = "Downtown Nablus"
    city = "Nablus"
    country = "Palestine"
    role = "supplier"
    supplierId = "3a-kefak"
    storeName = "3a kefak"
    businessType = "restaurants"
    openHours = "10:00 AM - 11:00 PM"
    location = @(35.**************, 32.**************)
} | ConvertTo-Json

try {
    $kefakResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/auth/signup" -Method POST -Body $kefakData -ContentType "application/json"
    Write-Host "✅ Created user account for '3a kefak'!" -ForegroundColor Green
    Write-Host "   📧 Email: <EMAIL>" -ForegroundColor Cyan
    Write-Host "   🔑 Password: Kefak123!" -ForegroundColor Cyan
} catch {
    $errorContent = $_.Exception.Response.GetResponseStream()
    $reader = New-Object System.IO.StreamReader($errorContent)
    $errorBody = $reader.ReadToEnd()
    
    if ($errorBody -like "*already exists*") {
        Write-Host "⚠️  User account for '3a kefak' already exists" -ForegroundColor Yellow
    } else {
        Write-Host "❌ Failed to create account for '3a kefak':" -ForegroundColor Red
        Write-Host "   Error: $errorBody" -ForegroundColor Red
    }
}

# Step 3: Create user account for "Style Point" - IMPORTANT: Use existing supplier ID
Write-Host "`n👤 Step 3: Creating user account for 'Style Point'..." -ForegroundColor Yellow

$styleData = @{
    firstName = "Sara"
    lastName = "Fashion"
    email = "<EMAIL>"
    phoneNumber = "+************"
    password = "Style123!"
    username = "sara_style"
    address = "Fashion District, Ramallah"
    city = "Ramallah"
    country = "Palestine"
    role = "supplier"
    supplierId = "style-point"
    storeName = "Style Point"
    businessType = "clothings"
    openHours = "10:00 AM - 11:00 PM"
    location = @(35.**************, 32.**************)
} | ConvertTo-Json

try {
    $styleResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/auth/signup" -Method POST -Body $styleData -ContentType "application/json"
    Write-Host "✅ Created user account for 'Style Point'!" -ForegroundColor Green
    Write-Host "   📧 Email: <EMAIL>" -ForegroundColor Cyan
    Write-Host "   🔑 Password: Style123!" -ForegroundColor Cyan
} catch {
    $errorContent = $_.Exception.Response.GetResponseStream()
    $reader = New-Object System.IO.StreamReader($errorContent)
    $errorBody = $reader.ReadToEnd()
    
    if ($errorBody -like "*already exists*") {
        Write-Host "⚠️  User account for 'Style Point' already exists" -ForegroundColor Yellow
    } else {
        Write-Host "❌ Failed to create account for 'Style Point':" -ForegroundColor Red
        Write-Host "   Error: $errorBody" -ForegroundColor Red
    }
}

# Step 4: Test login for both accounts
Write-Host "`n🔐 Step 4: Testing login for both accounts..." -ForegroundColor Yellow

# Test 3a kefak login
Write-Host "`n🔑 Testing login for '3a kefak'..." -ForegroundColor Cyan
$kefakLogin = @{
    email = "<EMAIL>"
    password = "Kefak123!"
} | ConvertTo-Json

try {
    $kefakLoginResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/auth/login" -Method POST -Body $kefakLogin -ContentType "application/json"
    $kefakLoginData = $kefakLoginResponse.Content | ConvertFrom-Json
    Write-Host "✅ Login successful for '3a kefak'!" -ForegroundColor Green
    Write-Host "   User ID: $($kefakLoginData.data.user._id)" -ForegroundColor Gray
    Write-Host "   Supplier ID: $($kefakLoginData.data.user.supplierId)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Login failed for '3a kefak': $($_.Exception.Message)" -ForegroundColor Red
}

# Test Style Point login
Write-Host "`n🔑 Testing login for 'Style Point'..." -ForegroundColor Cyan
$styleLogin = @{
    email = "<EMAIL>"
    password = "Style123!"
} | ConvertTo-Json

try {
    $styleLoginResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/auth/login" -Method POST -Body $styleLogin -ContentType "application/json"
    $styleLoginData = $styleLoginResponse.Content | ConvertFrom-Json
    Write-Host "✅ Login successful for 'Style Point'!" -ForegroundColor Green
    Write-Host "   User ID: $($styleLoginData.data.user._id)" -ForegroundColor Gray
    Write-Host "   Supplier ID: $($styleLoginData.data.user.supplierId)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Login failed for 'Style Point': $($_.Exception.Message)" -ForegroundColor Red
}

# Step 5: Verify supplier profiles and products
Write-Host "`n🔍 Step 5: Verifying supplier profiles and products..." -ForegroundColor Yellow

try {
    # Check 3a kefak products
    $kefakProducts = Invoke-WebRequest -Uri "http://localhost:3000/api/suppliers/3a-kefak/products" -Method GET
    $kefakProductsData = $kefakProducts.Content | ConvertFrom-Json
    Write-Host "✅ 3a kefak has $($kefakProductsData.data.products.Count) products" -ForegroundColor Green
    
    # Check Style Point products
    $styleProducts = Invoke-WebRequest -Uri "http://localhost:3000/api/suppliers/style-point/products" -Method GET
    $styleProductsData = $styleProducts.Content | ConvertFrom-Json
    Write-Host "✅ Style Point has $($styleProductsData.data.products.Count) products" -ForegroundColor Green
    
} catch {
    Write-Host "⚠️  Could not verify products: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n🎉 Setup completed!" -ForegroundColor Green
Write-Host "`n📋 SUPPLIER LOGIN CREDENTIALS:" -ForegroundColor White
Write-Host ""
Write-Host "🍽️  3a kefak (Restaurant):" -ForegroundColor Yellow
Write-Host "   📧 Email: <EMAIL>" -ForegroundColor White
Write-Host "   🔑 Password: Kefak123!" -ForegroundColor White
Write-Host "   🆔 Supplier ID: 3a-kefak" -ForegroundColor White
Write-Host ""
Write-Host "👕 Style Point (Clothing):" -ForegroundColor Yellow
Write-Host "   📧 Email: <EMAIL>" -ForegroundColor White
Write-Host "   🔑 Password: Style123!" -ForegroundColor White
Write-Host "   🆔 Supplier ID: style-point" -ForegroundColor White
Write-Host ""
Write-Host "✅ Both suppliers now have user accounts linked to their existing profiles with all products!" -ForegroundColor Green
