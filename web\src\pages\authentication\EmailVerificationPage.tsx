import React, { useState, useRef, useEffect } from 'react';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import {
  Mail, ArrowLeft, AlertCircle, CheckCircle, Sparkles, Zap,
  Shield, Crown, Gem, Star, Heart, Flame, ChevronRight,
  Bot, Package, Truck, MapPin, Gift, TrendingUp, Send,
  Clock, Rocket, Award, Target, Eye, Lock, User
} from 'lucide-react';
import Button from '../../components/common/Button';
import { LanguageSwitcher } from '../../components/common/LanguageSwitcher';
import { apiService } from '../../services/api';

const EmailVerificationPage: React.FC = () => {
  const { t } = useTranslation();
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null);
  const [animationPhase, setAnimationPhase] = useState<'initial' | 'welcome' | 'form'>('initial');

  const location = useLocation();
  const navigate = useNavigate();
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const email = location.state?.email || '';
  const fromLogin = location.state?.fromLogin || false;

  // Enhanced animation sequence
  useEffect(() => {
    const timer1 = setTimeout(() => setAnimationPhase('welcome'), 800);
    const timer2 = setTimeout(() => setAnimationPhase('form'), 1600);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);

  useEffect(() => {
    if (!email) {
      navigate('/auth/signup');
    }
  }, [email, navigate]);

  const handleCodeChange = (value: string, index: number) => {
    if (value.length > 1) return; // Prevent multiple characters

    const newCode = [...verificationCode];
    newCode[index] = value;
    setVerificationCode(newCode);
    setError('');

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent, index: number) => {
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerify = async () => {
    const code = verificationCode.join('');

    if (code.length !== 6) {
      setError(t('auth.completeSixDigitCode'));
      return;
    }

    setIsVerifying(true);
    setError('');

    try {
      const response = await apiService.verifyEmail({ token: code });

      if (response.success) {
        setSuccess(t('auth.emailVerifiedSuccessRedirect'));
        setTimeout(() => {
          navigate('/auth/login');
        }, 2000);
      } else {
        setError(response.message || t('auth.invalidVerificationCode'));
      }
    } catch (err) {
      setError(t('auth.unexpectedError'));
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendCode = async () => {
    setIsResending(true);
    setError('');

    try {
      const response = await apiService.resendVerificationEmail({ email });

      if (response.success) {
        setSuccess(t('auth.verificationCodeSent'));
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(response.message || t('auth.failedToResendCode'));
      }
    } catch (err) {
      setError(t('auth.unexpectedError'));
    } finally {
      setIsResending(false);
    }
  };

  if (!email) {
    return null;
  }

  return (
    <div className="relative">
      {/* Language Switcher */}
      <LanguageSwitcher variant="floating" position="top-right" />
      
      {/* Floating Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.3, 0.1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -top-20 -left-20 w-40 h-40 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.4, 0.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 5
          }}
          className="absolute -bottom-20 -right-20 w-32 h-32 bg-gradient-to-br from-third-500/20 to-primary-500/20 rounded-full blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-primary-400/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-10, -30, -10],
              opacity: [0, 1, 0],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}

        {/* Service icons floating */}
        {[
          { icon: Mail, delay: 0, x: 20, y: 30 },
          { icon: Shield, delay: 2, x: 80, y: 70 },
          { icon: Zap, delay: 4, x: 10, y: 80 },
          { icon: CheckCircle, delay: 6, x: 90, y: 20 },
        ].map((item, index) => (
          <motion.div
            key={index}
            className="absolute opacity-5"
            style={{ left: `${item.x}%`, top: `${item.y}%` }}
            animate={{
              y: [-5, 5, -5],
              rotate: [-5, 5, -5],
              opacity: [0.05, 0.15, 0.05],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              delay: item.delay,
              ease: "easeInOut"
            }}
          >
            <item.icon size={24} className="text-primary-400" />
          </motion.div>
        ))}
      </div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative z-10"
      >
        {/* Enhanced Welcome Header */}
        <AnimatePresence mode="wait">
          {animationPhase === 'initial' && (
            <motion.div
              key="initial"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.1, y: -20 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8"
            >
              <motion.div
                animate={{
                  scale: [1, 1.05, 1],
                  rotate: [0, 2, -2, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="inline-block"
              >
                <Sparkles size={48} className="text-primary-500 mx-auto mb-2" />
              </motion.div>
              <h3 className="text-xl font-semibold text-gray-700">{t('auth.preparingVerification')}</h3>
            </motion.div>
          )}

          {animationPhase === 'welcome' && (
            <motion.div
              key="welcome"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8"
            >
              <motion.div
                initial={{ scale: 0, rotate: 180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
                className="inline-block mb-4"
              >
                <div className="relative">
                  <Mail size={56} className="text-primary-500 mx-auto" />
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 0.8, 0.5],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="absolute inset-0 bg-primary-400/20 rounded-full blur-xl"
                  />
                </div>
              </motion.div>
              <motion.h2
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-3xl font-bold bg-gradient-to-r from-primary-600 via-secondary-500 to-third-500 bg-clip-text text-transparent"
              >
                {t('auth.emailVerificationReady')}
              </motion.h2>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="text-gray-600 mt-2"
              >
                {t('auth.verificationCodeWaiting')}
              </motion.p>
            </motion.div>
          )}

          {animationPhase === 'form' && (
            <motion.div
              key="form"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              {/* Premium Welcome Header */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-center mb-8"
              >
                <div className="flex items-center justify-center gap-3 mb-4">
                  <motion.div
                    animate={{
                      rotate: [0, 360],
                      scale: [1, 1.1, 1],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Mail size={32} className="text-primary-500" />
                  </motion.div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-primary-600 via-secondary-500 to-third-500 bg-clip-text text-transparent">
                    {t('auth.verifyYourEmail')}
                  </h2>
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.7, 1, 0.7],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 1
                    }}
                  >
                    <Sparkles size={28} className="text-secondary-500" />
                  </motion.div>
                </div>
                <p className="text-gray-600 font-medium mb-2">
                  {fromLogin
                    ? t('auth.pleaseVerifyEmail')
                    : t('auth.weSentCodeTo')
                  }
                </p>
                <motion.p
                  animate={{
                    scale: [1, 1.05, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="text-primary-600 font-bold text-lg"
                >
                  {email}
                </motion.p>
              </motion.div>

              {/* Enhanced Error Display */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  className="mb-6 p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-2xl flex items-center space-x-3 text-red-700 shadow-lg"
                >
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 0.5 }}
                  >
                    <AlertCircle size={20} className="text-red-500" />
                  </motion.div>
                  <span className="text-sm font-medium">{error}</span>
                </motion.div>
              )}

              {/* Enhanced Success Display */}
              {success && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  className="mb-6 p-4 bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-2xl flex items-center space-x-3 text-green-700 shadow-lg"
                >
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      rotate: [0, 360, 0]
                    }}
                    transition={{ duration: 1 }}
                  >
                    <CheckCircle size={20} className="text-green-500" />
                  </motion.div>
                  <span className="text-sm font-medium">{success}</span>
                </motion.div>
              )}

              {/* Enhanced Verification Code Input */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="mb-8"
              >
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="text-center mb-6"
                >
                  <div className="flex items-center justify-center gap-2 mb-3">
                    <Shield size={20} className="text-primary-500" />
                    <label className="text-lg font-bold text-gray-800">
                      {t('auth.verificationCodeLabel')}
                    </label>
                    <motion.div
                      animate={{
                        scale: [1, 1.1, 1],
                        opacity: [0.7, 1, 0.7],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <Zap size={18} className="text-secondary-500" />
                    </motion.div>
                  </div>
                  <p className="text-gray-600 text-sm">{t('auth.enterSixDigitCodeSent')}</p>
                </motion.div>

                <div className="flex justify-center space-x-3">
                  {verificationCode.map((digit, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, scale: 0.8, y: 20 }}
                      animate={{ opacity: 1, scale: 1, y: 0 }}
                      transition={{ delay: 0.6 + index * 0.1 }}
                      className="relative group"
                    >
                      <motion.div
                        className={`absolute inset-0 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-2xl transition-all duration-300 ${
                          focusedIndex === index ? 'scale-110 opacity-100' : 'scale-100 opacity-0'
                        }`}
                      />
                      <input
                        ref={el => { inputRefs.current[index] = el; }}
                        type="text"
                        maxLength={1}
                        value={digit}
                        onChange={e => handleCodeChange(e.target.value, index)}
                        onKeyDown={e => handleKeyDown(e, index)}
                        onFocus={() => setFocusedIndex(index)}
                        onBlur={() => setFocusedIndex(null)}
                        className="relative z-10 w-14 h-14 text-center text-xl font-bold bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-primary-300 focus:border-primary-500 rounded-2xl focus:outline-none focus:ring-2 focus:ring-primary-500/20 transition-all duration-300 shadow-lg hover:shadow-xl"
                      />
                      {digit && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="absolute top-1 right-1 w-3 h-3 bg-green-500 rounded-full flex items-center justify-center"
                        >
                          <CheckCircle size={8} className="text-white" />
                        </motion.div>
                      )}
                    </motion.div>
                  ))}
                </div>

                {/* Code Progress Indicator */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2 }}
                  className="mt-4 flex justify-center"
                >
                  <div className="flex space-x-1">
                    {verificationCode.map((digit, index) => (
                      <motion.div
                        key={index}
                        className={`w-2 h-2 rounded-full transition-all duration-300 ${
                          digit ? 'bg-primary-500' : 'bg-gray-300'
                        }`}
                        animate={digit ? { scale: [1, 1.2, 1] } : {}}
                        transition={{ duration: 0.3 }}
                      />
                    ))}
                  </div>
                </motion.div>
              </motion.div>

              {/* Premium Verify Button */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.4 }}
                className="relative mb-6"
              >
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl blur-lg opacity-30"
                  animate={{
                    scale: [1, 1.05, 1],
                    opacity: [0.3, 0.5, 0.3],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
                <Button
                  onClick={handleVerify}
                  variant="primary"
                  size="lg"
                  loading={isVerifying}
                  disabled={isVerifying || verificationCode.join('').length !== 6}
                  className="w-full relative z-10 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white font-bold py-4 px-8 rounded-2xl shadow-xl transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"
                >
                  <div className="flex items-center justify-center gap-3">
                    {isVerifying ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <Shield size={20} />
                      </motion.div>
                    ) : (
                      <motion.div
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <CheckCircle size={20} />
                      </motion.div>
                    )}
                    <span className="text-lg">
                      {isVerifying ? t('auth.verifyingEmail') : t('auth.verifyEmailButton')}
                    </span>
                    {!isVerifying && (
                      <motion.div
                        animate={{ x: [0, 3, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        <ChevronRight size={20} />
                      </motion.div>
                    )}
                  </div>
                </Button>
              </motion.div>

              {/* Enhanced Resend Section */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.6 }}
                className="text-center mb-8"
              >
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 bg-white text-gray-500 font-medium">{t('auth.didntReceiveCode')}</span>
                  </div>
                </div>
                <motion.div
                  className="mt-4"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <button
                    onClick={handleResendCode}
                    disabled={isResending}
                    className="group inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-semibold transition-all duration-300 bg-gradient-to-r from-primary-50 to-secondary-50 hover:from-primary-100 hover:to-secondary-100 px-6 py-3 rounded-2xl border border-primary-200 hover:border-primary-300 disabled:opacity-50"
                  >
                    {isResending ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <Send size={16} />
                      </motion.div>
                    ) : (
                      <Send size={16} />
                    )}
                    <span>{isResending ? t('auth.sendingCode') : t('auth.resendCodeButton')}</span>
                    {!isResending && (
                      <motion.div
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                        animate={{ x: [0, 3, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        <ChevronRight size={16} />
                      </motion.div>
                    )}
                  </button>
                </motion.div>
              </motion.div>

              {/* Enhanced Back Link */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.8 }}
                className="text-center"
              >
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link
                    to={fromLogin ? "/auth/login" : "/auth/signup"}
                    className="group inline-flex items-center gap-2 text-gray-600 hover:text-gray-800 font-medium transition-all duration-300 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-gray-100 hover:to-gray-200 px-6 py-3 rounded-2xl border border-gray-200 hover:border-gray-300"
                  >
                    <ArrowLeft size={16} />
                    <span>{fromLogin ? t('auth.backToLogin') : t('auth.backToSignup')}</span>
                    <motion.div
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      animate={{ x: [0, -3, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ArrowLeft size={14} />
                    </motion.div>
                  </Link>
                </motion.div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default EmailVerificationPage;
