import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import {
  Mail, ArrowLeft, AlertCircle, CheckCircle, Sparkles,
  Shield, ChevronRight, Key, Send
} from 'lucide-react';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import { LanguageSwitcher } from '../../components/common/LanguageSwitcher';
import { apiService } from '../../services/api';

interface ForgotPasswordFormData {
  email: string;
}

const VerifyBeforeResetPage: React.FC = () => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [animationPhase, setAnimationPhase] = useState<'initial' | 'welcome' | 'form'>('initial');

  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordFormData>();

  // Enhanced animation sequence
  useEffect(() => {
    const timer1 = setTimeout(() => setAnimationPhase('welcome'), 800);
    const timer2 = setTimeout(() => setAnimationPhase('form'), 1600);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await apiService.forgotPassword(data.email);

      if (response.success) {
        setSuccess(t('auth.resetCodeSent'));
        setTimeout(() => {
          navigate('/auth/reset-password', {
            state: { email: data.email }
          });
        }, 2000);
      } else {
        setError(response.message || t('auth.failedToSendCode'));
      }
    } catch (err) {
      setError(t('auth.unexpectedError'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative">
      {/* Language Switcher */}
      <LanguageSwitcher variant="floating" position="top-right" />
      
      {/* Floating Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.3, 0.1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -top-20 -left-20 w-40 h-40 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.4, 0.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 5
          }}
          className="absolute -bottom-20 -right-20 w-32 h-32 bg-gradient-to-br from-third-500/20 to-primary-500/20 rounded-full blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-primary-400/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-10, -30, -10],
              opacity: [0, 1, 0],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}

        {/* Service icons floating */}
        <motion.div
          className="absolute opacity-5"
          style={{ left: '20%', top: '30%' }}
          animate={{
            y: [-5, 5, -5],
            rotate: [-5, 5, -5],
            opacity: [0.05, 0.15, 0.05],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            delay: 0,
            ease: "easeInOut"
          }}
        >
          <Shield size={24} className="text-primary-400" />
        </motion.div>
        <motion.div
          className="absolute opacity-5"
          style={{ left: '80%', top: '70%' }}
          animate={{
            y: [-5, 5, -5],
            rotate: [-5, 5, -5],
            opacity: [0.05, 0.15, 0.05],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            delay: 2,
            ease: "easeInOut"
          }}
        >
          <Key size={24} className="text-primary-400" />
        </motion.div>
        <motion.div
          className="absolute opacity-5"
          style={{ left: '10%', top: '80%' }}
          animate={{
            y: [-5, 5, -5],
            rotate: [-5, 5, -5],
            opacity: [0.05, 0.15, 0.05],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            delay: 4,
            ease: "easeInOut"
          }}
        >
          <Mail size={24} className="text-primary-400" />
        </motion.div>
        <motion.div
          className="absolute opacity-5"
          style={{ left: '90%', top: '20%' }}
          animate={{
            y: [-5, 5, -5],
            rotate: [-5, 5, -5],
            opacity: [0.05, 0.15, 0.05],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            delay: 6,
            ease: "easeInOut"
          }}
        >
          <Send size={24} className="text-primary-400" />
        </motion.div>
      </div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative z-10"
      >
        {/* Enhanced Welcome Header */}
        <AnimatePresence mode="wait">
          {animationPhase === 'initial' && (
            <motion.div
              key="initial"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.1, y: -20 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8"
            >
              <motion.div
                animate={{
                  scale: [1, 1.05, 1],
                  rotate: [0, 2, -2, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="inline-block"
              >
                <Sparkles size={48} className="text-primary-500 mx-auto mb-2" />
              </motion.div>
              <h3 className="text-xl font-semibold text-gray-700">{t('auth.initializingSecurity')}</h3>
            </motion.div>
          )}

          {animationPhase === 'welcome' && (
            <motion.div
              key="welcome"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8"
            >
              <motion.div
                initial={{ scale: 0, rotate: 180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
                className="inline-block mb-4"
              >
                <div className="relative">
                  <Shield size={56} className="text-primary-500 mx-auto" />
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 0.8, 0.5],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="absolute inset-0 bg-primary-400/20 rounded-full blur-xl"
                  />
                </div>
              </motion.div>
              <motion.h2
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-3xl font-bold bg-gradient-to-r from-primary-600 via-secondary-500 to-third-500 bg-clip-text text-transparent"
              >
                {t('auth.passwordRecovery')} 🔐
              </motion.h2>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="text-gray-600 mt-2"
              >
                {t('auth.passwordRecoveryDescription')}
              </motion.p>
            </motion.div>
          )}

          {animationPhase === 'form' && (
            <motion.div
              key="form"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              {/* Premium Welcome Header */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-center mb-8"
              >
                <div className="flex items-center justify-center gap-3 mb-4">
                  <motion.div
                    animate={{
                      rotate: [0, 360],
                      scale: [1, 1.1, 1],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Key size={32} className="text-primary-500" />
                  </motion.div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-primary-600 via-secondary-500 to-third-500 bg-clip-text text-transparent">
                    {t('auth.passwordRecovery')}
                  </h2>
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.7, 1, 0.7],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 1
                    }}
                  >
                    <Shield size={28} className="text-secondary-500" />
                  </motion.div>
                </div>
                <p className="text-gray-600 font-medium">
                  {t('auth.enterEmailForReset')}
                </p>
              </motion.div>

              {/* Enhanced Error Display */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  className="mb-6 p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-2xl flex items-center space-x-3 text-red-700 shadow-lg"
                >
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 0.5 }}
                  >
                    <AlertCircle size={20} className="text-red-500" />
                  </motion.div>
                  <span className="text-sm font-medium">{error}</span>
                </motion.div>
              )}

              {/* Enhanced Success Display */}
              {success && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  className="mb-6 p-4 bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-2xl flex items-center space-x-3 text-green-700 shadow-lg"
                >
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 0.5 }}
                  >
                    <CheckCircle size={20} className="text-green-500" />
                  </motion.div>
                  <span className="text-sm font-medium">{success}</span>
                </motion.div>
              )}

              {/* Premium Form */}
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Enhanced Email Field */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                  className="relative"
                >
                  <div className="relative group">
                    <motion.div
                      className={`absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 rounded-2xl transition-all duration-300 ${
                        focusedField === 'email' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                      }`}
                    />
                    <div className="relative">
                      <Input
                        label={t('auth.emailAddress')}
                        type="email"
                        placeholder={t('auth.enterEmailAddress')}
                        icon={<Mail size={18} className="text-primary-500" />}
                        error={errors.email?.message}
                        onFocus={() => setFocusedField('email')}
                        className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-primary-300 focus:border-primary-500 rounded-2xl transition-all duration-300"
                        {...register('email', {
                          required: t('auth.emailRequired'),
                          pattern: {
                            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                            message: t('auth.invalidEmail'),
                          },
                          onBlur: () => setFocusedField(null)
                        })}
                      />
                    </div>
                  </div>
                </motion.div>

                {/* Premium Submit Button */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="relative"
                >
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl blur-lg opacity-30"
                    animate={{
                      scale: [1, 1.05, 1],
                      opacity: [0.3, 0.5, 0.3],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <Button
                    type="submit"
                    variant="primary"
                    size="lg"
                    loading={isLoading}
                    disabled={isLoading}
                    className="w-full relative z-10 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white font-bold py-4 px-8 rounded-2xl shadow-xl transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"
                  >
                    <div className="flex items-center justify-center gap-3">
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        >
                          <Send size={20} />
                        </motion.div>
                      ) : (
                        <motion.div
                          animate={{ scale: [1, 1.1, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          <Send size={20} />
                        </motion.div>
                      )}
                      <span className="text-lg">
                        {isLoading ? t('auth.sendingResetCode') : t('auth.sendResetCode')}
                      </span>
                    </div>
                  </Button>
                </motion.div>
              </form>

              {/* Enhanced Back to Login Link */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="mt-8 text-center"
              >
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 bg-white text-gray-500 font-medium">{t('auth.rememberPassword')}</span>
                  </div>
                </div>
                <motion.div
                  className="mt-4"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link
                    to="/auth/login"
                    className="group inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-semibold transition-all duration-300 bg-gradient-to-r from-primary-50 to-secondary-50 hover:from-primary-100 hover:to-secondary-100 px-6 py-3 rounded-2xl border border-primary-200 hover:border-primary-300"
                  >
                    <ArrowLeft size={16} />
                    <span>{t('auth.backToLogin')}</span>
                    <motion.div
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      animate={{ x: [0, -3, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ChevronRight size={16} className="rotate-180" />
                    </motion.div>
                  </Link>
                </motion.div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default VerifyBeforeResetPage;
