import { Router } from 'express';
import { body } from 'express-validator';
import { PromoCodeController } from '../controllers/promoCodeController';
import { authenticate } from '../middleware/auth';

const router = Router();

// Validation rules for promo code validation
const validatePromoCodeValidation = [
  body('code')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Promo code is required and must be between 1 and 50 characters'),
  body('orderTotal')
    .isFloat({ min: 0 })
    .withMessage('Order total must be a positive number')
];

// All routes require authentication
router.use(authenticate);

// Promo code routes
router.post('/validate', validatePromoCodeValidation, PromoCodeController.validatePromoCode);

export default router;
