import { useRequestPickupStore } from '../stores/useRequestPickupStore';
import { apiService } from '../services/api';
import { useCallback } from 'react';

export const useRequestPickup = () => {
  const store = useRequestPickupStore();

  // Enhanced fetch function that updates store
  const fetchPackageByTrackingNumber = useCallback(async (trackingNumber: string) => {
    try {
      store.setLoading(true);
      store.setError(null);

      // First check store
      let packageData = store.getPackageByTrackingNumber(trackingNumber);
      
      if (packageData) {
        store.setCurrentPackage(packageData);
        return { success: true, data: packageData, source: 'store' as const };
      }

      // If not in store, fetch from API
      const response = await apiService.getPackageByTrackingNumber(trackingNumber);
      
      if (response.success && response.data) {
        // Store the data for future use
        store.addPackage(response.data);
        store.setCurrentPackage(response.data);
        return { success: true, data: response.data, source: 'api' as const };
      } else {
        const errorMessage = 'Package not found';
        store.setError(errorMessage);
        return { success: false, error: errorMessage, source: 'api' as const };
      }
    } catch (error) {
      const errorMessage = 'Failed to fetch package details';
      store.setError(errorMessage);
      return { success: false, error: errorMessage, source: 'api' as const };
    } finally {
      store.setLoading(false);
    }
  }, [store]);

  // Enhanced fetch function for multiple packages
  const fetchUserPackages = useCallback(async (userId: string) => {
    try {
      store.setLoading(true);
      store.setError(null);

      // This would be an API call to get all user packages
      // For now, we'll return the store data
      const userPackages = store.packages.filter(pkg => pkg.userId === userId);
      return { success: true, data: userPackages, source: 'store' as const };
    } catch (error) {
      const errorMessage = 'Failed to fetch user packages';
      store.setError(errorMessage);
      return { success: false, error: errorMessage, source: 'api' as const };
    } finally {
      store.setLoading(false);
    }
  }, [store]);

  // Utility function to get package with fallback
  const getPackageWithFallback = useCallback((identifier: string) => {
    // Try by tracking number first
    let packageData = store.getPackageByTrackingNumber(identifier);
    
    if (!packageData) {
      // Try by ID
      packageData = store.getPackageById(identifier);
    }

    return packageData;
  }, [store]);

  return {
    // Store state
    packages: store.packages,
    currentPackage: store.currentPackage,
    loading: store.loading,
    error: store.error,
    
    // Store actions
    addPackage: store.addPackage,
    setCurrentPackage: store.setCurrentPackage,
    updatePackageStatus: store.updatePackageStatus,
    updatePackage: store.updatePackage,
    removePackage: store.removePackage,
    clearPackages: store.clearPackages,
    
    // Store computed
    getRecentPackages: store.getRecentPackages,
    getPackagesByStatus: store.getPackagesByStatus,
    getActivePackages: store.getActivePackages,
    
    // Enhanced functions
    fetchPackageByTrackingNumber,
    fetchUserPackages,
    getPackageWithFallback,
    
    // Direct store access
    getPackageById: store.getPackageById,
    getPackageByTrackingNumber: store.getPackageByTrackingNumber,
  };
}; 