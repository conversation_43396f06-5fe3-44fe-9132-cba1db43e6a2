const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('../dist/models/User').default;
const { Supplier } = require('../dist/models/Supplier');

// Connect to MongoDB
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/wasel');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

// Link user account to existing supplier profile
async function linkUserToSupplier() {
  try {
    await connectDB();

    console.log('🔗 Linking user account to existing supplier profile...');
    
    // Find the user account that was created manually
    const user = await User.findOne({ 
      email: '<EMAIL>',
      role: 'supplier'
    });

    if (!user) {
      console.log('❌ User not found with email: <EMAIL>');
      return;
    }

    console.log(`👤 Found user: ${user.firstName} ${user.lastName}`);
    console.log(`   Current supplier ID: ${user.supplierId}`);
    console.log(`   Store name: ${user.storeName}`);

    // Find the existing "3a kefak" supplier profile
    const existingSupplier = await Supplier.findOne({ id: '3a-kefak' });

    if (!existingSupplier) {
      console.log('❌ Existing supplier "3a-kefak" not found in database');
      return;
    }

    console.log(`🏪 Found existing supplier: ${existingSupplier.name}`);
    console.log(`   Supplier ID: ${existingSupplier.id}`);
    console.log(`   Products: ${existingSupplier.products?.length || 0}`);
    console.log(`   Rating: ${existingSupplier.rating}`);

    // Update the user account to link to the existing supplier
    const updatedUser = await User.findByIdAndUpdate(
      user._id,
      {
        $set: {
          supplierId: '3a-kefak',  // Link to existing supplier
          storeName: '3a kefak',   // Match the existing supplier name
          businessType: 'restaurants' // Match the existing category
        }
      },
      { new: true }
    );

    console.log('✅ Successfully linked user account to existing supplier!');
    console.log(`   User email: ${updatedUser.email}`);
    console.log(`   New supplier ID: ${updatedUser.supplierId}`);
    console.log(`   Store name: ${updatedUser.storeName}`);

    // Optional: Remove the auto-generated supplier profile if it exists
    const autoGeneratedSupplier = await Supplier.findOne({ id: user.supplierId });
    if (autoGeneratedSupplier && autoGeneratedSupplier.id !== '3a-kefak') {
      await Supplier.deleteOne({ id: user.supplierId });
      console.log(`🗑️  Removed auto-generated supplier profile: ${user.supplierId}`);
    }

    // Verify the link works
    console.log('\n🔍 Verifying the link...');
    
    // Check if user can access the supplier profile
    const linkedSupplier = await Supplier.findOne({ id: updatedUser.supplierId });
    if (linkedSupplier) {
      console.log('✅ Link verification successful!');
      console.log(`   Supplier: ${linkedSupplier.name}`);
      console.log(`   Products: ${linkedSupplier.products?.length || 0}`);
      console.log(`   Category: ${linkedSupplier.category}`);
      console.log(`   Rating: ${linkedSupplier.rating}`);
      
      // Show some sample products
      if (linkedSupplier.products && linkedSupplier.products.length > 0) {
        console.log('\n📦 Sample products:');
        linkedSupplier.products.slice(0, 3).forEach((product, index) => {
          console.log(`   ${index + 1}. ${product.name} - $${product.price}`);
        });
      }
    } else {
      console.log('❌ Link verification failed!');
    }

    console.log('\n🎉 Process completed!');
    console.log('\n📋 SUMMARY:');
    console.log(`✅ User "${user.firstName} ${user.lastName}" is now linked to supplier "3a kefak"`);
    console.log(`✅ All ${existingSupplier.products?.length || 0} products are now accessible`);
    console.log(`✅ User can login and manage the "3a kefak" store`);
    console.log(`✅ Store appears in customer pages with all products`);
    
    console.log('\n🔑 LOGIN CREDENTIALS:');
    console.log(`📧 Email: ${user.email}`);
    console.log(`🔑 Password: [Use the password you set during signup]`);
    console.log(`🏪 Store: 3a kefak`);
    console.log(`🆔 Supplier ID: 3a-kefak`);

  } catch (error) {
    console.error('❌ Error linking user to supplier:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
  }
}

// Show current status before linking
async function showCurrentStatus() {
  try {
    await connectDB();

    console.log('📊 CURRENT STATUS:');
    
    // Find the user
    const user = await User.findOne({ 
      email: '<EMAIL>',
      role: 'supplier'
    });

    if (user) {
      console.log(`👤 User: ${user.firstName} ${user.lastName}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Current supplier ID: ${user.supplierId}`);
      console.log(`   Store name: ${user.storeName}`);
    } else {
      console.log('❌ User not found');
      return;
    }

    // Find the existing supplier
    const existingSupplier = await Supplier.findOne({ id: '3a-kefak' });
    if (existingSupplier) {
      console.log(`🏪 Existing supplier "3a kefak":`);
      console.log(`   Products: ${existingSupplier.products?.length || 0}`);
      console.log(`   Rating: ${existingSupplier.rating}`);
      console.log(`   Category: ${existingSupplier.category}`);
    } else {
      console.log('❌ Existing supplier "3a-kefak" not found');
    }

    // Check if user's current supplier profile exists
    const userSupplier = await Supplier.findOne({ id: user.supplierId });
    if (userSupplier) {
      console.log(`🔗 User's current supplier profile:`);
      console.log(`   Name: ${userSupplier.name}`);
      console.log(`   Products: ${userSupplier.products?.length || 0}`);
    } else {
      console.log('⚠️  User\'s current supplier profile not found');
    }

  } catch (error) {
    console.error('❌ Error showing status:', error);
  } finally {
    await mongoose.disconnect();
  }
}

// Main execution
const command = process.argv[2];

if (command === 'status') {
  showCurrentStatus();
} else if (command === 'link') {
  linkUserToSupplier();
} else {
  console.log('📖 Usage:');
  console.log('  node linkUserToSupplier.js status  - Show current status');
  console.log('  node linkUserToSupplier.js link    - Link user to existing supplier');
}
