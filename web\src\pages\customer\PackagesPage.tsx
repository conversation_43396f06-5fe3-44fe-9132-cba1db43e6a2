import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Package,
  MapPin,
  User,
  Phone,
  Clock,
  Eye,
  Search,
  X,
  Navigation,
  Truck,
  CheckCircle,
  RotateCcw,
  Calendar,
  Filter,
  ArrowLeft,
  Zap,
  Star,
  TrendingUp,
  Activity,
  Loader2,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { usePackagesStore } from '../../stores/packagesStore';
import { useAuth } from '../../contexts/AuthContext';

type PackageStatus = 'All' | 'pending' | 'confirmed' | 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'cancelled';
type TabType = 'sent' | 'pickup';

const PackagesPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const {
    packages,
    isLoading,
    error,
    pagination,
    stats: storeStats,
    fetchPackages,
    refreshPackages,
    setError
  } = usePackagesStore();

  const [activeTab, setActiveTab] = useState<TabType>('sent');
  const [selectedFilter, setSelectedFilter] = useState<PackageStatus>('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [packagesPerPage] = useState(10);

  // Smart search debouncing to prevent server overload
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500); // 500ms debounce for search

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // Authentication check and initial data fetch
  useEffect(() => {
    if (!isAuthenticated || !user) {
      navigate('/auth/login');
      return;
    }

    // Fetch packages with pagination on component mount
    fetchPackagesWithPagination();
  }, [isAuthenticated, user, navigate]);

  // Refresh packages when component becomes visible (e.g., after adding new packages)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && isAuthenticated && user) {
        // Refresh packages when page becomes visible
        fetchPackagesWithPagination();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [isAuthenticated, user]);

  // Fetch packages when page, filter, or search changes (all server-side now)
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchPackagesWithPagination();
    }
  }, [currentPage, selectedFilter, debouncedSearchQuery, activeTab, isAuthenticated, user]);

  // Reset to page 1 when search or filter changes
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [debouncedSearchQuery, selectedFilter, activeTab]);

  // Handle scroll for header animation with debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      // Clear previous timeout
      clearTimeout(timeoutId);

      // Debounce the header compact state change
      timeoutId = setTimeout(() => {
        // Show sliding header after scrolling past the hero section
        setIsHeaderCompact(currentScrollY > 300);
      }, 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Fetch packages with server-side pagination and search
  const fetchPackagesWithPagination = useCallback(async () => {
    try {
      setError(null);

      // Prepare API parameters - backend supports page, limit, status, and type
      const params: any = {
        page: currentPage,
        limit: packagesPerPage
      };

      // Add status filter if not 'All' - map frontend status to backend status
      if (selectedFilter !== 'All') {
        params.status = selectedFilter;
      }

      // Add service type filter based on active tab
      if (activeTab === 'sent') {
        params.type = 'send_package';
      } else {
        params.type = 'request_pickup';
      }

      // Add search parameter if exists (now supported by backend)
      if (debouncedSearchQuery.trim()) {
        const searchTerm = debouncedSearchQuery.trim();

        // Client-side validation
        if (searchTerm.length > 100) {
          setError('Search term too long (max 100 characters)');
          return;
        }

        if (searchTerm.length >= 2) {
          params.search = searchTerm;
        }
      }

      await fetchPackages(true, params);
    } catch (error) {
      console.error('Error fetching packages:', error);
      setError('Failed to fetch packages. Please try again.');
    }
  }, [currentPage, packagesPerPage, selectedFilter, debouncedSearchQuery, activeTab, fetchPackages, setError]);

  // Handle refresh functionality
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchPackagesWithPagination();
    } catch (error) {
      console.error('Error refreshing packages:', error);
      setError('Failed to refresh packages. Please try again.');
    } finally {
      setRefreshing(false);
    }
  }, [fetchPackagesWithPagination, setError]);

  // Filter packages based on active tab and search
  const filteredPackages = useMemo(() => {
    // Since the backend now handles all filtering (type, status, search),
    // we should use the packages directly as they come from the server
    // The backend already filters by serviceType, status, and search
    return packages;
  }, [packages]);

  const filters: { key: PackageStatus; label: string; color: string }[] = [
    { key: 'All', label: 'All Packages', color: 'bg-gray-100 text-gray-700' },
    { key: 'delivered', label: 'Delivered', color: 'bg-green-100 text-green-700' },
    { key: 'out_for_delivery', label: 'On the Way', color: 'bg-orange-100 text-orange-700' },
    { key: 'confirmed', label: 'Confirmed', color: 'bg-blue-100 text-blue-700' },
    { key: 'pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-700' },
    { key: 'picked_up', label: 'Picked Up', color: 'bg-purple-100 text-purple-700' },
    { key: 'in_transit', label: 'In Transit', color: 'bg-indigo-100 text-indigo-700' },
    { key: 'cancelled', label: 'Cancelled', color: 'bg-red-100 text-red-700' }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered': return <CheckCircle size={20} className="text-green-300" />;
      case 'out_for_delivery': return <Truck size={20} className="text-orange-300" />;
      case 'confirmed': return <CheckCircle size={20} className="text-blue-300" />;
      case 'pending': return <Clock size={20} className="text-yellow-300" />;
      case 'picked_up': return <Package size={20} className="text-purple-300" />;
      case 'in_transit': return <Truck size={20} className="text-indigo-300" />;
      case 'cancelled': return <X size={20} className="text-red-300" />;
      default: return <Package size={20} className="text-gray-300" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered': return 'text-green-300 bg-green-500/20 border-green-400/50';
      case 'out_for_delivery': return 'text-orange-300 bg-orange-500/20 border-orange-400/50';
      case 'confirmed': return 'text-blue-300 bg-blue-500/20 border-blue-400/50';
      case 'pending': return 'text-yellow-300 bg-yellow-500/20 border-yellow-400/50';
      case 'picked_up': return 'text-purple-300 bg-purple-500/20 border-purple-400/50';
      case 'in_transit': return 'text-indigo-300 bg-indigo-500/20 border-indigo-400/50';
      case 'cancelled': return 'text-red-300 bg-red-500/20 border-red-400/50';
      default: return 'text-gray-300 bg-gray-500/20 border-gray-400/50';
    }
  };

  const getStatusDisplayName = (status: string) => {
    switch (status) {
      case 'delivered': return 'Delivered';
      case 'out_for_delivery': return 'On the Way';
      case 'confirmed': return 'Confirmed';
      case 'pending': return 'Pending';
      case 'picked_up': return 'Picked Up';
      case 'in_transit': return 'In Transit';
      case 'cancelled': return 'Cancelled';
      default: return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const getEstimatedTime = (pkg: any) => {
    if (pkg.estimatedDeliveryTime) {
      const estimated = new Date(pkg.estimatedDeliveryTime);
      const now = new Date();
      const diffMs = estimated.getTime() - now.getTime();
      const diffMins = Math.round(diffMs / (1000 * 60));
      
      if (diffMins <= 0) return 'Delivered';
      if (diffMins < 60) return `${diffMins} mins`;
      const hours = Math.floor(diffMins / 60);
      const mins = diffMins % 60;
      return `${hours}h ${mins}m`;
    }
    return '30-45 mins';
  };

  // Calculate stats for the current tab
  const stats = useMemo(() => {
    // Since the backend now handles filtering by serviceType, 
    // the packages returned are already filtered for the current tab
    const currentPackages = filteredPackages;
    
    // Get the total for the current service type from storeStats
    const currentTypeTotal = activeTab === 'sent' 
      ? (storeStats?.sendPackagesTotal || 0)
      : (storeStats?.pickupPackagesTotal || 0);
    
    return {
      delivered: currentPackages.filter(p => p.status === 'delivered').length,
      inTransit: currentPackages.filter(p => ['out_for_delivery', 'in_transit', 'picked_up'].includes(p.status)).length,
      total: currentTypeTotal // Use the total for the current service type
    };
  }, [filteredPackages, storeStats, activeTab]);

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-purple-500/30 to-pink-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-green-500/20 to-blue-500/20 rounded-full blur-3xl"
          />

          {/* Floating particles */}
          <div className="absolute inset-0">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-white/10 rounded-full"
                animate={{
                  y: [0, -100, 0],
                  x: [0, Math.random() * 100 - 50, 0],
                  opacity: [0, 1, 0],
                }}
                transition={{
                  duration: Math.random() * 10 + 10,
                  repeat: Infinity,
                  delay: Math.random() * 10,
                }}
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
              />
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="relative z-10">
          {/* Hero Header */}
          <div className="pt-20 pb-16 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto text-center">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="mb-8"
              >
                <div className="inline-flex items-center gap-3 mb-6">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-2xl"
                  >
                    <Package className="text-white" size={32} />
                  </motion.div>
                  <div>
                    <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent">
                      My Packages
                    </h1>
                    <motion.div
                      animate={{ width: ["0%", "100%"] }}
                      transition={{ duration: 2, delay: 0.5 }}
                      className="h-1 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full mt-2"
                    />
                  </div>
                </div>
                <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
                  Track your deliveries with real-time updates and comprehensive package management
                </p>
              </motion.div>

              {/* Stats Cards */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
              >
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                      <CheckCircle className="text-green-400" size={24} />
                    </div>
                    <div className="text-left">
                      <p className="text-2xl font-bold text-white">
                        {stats.delivered}
                      </p>
                      <p className="text-gray-300 text-sm">Delivered</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center">
                      <Truck className="text-orange-400" size={24} />
                    </div>
                    <div className="text-left">
                      <p className="text-2xl font-bold text-white">
                        {stats.inTransit}
                      </p>
                      <p className="text-gray-300 text-sm">In Transit</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                      <Activity className="text-blue-400" size={24} />
                    </div>
                    <div className="text-left">
                      <p className="text-2xl font-bold text-white">
                        {stats.total}
                      </p>
                      <p className="text-gray-300 text-sm">
                        {activeTab === 'sent' ? 'Sent Packages' : 'Pickup Requests'}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Control Panel */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
            {/* Tabs */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="mb-8"
            >
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                <div className="flex justify-center gap-4">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setActiveTab('sent')}
                    className={`px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 ${
                      activeTab === 'sent'
                        ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-2xl'
                        : 'bg-white/20 text-white hover:bg-white/30'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <Package size={24} />
                      Sent Packages
                    </div>
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setActiveTab('pickup')}
                    className={`px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 ${
                      activeTab === 'pickup'
                        ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-2xl'
                        : 'bg-white/20 text-white hover:bg-white/30'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <RotateCcw size={24} />
                      Pickup Requests
                    </div>
                  </motion.button>
                </div>
              </div>
            </motion.div>

            {/* Search Bar */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="mb-8"
            >
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 overflow-hidden">
                <div className="flex items-center gap-4 p-6">
                  <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                    <Search size={24} className="text-white" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search packages, types, addresses, or recipients..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1 bg-transparent text-white placeholder-gray-300 outline-none text-lg"
                  />
                  {searchQuery.trim() && (
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setSearchQuery('')}
                      className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-lg transition-colors flex items-center justify-center"
                    >
                      <X size={20} className="text-white" />
                    </motion.button>
                  )}
                </div>
              </div>
            </motion.div>

            {/* Filter Bar */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="mb-12"
            >
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                    <Filter size={20} className="text-white" />
                  </div>
                  <h3 className="text-white font-semibold text-lg">Filter by Status</h3>
                </div>
                <div className="flex flex-wrap gap-3">
                  {filters.map((filter) => (
                    <motion.button
                      key={filter.key}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setSelectedFilter(filter.key)}
                      className={`px-6 py-3 rounded-xl text-sm font-semibold transition-all duration-300 ${
                        selectedFilter === filter.key
                          ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-2xl'
                          : 'bg-white/20 text-white hover:bg-white/30'
                      }`}
                    >
                      {filter.label}
                    </motion.button>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Sticky Header with Ultra-Smooth Scroll Animation */}
        <motion.div
          className="fixed left-0 right-0 transition-all duration-500"
          animate={{
            top: isHeaderCompact ? "0px" : "64px",
            zIndex: isHeaderCompact ? 50 : 40,
            backgroundColor: isHeaderCompact
              ? "rgba(15, 23, 42, 0.95)"
              : "rgba(15, 23, 42, 0)",
            backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
            borderBottom: isHeaderCompact
              ? "1px solid rgba(255, 255, 255, 0.1)"
              : "1px solid rgba(255, 255, 255, 0)",
          }}
          transition={{
            duration: 0.6,
            ease: [0.25, 0.46, 0.45, 0.94],
            type: "tween"
          }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              animate={{
                paddingTop: isHeaderCompact ? "1rem" : "2rem",
                paddingBottom: isHeaderCompact ? "1rem" : "2rem",
              }}
              transition={{ duration: 0.3 }}
              className="flex items-center justify-between"
            >
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/customer')}
                className="flex items-center gap-3 text-white hover:text-gray-300 transition-colors"
              >
                <ArrowLeft size={24} />
                <span className="font-medium">Back to Dashboard</span>
              </motion.button>

              <motion.div
                animate={{
                  opacity: isHeaderCompact ? 1 : 0,
                  scale: isHeaderCompact ? 1 : 0.8,
                }}
                transition={{ duration: 0.3 }}
                className="flex items-center gap-4"
              >
                <div className="flex items-center gap-3">
                  <Package className="text-white" size={24} />
                  <span className="text-white font-semibold text-lg">My Packages</span>
                </div>

                {/* Quick search in header */}
                <div className="hidden md:flex items-center gap-2 bg-white/10 rounded-lg px-4 py-2">
                  <Search size={16} className="text-gray-300" />
                  <input
                    type="text"
                    placeholder="Quick search..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-transparent text-white placeholder-gray-300 outline-none text-sm w-32"
                  />
                </div>

                {/* Refresh button */}
                <motion.button
                  whileHover={{ scale: 1.1, rotate: 180 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleRefresh}
                  disabled={refreshing || isLoading}
                  className="w-10 h-10 bg-white/10 hover:bg-white/20 rounded-lg transition-colors flex items-center justify-center disabled:opacity-50"
                  title="Refresh packages"
                >
                  {refreshing || isLoading ? (
                    <Loader2 size={20} className="text-white animate-spin" />
                  ) : (
                    <RefreshCw size={20} className="text-white" />
                  )}
                </motion.button>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Packages List */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.7 }}
            className="space-y-6"
          >
            {/* Loading State */}
            {isLoading && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="bg-white/10 backdrop-blur-lg rounded-3xl p-16 text-center border border-white/20"
              >
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="w-24 h-24 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-6"
                >
                  <Loader2 size={48} className="text-white" />
                </motion.div>
                <h3 className="text-2xl font-bold text-white mb-4">Loading Packages</h3>
                <p className="text-gray-300 text-lg">Fetching your package data...</p>
              </motion.div>
            )}

            {/* Error State */}
            {error && !isLoading && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="bg-white/10 backdrop-blur-lg rounded-3xl p-16 text-center border border-white/20"
              >
                <motion.div
                  animate={{
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="w-24 h-24 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6"
                >
                  <AlertCircle size={48} className="text-white" />
                </motion.div>
                <h3 className="text-2xl font-bold text-white mb-4">Error Loading Packages</h3>
                <p className="text-gray-300 text-lg mb-6">{error}</p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleRefresh}
                  className="px-8 py-4 bg-gradient-to-r from-primary-500 to-secondary-500 text-white font-semibold rounded-xl shadow-2xl"
                >
                  Try Again
                </motion.button>
              </motion.div>
            )}

            {/* Empty State */}
            {!isLoading && !error && filteredPackages.length === 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="bg-white/10 backdrop-blur-lg rounded-3xl p-16 text-center border border-white/20"
              >
                <motion.div
                  animate={{
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="w-24 h-24 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-6"
                >
                  <Package size={48} className="text-white" />
                </motion.div>
                <h3 className="text-2xl font-bold text-white mb-4">
                  {activeTab === 'sent' ? 'No Sent Packages' : 'No Pickup Requests'}
                </h3>
                <p className="text-gray-300 text-lg">
                  {searchQuery.trim()
                    ? `No packages match "${searchQuery}"`
                    : activeTab === 'sent'
                      ? "You haven't sent any packages yet"
                      : "You haven't requested any pickups yet"
                  }
                </p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => navigate('/customer/send-package')}
                  className="mt-8 px-8 py-4 bg-gradient-to-r from-primary-500 to-secondary-500 text-white font-semibold rounded-xl shadow-2xl"
                >
                  Send Your First Package
                </motion.button>
              </motion.div>
            )}

            {/* Packages List */}
            {!isLoading && !error && filteredPackages.length > 0 && (
              <AnimatePresence>
                {filteredPackages.map((pkg, index) => (
                  <motion.div
                    key={pkg.id}
                    initial={{ opacity: 0, y: 30, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -30, scale: 0.9 }}
                    transition={{
                      duration: 0.6,
                      delay: index * 0.1,
                      type: "spring",
                      stiffness: 100
                    }}
                    whileHover={{
                      y: -8,
                      scale: 1.02,
                      transition: { duration: 0.3 }
                    }}
                    className="bg-white/10 backdrop-blur-lg rounded-3xl border border-white/20 overflow-hidden hover:bg-white/15 transition-all duration-500 group"
                  >
                    {/* Package Header */}
                    <div className="p-8 border-b border-white/10">
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center gap-4">
                          <motion.div
                            whileHover={{ rotate: 360 }}
                            transition={{ duration: 0.6 }}
                            className="w-16 h-16 rounded-2xl flex items-center justify-center bg-gradient-to-br from-primary-500 to-secondary-500 shadow-2xl"
                          >
                            {activeTab === 'sent' ? (
                              <Package className="text-white" size={28} />
                            ) : (
                              <RotateCcw className="text-white" size={28} />
                            )}
                          </motion.div>
                          <div>
                            <h3 className="font-bold text-2xl text-white mb-1">#{pkg.packageId.slice(-8)}</h3>
                            <div className="flex items-center gap-2">
                              <span className="text-gray-300 text-lg">{pkg.packageDetails.type}</span>
                              <Star className="text-yellow-400" size={16} />
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <motion.div
                            whileHover={{ scale: 1.05 }}
                            className={`inline-flex items-center gap-3 px-4 py-2 rounded-xl text-lg font-semibold border-2 ${getStatusColor(pkg.status)} backdrop-blur-sm`}
                          >
                            {getStatusIcon(pkg.status)}
                            {getStatusDisplayName(pkg.status)}
                          </motion.div>
                          <p className="text-gray-300 text-sm mt-2">
                            {formatDate(pkg.createdAt).date} • {formatDate(pkg.createdAt).time}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Compact Package Details - Most Important Info Only */}
                    <div className="p-6">
                      <div className="grid md:grid-cols-2 gap-6">
                        {/* Essential Route Info */}
                        <div className="space-y-4">
                          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div className="flex items-center gap-3 mb-3">
                              <MapPin size={16} className="text-blue-400" />
                              <span className="text-gray-300 text-sm font-medium">From</span>
                            </div>
                            <p className="text-white text-sm leading-relaxed truncate">{pkg.pickupAddress.street}</p>
                          </div>

                          {activeTab === 'sent' && pkg.deliveryAddress && (
                            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                              <div className="flex items-center gap-3 mb-3">
                                <Navigation size={16} className="text-green-400" />
                                <span className="text-gray-300 text-sm font-medium">To</span>
                              </div>
                              <p className="text-white text-sm leading-relaxed truncate">{pkg.deliveryAddress.street}</p>
                            </div>
                          )}
                        </div>

                        {/* Essential Package Info */}
                        <div className="space-y-4">
                          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <p className="text-gray-300 text-xs">Type</p>
                                <p className="text-white font-semibold text-sm">{pkg.packageDetails.type}</p>
                              </div>
                              <div>
                                <p className="text-gray-300 text-xs">Est. Time</p>
                                <p className="text-white font-semibold text-sm">{getEstimatedTime(pkg)}</p>
                              </div>
                            </div>
                          </div>

                          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div className="flex justify-between items-center">
                              <div>
                                <p className="text-gray-300 text-xs">Total Cost</p>
                                <p className="text-2xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                                  ₪{pkg.cost}
                                </p>
                              </div>
                              {activeTab === 'sent' && pkg.recipientName && (
                                <div className="text-right">
                                  <p className="text-gray-300 text-xs">Recipient</p>
                                  <p className="text-white font-semibold text-sm">{pkg.recipientName}</p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="mt-8 pt-6 border-t border-white/20">
                        <div className="flex flex-col sm:flex-row gap-4">
                          <motion.button
                            whileHover={{ scale: 1.025 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => {
                              window.location.href = `/customer/package-tracking?packageId=${pkg.packageId}`;
                            }}
                            className="flex-1 px-8 py-4 bg-gradient-to-r from-primary-500 to-secondary-500 text-white font-bold rounded-xl shadow-2xl transition-all duration-300 flex items-center justify-center gap-3 text-lg"
                          >
                            <Eye size={24} />
                            Track Package
                          </motion.button>

                          <motion.button
                            whileHover={{ scale: 1.025 }}
                            whileTap={{ scale: 0.975 }}
                            onClick={() => navigate(`/customer/packages/details/${pkg.packageId}?type=${activeTab}`)}
                            className="px-6 py-4 bg-white/20 hover:bg-white/30 text-white font-semibold rounded-xl transition-all duration-300 flex items-center justify-center gap-2"
                          >
                            <TrendingUp size={20} />
                            Details
                          </motion.button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            )}

            {/* Pagination Controls */}
            {!isLoading && !error && filteredPackages.length > 0 && pagination && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.8 }}
                className="mt-8 flex items-center justify-between bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
              >
                <div className="text-white/70 text-sm">
                  Showing {((currentPage - 1) * packagesPerPage) + 1} to {Math.min(currentPage * packagesPerPage, pagination.totalPackages)} of {pagination.totalPackages} packages
                </div>
                
                <div className="flex items-center gap-2">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                    className={`px-4 py-2 rounded-lg font-semibold transition-all duration-300 ${
                      currentPage === 1
                        ? 'bg-white/10 text-white/30 cursor-not-allowed'
                        : 'bg-white/20 text-white hover:bg-white/30'
                    }`}
                  >
                    Previous
                  </motion.button>
                  
                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                      const pageNum = i + 1;
                      const isActive = pageNum === currentPage;
                      
                      return (
                        <motion.button
                          key={pageNum}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`w-10 h-10 rounded-lg font-semibold transition-all duration-300 ${
                            isActive
                              ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-2xl'
                              : 'bg-white/20 text-white hover:bg-white/30'
                          }`}
                        >
                          {pageNum}
                        </motion.button>
                      );
                    })}
                  </div>
                  
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setCurrentPage(prev => prev + 1)}
                    disabled={currentPage >= pagination.totalPages}
                    className={`px-4 py-2 rounded-lg font-semibold transition-all duration-300 ${
                      currentPage >= pagination.totalPages
                        ? 'bg-white/10 text-white/30 cursor-not-allowed'
                        : 'bg-white/20 text-white hover:bg-white/30'
                    }`}
                  >
                    Next
                  </motion.button>
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>

      {/* Floating Action Button */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 1, type: "spring", stiffness: 200 }}
        className="fixed bottom-8 right-8 z-40"
      >
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => navigate('/customer/send-package')}
          className="w-16 h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full shadow-2xl flex items-center justify-center text-white"
        >
          <motion.div
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          >
            <Zap size={28} />
          </motion.div>
        </motion.button>
      </motion.div>
    </>
  );
};

export default PackagesPage;