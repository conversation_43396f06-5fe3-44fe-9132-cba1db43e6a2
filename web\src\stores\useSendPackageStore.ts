import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Types for send package data
interface PackageDetails {
  type: string;
  description: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  value?: number;
  fragile: boolean;
  size?: 'small' | 'medium' | 'large';
}

interface Address {
  street: string;
  city: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  notes?: string;
}

interface SendPackage {
  _id: string;
  packageId: string;
  trackingNumber?: string; // Added for compatibility with frontend
  userId: string;
  senderName: string;
  senderPhone: string;
  recipientName: string;
  recipientPhone: string;
  pickupAddress: Address;
  deliveryAddress: Address;
  packageDetails: PackageDetails;
  serviceType: 'send_package' | 'request_pickup';
  status: 'pending' | 'confirmed' | 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'cancelled';
  priority: 'standard' | 'express' | 'urgent';
  cost: number;
  paymentMethod: 'cash' | 'card' | 'wallet';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  scheduledPickupTime?: string;
  actualPickupTime?: string;
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  driverId?: string;
  driverName?: string;
  driverPhone?: string;
  trackingUpdates: Array<{
    status: string;
    timestamp: string;
    message: string;
    location?: {
      lat: number;
      lng: number;
    };
  }>;
  photos?: {
    pickup?: string[];
    delivery?: string[];
  };
  signature?: {
    recipientSignature?: string;
    recipientName?: string;
    timestamp?: string;
  };
  notes?: string;
  rating?: {
    score: number;
    comment?: string;
    ratedAt: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface SendPackageStore {
  // State
  packages: SendPackage[];
  currentPackage: SendPackage | null;
  loading: boolean;
  error: string | null;

  // Actions
  addPackage: (packageData: SendPackage) => void;
  setCurrentPackage: (packageData: SendPackage | null) => void;
  getPackageByTrackingNumber: (trackingNumber: string) => SendPackage | undefined;
  getPackageById: (id: string) => SendPackage | undefined;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearPackages: () => void;
}

export const useSendPackageStore = create<SendPackageStore>()(
  persist(
    (set, get) => ({
      // Initial state
      packages: [],
      currentPackage: null,
      loading: false,
      error: null,

      // Actions
      addPackage: (packageData) => {
        set((state) => {
          const existingIndex = state.packages.findIndex(
            (pkg) => pkg.packageId === packageData.packageId || pkg._id === packageData._id
          );

          if (existingIndex >= 0) {
            // Update existing package
            const updatedPackages = [...state.packages];
            updatedPackages[existingIndex] = packageData;
            return { packages: updatedPackages };
          } else {
            // Add new package
            return { packages: [...state.packages, packageData] };
          }
        });
      },

      setCurrentPackage: (packageData) => {
        set({ currentPackage: packageData });
      },

      getPackageByTrackingNumber: (trackingNumber) => {
        const state = get();
        return state.packages.find(pkg => 
          pkg.packageId === trackingNumber || 
          pkg.trackingNumber === trackingNumber ||
          pkg._id === trackingNumber
        );
      },

      getPackageById: (id) => {
        const state = get();
        return state.packages.find(pkg => pkg._id === id);
      },

      setLoading: (loading) => {
        set({ loading });
      },

      setError: (error) => {
        set({ error });
      },

      clearPackages: () => {
        set({ packages: [], currentPackage: null });
      },
    }),
    {
      name: 'send-package-store', // unique name for localStorage key
      partialize: (state) => ({
        packages: state.packages,
        currentPackage: state.currentPackage,
      }),
    }
  )
); 