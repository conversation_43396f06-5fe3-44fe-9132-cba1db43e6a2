import React from 'react';
import AppRouter from './router/AppRouter';
import { AuthProvider } from './contexts/AuthContext';
import { LanguageProvider } from './components/common/LanguageProvider';
import './i18n'; // Import i18n configuration
import './App.css';

function App() {
  return (
    <LanguageProvider>
      <AuthProvider>
        <div className="App">
          <AppRouter />
        </div>
      </AuthProvider>
    </LanguageProvider>
  );
}

export default App;
