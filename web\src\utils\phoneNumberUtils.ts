import { parsePhoneNumber, isValidPhoneNumber, getCountryCallingCode } from 'libphonenumber-js';

export interface CountryCode {
  code: string;
  name: string;
  dialCode: string;
  flag: string;
}

// Popular countries with their codes and flags
export const popularCountries: CountryCode[] = [
  { code: 'PS', name: 'Palestine', dialCode: '+970', flag: '🇵🇸' },
  { code: 'JO', name: 'Jordan', dialCode: '+962', flag: '🇯🇴' },
  { code: 'IL', name: 'Israel', dialCode: '+972', flag: '🇮🇱' },
  { code: 'EG', name: 'Egypt', dialCode: '+20', flag: '🇪🇬' },
  { code: 'SA', name: 'Saudi Arabia', dialCode: '+966', flag: '🇸🇦' },
  { code: 'AE', name: 'UAE', dialCode: '+971', flag: '🇦🇪' },
  { code: 'LB', name: 'Lebanon', dialCode: '+961', flag: '🇱🇧' },
  { code: 'SY', name: 'Syria', dialCode: '+963', flag: '🇸🇾' },
  { code: 'IQ', name: 'Iraq', dialCode: '+964', flag: '🇮🇶' },
  { code: 'KW', name: 'Kuwait', dialCode: '+965', flag: '🇰🇼' },
  { code: 'QA', name: 'Qatar', dialCode: '+974', flag: '🇶🇦' },
  { code: 'BH', name: 'Bahrain', dialCode: '+973', flag: '🇧🇭' },
  { code: 'OM', name: 'Oman', dialCode: '+968', flag: '🇴🇲' },
  { code: 'YE', name: 'Yemen', dialCode: '+967', flag: '🇾🇪' },
  { code: 'US', name: 'United States', dialCode: '+1', flag: '🇺🇸' },
  { code: 'GB', name: 'United Kingdom', dialCode: '+44', flag: '🇬🇧' },
  { code: 'DE', name: 'Germany', dialCode: '+49', flag: '🇩🇪' },
  { code: 'FR', name: 'France', dialCode: '+33', flag: '🇫🇷' },
  { code: 'IT', name: 'Italy', dialCode: '+39', flag: '🇮🇹' },
  { code: 'ES', name: 'Spain', dialCode: '+34', flag: '🇪🇸' },
  { code: 'CA', name: 'Canada', dialCode: '+1', flag: '🇨🇦' },
  { code: 'AU', name: 'Australia', dialCode: '+61', flag: '🇦🇺' },
  { code: 'IN', name: 'India', dialCode: '+91', flag: '🇮🇳' },
  { code: 'CN', name: 'China', dialCode: '+86', flag: '🇨🇳' },
  { code: 'JP', name: 'Japan', dialCode: '+81', flag: '🇯🇵' },
  { code: 'KR', name: 'South Korea', dialCode: '+82', flag: '🇰🇷' },
  { code: 'BR', name: 'Brazil', dialCode: '+55', flag: '🇧🇷' },
  { code: 'MX', name: 'Mexico', dialCode: '+52', flag: '🇲🇽' },
  { code: 'AR', name: 'Argentina', dialCode: '+54', flag: '🇦🇷' },
  { code: 'CL', name: 'Chile', dialCode: '+56', flag: '🇨🇱' },
  { code: 'CO', name: 'Colombia', dialCode: '+57', flag: '🇨🇴' },
  { code: 'PE', name: 'Peru', dialCode: '+51', flag: '🇵🇪' },
  { code: 'VE', name: 'Venezuela', dialCode: '+58', flag: '🇻🇪' },
  { code: 'EC', name: 'Ecuador', dialCode: '+593', flag: '🇪🇨' },
  { code: 'BO', name: 'Bolivia', dialCode: '+591', flag: '🇧🇴' },
  { code: 'PY', name: 'Paraguay', dialCode: '+595', flag: '🇵🇾' },
  { code: 'UY', name: 'Uruguay', dialCode: '+598', flag: '🇺🇾' },
  { code: 'GY', name: 'Guyana', dialCode: '+592', flag: '🇬🇾' },
  { code: 'SR', name: 'Suriname', dialCode: '+597', flag: '🇸🇷' },
  { code: 'FK', name: 'Falkland Islands', dialCode: '+500', flag: '🇫🇰' },
  { code: 'GF', name: 'French Guiana', dialCode: '+594', flag: '🇬🇫' },
];

/**
 * Validates a phone number for a specific country
 * @param phoneNumber - The phone number to validate
 * @param countryCode - The country code (e.g., 'PS', 'US')
 * @returns Validation result object
 */
export const validatePhoneNumber = (phoneNumber: string, countryCode: string) => {
  try {
    // Remove any non-digit characters except + and spaces
    const cleanedNumber = phoneNumber.replace(/[^\d+\s]/g, '');
    
    // If no country code is provided, assume it's for the specified country
    let numberToValidate = cleanedNumber;
    if (!cleanedNumber.startsWith('+')) {
      const dialCode = getCountryCallingCode(countryCode as any);
      numberToValidate = `+${dialCode}${cleanedNumber}`;
    }

    const parsedNumber = parsePhoneNumber(numberToValidate);
    
    return {
      isValid: parsedNumber ? parsedNumber.isValid() : false,
      formattedNumber: parsedNumber ? parsedNumber.formatInternational() : phoneNumber,
      country: parsedNumber?.country,
      nationalNumber: parsedNumber?.nationalNumber,
      internationalNumber: parsedNumber?.number,
      type: parsedNumber?.getType(),
      error: null
    };
  } catch (error) {
    return {
      isValid: false,
      formattedNumber: phoneNumber,
      country: null,
      nationalNumber: null,
      internationalNumber: null,
      type: null,
      error: error instanceof Error ? error.message : 'Invalid phone number'
    };
  }
};

/**
 * Formats a phone number for display
 * @param phoneNumber - The phone number to format
 * @param countryCode - The country code
 * @returns Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber: string, countryCode: string = 'PS') => {
  try {
    if (!phoneNumber) return '';
    
    // Remove all non-digit characters except +
    let cleanedNumber = phoneNumber.replace(/[^\d+]/g, '');
    
    // If no country code, add it
    if (!cleanedNumber.startsWith('+')) {
      const dialCode = getCountryCallingCode(countryCode as any);
      cleanedNumber = `+${dialCode}${cleanedNumber}`;
    }
    
    const parsedNumber = parsePhoneNumber(cleanedNumber);
    return parsedNumber ? parsedNumber.formatInternational() : phoneNumber;
  } catch (error) {
    return phoneNumber;
  }
};

/**
 * Extracts the national number from a full phone number
 * @param phoneNumber - The full phone number
 * @param countryCode - The country code
 * @returns National number without country code
 */
export const extractNationalNumber = (phoneNumber: string, countryCode: string = 'PS') => {
  try {
    if (!phoneNumber) return '';
    
    let cleanedNumber = phoneNumber.replace(/[^\d+]/g, '');
    
    if (!cleanedNumber.startsWith('+')) {
      const dialCode = getCountryCallingCode(countryCode as any);
      cleanedNumber = `+${dialCode}${cleanedNumber}`;
    }
    
    const parsedNumber = parsePhoneNumber(cleanedNumber);
    return parsedNumber ? parsedNumber.nationalNumber : phoneNumber;
  } catch (error) {
    return phoneNumber;
  }
};

/**
 * Gets the country calling code for a country
 * @param countryCode - The country code
 * @returns The dial code
 */
export const getDialCode = (countryCode: string): string => {
  try {
    return getCountryCallingCode(countryCode as any);
  } catch (error) {
    return '+970'; // Default to Palestine
  }
};

/**
 * Finds a country by its code
 * @param countryCode - The country code to find
 * @returns Country object or null
 */
export const findCountryByCode = (countryCode: string): CountryCode | null => {
  return popularCountries.find(country => country.code === countryCode) || null;
};

/**
 * Searches countries by name or code
 * @param query - Search query
 * @returns Filtered countries
 */
export const searchCountries = (query: string): CountryCode[] => {
  const lowerQuery = query.toLowerCase();
  return popularCountries.filter(country => 
    country.name.toLowerCase().includes(lowerQuery) ||
    country.code.toLowerCase().includes(lowerQuery) ||
    country.dialCode.includes(query)
  );
};

/**
 * Gets the default country based on user's location or preference
 * @returns Default country code
 */
export const getDefaultCountry = (): string => {
  // Try to detect from browser locale
  const locale = navigator.language || 'en-US';
  const countryFromLocale = locale.split('-')[1];
  
  if (countryFromLocale && findCountryByCode(countryFromLocale)) {
    return countryFromLocale;
  }
  
  // Default to Palestine for this application
  return 'PS';
};

/**
 * Validates if a phone number is required and properly formatted
 * @param phoneNumber - The phone number to validate
 * @param countryCode - The country code
 * @param isRequired - Whether the field is required
 * @returns Validation result
 */
export const validatePhoneField = (
  phoneNumber: string, 
  countryCode: string, 
  isRequired: boolean = true
) => {
  if (!phoneNumber && !isRequired) {
    return { isValid: true, message: '' };
  }
  
  if (!phoneNumber && isRequired) {
    return { isValid: false, message: 'Phone number is required' };
  }
  
  const validation = validatePhoneNumber(phoneNumber, countryCode);
  
  if (!validation.isValid) {
    return { 
      isValid: false, 
      message: 'Please enter a valid phone number for the selected country' 
    };
  }
  
  return { isValid: true, message: '' };
}; 