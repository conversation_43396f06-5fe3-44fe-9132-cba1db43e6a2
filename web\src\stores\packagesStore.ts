import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiService } from '../services/api';

type Location = {
  lat: number;
  lng: number;
  address?: string;
};

export type Package = {
  id: string;
  packageId: string;
  createdAt: string;
  serviceType: 'send_package' | 'request_pickup';
  status: 'pending' | 'confirmed' | 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'cancelled';
  priority: 'standard' | 'express' | 'urgent';
  cost: number;
  paymentMethod: 'cash' | 'card' | 'wallet';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  
  // Sender information
  senderName: string;
  senderPhone: string;
  
  // Recipient information (for send packages)
  recipientName?: string;
  recipientPhone?: string;
  
  // Addresses
  pickupAddress: {
    street: string;
    city: string;
    coordinates: {
      lat: number;
      lng: number;
    };
    notes?: string;
  };
  deliveryAddress?: {
    street: string;
    city: string;
    coordinates: {
      lat: number;
      lng: number;
    };
    notes?: string;
  };
  
  // Package details
  packageDetails: {
    type: string;
    description: string;
    weight?: number;
    dimensions?: {
      length: number;
      width: number;
      height: number;
    };
    value?: number;
    fragile: boolean;
  };
  
  // Timing
  scheduledPickupTime?: string;
  actualPickupTime?: string;
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  
  // Driver information
  driverId?: string;
  driverName?: string;
  driverPhone?: string;
  
  // Tracking updates
  trackingUpdates: Array<{
    status: string;
    timestamp: string;
    message: string;
    location?: {
      lat: number;
      lng: number;
    };
  }>;
  
  // Additional data
  photos?: {
    pickup?: string[];
    delivery?: string[];
  };
  signature?: {
    recipientSignature?: string;
    recipientName?: string;
    timestamp?: string;
  };
  notes?: string;
  rating?: {
    score: number;
    comment?: string;
    ratedAt: string;
  };
};

type PaginationInfo = {
  currentPage: number;
  totalPages: number;
  totalPackages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
};

type PackageStats = {
  sendPackagesTotal: number;
  pickupPackagesTotal: number;
  totalPackages: number;
};

type PackagesState = {
  packages: Package[];
  isLoading: boolean;
  error: string | null;
  lastFetch: number | null;
  pagination: PaginationInfo | null;
  stats: PackageStats | null;

  // Local state management
  addPackage: (pack: Package) => void;
  addMultiplePackages: (packages: Package[]) => void;
  updatePackageStatus: (packageId: string, status: Package['status']) => void;
  getPackageById: (packageId: string) => Package | undefined;
  getPackagesByStatus: (status: Package['status']) => Package[];
  getPackagesByType: (serviceType: Package['serviceType']) => Package[];
  clearPackages: () => void;

  // Backend integration
  fetchPackages: (forceRefresh?: boolean, params?: any) => Promise<void>;
  refreshPackages: () => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  mapBackendStatus: (backendStatus: string) => Package['status'];
};

export const usePackagesStore = create<PackagesState>()(
  persist(
    (set, get) => ({
      packages: [],
      isLoading: false,
      error: null,
      lastFetch: null,
      pagination: null,
      stats: null,

      // Local state management
      addPackage: (pack) => {
        set((state) => ({
          packages: [pack, ...state.packages]
        }));
      },

      addMultiplePackages: (packages) => {
        set((state) => ({
          packages: [...packages, ...state.packages]
        }));
      },

      updatePackageStatus: (packageId, status) => {
        set((state) => ({
          packages: state.packages.map((pkg) =>
            pkg.id === packageId ? { ...pkg, status } : pkg
          )
        }));
      },

      getPackageById: (packageId) => {
        return get().packages.find((pkg) => pkg.id === packageId);
      },

      getPackagesByStatus: (status) => {
        return get().packages.filter((pkg) => pkg.status === status);
      },

      getPackagesByType: (serviceType) => {
        return get().packages.filter((pkg) => pkg.serviceType === serviceType);
      },

      clearPackages: () => {
        set({ packages: [], error: null, stats: null });
      },

      setLoading: (isLoading) => {
        set({ isLoading });
      },

      setError: (error) => {
        set({ error });
      },

      // Backend integration with server-side pagination
      fetchPackages: async (forceRefresh = false, params = {}) => {
        const state = get();
        const now = Date.now();
        const CACHE_DURATION = 2 * 60 * 1000; // 2 minutes

        // Skip cache for pagination or search/filter changes
        const shouldSkipCache = forceRefresh || params.page > 1 || params.status || params.type;

        if (!shouldSkipCache && state.lastFetch && (now - state.lastFetch) < CACHE_DURATION) {
          return;
        }

        set({ isLoading: true, error: null });

        try {
          // Fetch with server-side pagination parameters
          const response = await apiService.getUserPackages(params);

                     if (response.success && response.data && Array.isArray(response.data)) {
             const backendPackages = response.data.map((pkg: any) => {
              // Handle different possible field names from backend
              const packageId = pkg.packageId || pkg._id || pkg.id;
              const trackingNumber = pkg.trackingNumber || pkg.packageId || pkg._id;

              return {
                id: trackingNumber,
                packageId: packageId,
                createdAt: pkg.createdAt || new Date().toISOString(),
                serviceType: pkg.serviceType || 'send_package',
                status: get().mapBackendStatus(pkg.status),
                priority: pkg.priority || 'standard',
                cost: pkg.cost || 0,
                paymentMethod: (pkg.paymentMethod as 'cash' | 'card' | 'wallet') || 'cash',
                paymentStatus: pkg.paymentStatus || 'pending',
                
                // Sender information
                senderName: pkg.senderName || '',
                senderPhone: pkg.senderPhone || '',
                
                // Recipient information
                recipientName: pkg.recipientName || '',
                recipientPhone: pkg.recipientPhone || '',
                
                // Addresses
                pickupAddress: {
                  street: pkg.pickupAddress?.street || '',
                  city: pkg.pickupAddress?.city || '',
                  coordinates: {
                    lat: pkg.pickupAddress?.coordinates?.lat || 0,
                    lng: pkg.pickupAddress?.coordinates?.lng || 0,
                  },
                  notes: pkg.pickupAddress?.notes || '',
                },
                deliveryAddress: pkg.deliveryAddress ? {
                  street: pkg.deliveryAddress.street || '',
                  city: pkg.deliveryAddress.city || '',
                  coordinates: {
                    lat: pkg.deliveryAddress.coordinates?.lat || 0,
                    lng: pkg.deliveryAddress.coordinates?.lng || 0,
                  },
                  notes: pkg.deliveryAddress.notes || '',
                } : undefined,
                
                // Package details
                packageDetails: {
                  type: pkg.packageDetails?.type || '',
                  description: pkg.packageDetails?.description || '',
                  weight: pkg.packageDetails?.weight || undefined,
                  dimensions: pkg.packageDetails?.dimensions || undefined,
                  value: pkg.packageDetails?.value || undefined,
                  fragile: pkg.packageDetails?.fragile || false,
                },
                
                // Timing
                scheduledPickupTime: pkg.scheduledPickupTime || undefined,
                actualPickupTime: pkg.actualPickupTime || undefined,
                estimatedDeliveryTime: pkg.estimatedDeliveryTime || undefined,
                actualDeliveryTime: pkg.actualDeliveryTime || undefined,
                
                // Driver information
                driverId: pkg.driverId || undefined,
                driverName: pkg.driverName || undefined,
                driverPhone: pkg.driverPhone || undefined,
                
                // Tracking updates
                trackingUpdates: pkg.trackingUpdates || [],
                
                // Additional data
                photos: pkg.photos || undefined,
                signature: pkg.signature || undefined,
                notes: pkg.notes || undefined,
                rating: pkg.rating || undefined,
              };
            });

                         // Extract pagination info from response
            const paginationData = response.pagination || {};

            // Use backend pagination data directly
            const paginationInfo: PaginationInfo = {
              currentPage: paginationData.page || params.page || 1,
              totalPages: paginationData.pages || 1,
              totalPackages: paginationData.total || backendPackages.length,
              hasNextPage: paginationData.hasNextPage !== undefined ? paginationData.hasNextPage : false,
              hasPrevPage: paginationData.hasPrevPage !== undefined ? paginationData.hasPrevPage : false,
            };

            // Extract stats from response
            const statsData = response.stats || {
              sendPackagesTotal: 0,
              pickupPackagesTotal: 0,
              totalPackages: 0
            };

            set({
              packages: backendPackages,
              pagination: paginationInfo,
              stats: statsData,
              isLoading: false,
              error: null,
              lastFetch: now
            });
          } else {
            // If no packages or failed response, set empty array
            set({
              packages: [],
              pagination: {
                currentPage: params.page || 1,
                totalPages: 0,
                totalPackages: 0,
                hasNextPage: false,
                hasPrevPage: false,
              },
              stats: null,
              isLoading: false,
              error: response.message || 'No packages found'
            });
          }
        } catch (error) {
          console.error('Error fetching packages:', error);
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch packages',
            stats: null
          });
        }
      },

      // Helper method to map backend status to frontend status
      mapBackendStatus: (backendStatus: string): Package['status'] => {
        const statusMap: Record<string, Package['status']> = {
          'pending': 'pending',
          'confirmed': 'confirmed',
          'picked_up': 'picked_up',
          'in_transit': 'in_transit',
          'out_for_delivery': 'out_for_delivery',
          'delivered': 'delivered',
          'cancelled': 'cancelled'
        };

        return statusMap[backendStatus?.toLowerCase()] || 'pending';
      },

      refreshPackages: async () => {
        await get().fetchPackages(true);
      },
    }),
    {
      name: 'wasel-packages-storage',
      partialize: (state) => ({
        packages: state.packages,
        lastFetch: state.lastFetch,
      }),
    }
  )
); 