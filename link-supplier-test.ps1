# Link Supplier Test Script

Write-Host "🔗 Linking User Account to Existing Supplier..." -ForegroundColor Green

# Step 1: Login to get access token
Write-Host "`n🔐 Step 1: Logging in..." -ForegroundColor Yellow

$loginData = @{
    email = "<EMAIL>"
    password = "SfE#76132"  # Replace with your actual password
} | ConvertTo-Json

try {
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    $loginResult = $loginResponse.Content | ConvertFrom-Json
    
    if ($loginResult.success) {
        $token = $loginResult.data.accessToken
        Write-Host "✅ Login successful!" -ForegroundColor Green
        Write-Host "   User: $($loginResult.data.user.firstName) $($loginResult.data.user.lastName)" -ForegroundColor Cyan
        Write-Host "   Current Supplier ID: $($loginResult.data.user.supplierId)" -ForegroundColor Cyan
        Write-Host "   Current Store: $($loginResult.data.user.storeName)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Login failed: $($loginResult.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Login error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "⚠️  Please update the password in this script!" -ForegroundColor Yellow
    exit 1
}

# Step 2: Link to existing supplier
Write-Host "`n🔗 Step 2: Linking to existing '3a kefak' supplier..." -ForegroundColor Yellow

$linkData = @{
    existingSupplierId = "3a-kefak"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $linkResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/suppliers/link-to-existing" -Method POST -Body $linkData -Headers $headers
    $linkResult = $linkResponse.Content | ConvertFrom-Json
    
    if ($linkResult.success) {
        Write-Host "✅ Successfully linked to existing supplier!" -ForegroundColor Green
        Write-Host "   New Supplier ID: $($linkResult.data.user.supplierId)" -ForegroundColor Cyan
        Write-Host "   Store Name: $($linkResult.data.user.storeName)" -ForegroundColor Cyan
        Write-Host "   Products Count: $($linkResult.data.supplier.productsCount)" -ForegroundColor Cyan
        Write-Host "   Rating: $($linkResult.data.supplier.rating)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Link failed: $($linkResult.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Link error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Verify the link by checking supplier products
Write-Host "`n🔍 Step 3: Verifying the link..." -ForegroundColor Yellow

try {
    $productsResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/suppliers/3a-kefak/products" -Method GET
    $productsResult = $productsResponse.Content | ConvertFrom-Json
    
    if ($productsResult.success) {
        Write-Host "✅ Verification successful!" -ForegroundColor Green
        Write-Host "   Supplier: $($productsResult.data.supplier.name)" -ForegroundColor Cyan
        Write-Host "   Products: $($productsResult.data.products.Count)" -ForegroundColor Cyan
        Write-Host "   Rating: $($productsResult.data.supplier.rating)" -ForegroundColor Cyan
        
        if ($productsResult.data.products.Count -gt 0) {
            Write-Host "`n📦 Sample products:" -ForegroundColor White
            $productsResult.data.products | Select-Object -First 3 | ForEach-Object {
                Write-Host "   - $($_.name) - $($_.price) NIS" -ForegroundColor Gray
            }
        }
    }
} catch {
    Write-Host "⚠️  Could not verify products: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 4: Test login again to confirm changes
Write-Host "`n🔐 Step 4: Testing login with updated account..." -ForegroundColor Yellow

try {
    $finalLoginResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    $finalLoginResult = $finalLoginResponse.Content | ConvertFrom-Json
    
    if ($finalLoginResult.success) {
        Write-Host "✅ Final login test successful!" -ForegroundColor Green
        Write-Host "   Updated Supplier ID: $($finalLoginResult.data.user.supplierId)" -ForegroundColor Cyan
        Write-Host "   Updated Store Name: $($finalLoginResult.data.user.storeName)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "⚠️  Final login test failed: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n🎉 Process completed!" -ForegroundColor Green
Write-Host "`n📋 SUMMARY:" -ForegroundColor White
Write-Host "✅ Your user account is now linked to the existing '3a kefak' supplier" -ForegroundColor Green
Write-Host "✅ All products from '3a kefak' are now accessible through your account" -ForegroundColor Green
Write-Host "✅ You can login to the mobile app and manage the '3a kefak' store" -ForegroundColor Green
Write-Host "✅ The store appears in customer pages with all existing products" -ForegroundColor Green
Write-Host ""
Write-Host "🔑 LOGIN CREDENTIALS:" -ForegroundColor Yellow
Write-Host "📧 Email: <EMAIL>" -ForegroundColor White
Write-Host "🔑 Password: [Your password]" -ForegroundColor White
Write-Host "🏪 Store: 3a kefak" -ForegroundColor White
Write-Host "🆔 Supplier ID: 3a-kefak" -ForegroundColor White
