/**
 * Numeric Input Utilities
 * 
 * This file contains utility functions for handling numeric input validation,
 * formatting, and data visualization across the application.
 */

/**
 * Rounds a number to the nearest 0.5 step
 * Used for cost displays to show only .0 or .5 decimal places
 */
export const formatNumberToHalfStep = (value: number): number => {
  return Math.round(value * 2) / 2;
};

/**
 * Formats currency values with proper rounding and symbol
 */
export const formatCurrency = (value: number): string => {
  const rounded = formatNumberToHalfStep(value);
  return `₪${rounded.toFixed(1)}`;
};

/**
 * Validates and cleans numeric input
 * @param value - The input value to validate
 * @param allowDecimals - Whether to allow decimal points
 * @param maxDecimalPlaces - Maximum number of decimal places (default: 2)
 * @returns Cleaned numeric string
 */
export const validateNumericInput = (
  value: string, 
  allowDecimals: boolean = true, 
  maxDecimalPlaces: number = 2
): string => {
  // Remove all non-numeric characters except decimal point
  let cleaned = value.replace(/[^\d.]/g, '');
  
  // Ensure only one decimal point
  const parts = cleaned.split('.');
  if (parts.length > 2) {
    cleaned = parts[0] + '.' + parts.slice(1).join('');
  }
  
  // Limit decimal places
  if (parts.length === 2 && parts[1].length > maxDecimalPlaces) {
    cleaned = parts[0] + '.' + parts[1].substring(0, maxDecimalPlaces);
  }
  
  // If decimals not allowed, remove decimal part
  if (!allowDecimals && cleaned.includes('.')) {
    cleaned = cleaned.split('.')[0];
  }
  
  return cleaned;
};

/**
 * Formats phone numbers with proper spacing
 * @param value - Raw phone number input
 * @returns Formatted phone number string
 */
export const formatPhoneNumber = (value: string): string => {
  // Remove all non-numeric characters
  const cleaned = value.replace(/\D/g, '');
  
  // Format as +970 XXX XXX XXX
  if (cleaned.length <= 3) {
    return cleaned;
  } else if (cleaned.length <= 6) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
  } else if (cleaned.length <= 9) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
  } else {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 9)}`;
  }
};

/**
 * Validates package weight with minimum and maximum constraints
 * @param value - Weight input value
 * @param minWeight - Minimum allowed weight (default: 0.1)
 * @param maxWeight - Maximum allowed weight (default: 100)
 * @returns Validated weight string
 */
export const validateWeight = (
  value: string, 
  minWeight: number = 0.1, 
  maxWeight: number = 100
): string => {
  const cleaned = validateNumericInput(value, true);
  const numValue = parseFloat(cleaned);
  
  // Ensure minimum weight
  if (numValue < minWeight && cleaned !== '') {
    return minWeight.toString();
  }
  
  // Limit maximum weight
  if (numValue > maxWeight) {
    return maxWeight.toString();
  }
  
  return cleaned;
};

/**
 * Validates package dimensions (length, width, height)
 * @param value - Dimension input value
 * @param maxDimension - Maximum allowed dimension (default: 200)
 * @returns Validated dimension string
 */
export const validateDimension = (
  value: string, 
  maxDimension: number = 200
): string => {
  const cleaned = validateNumericInput(value, true, 1);
  const numValue = parseFloat(cleaned);
  
  // Ensure positive value
  if (numValue < 0 && cleaned !== '') {
    return '0';
  }
  
  // Limit maximum dimension
  if (numValue > maxDimension) {
    return maxDimension.toString();
  }
  
  return cleaned;
};

/**
 * Validates package value with reasonable limits
 * @param value - Value input
 * @param maxValue - Maximum allowed value (default: 10000)
 * @returns Validated value number
 */
export const validatePackageValue = (
  value: string, 
  maxValue: number = 10000
): number => {
  const cleaned = validateNumericInput(value, true);
  const numValue = parseFloat(cleaned) || 0;
  
  // Ensure non-negative value
  if (numValue < 0) {
    return 0;
  }
  
  // Limit maximum value
  if (numValue > maxValue) {
    return maxValue;
  }
  
  return numValue;
};

/**
 * Formats a number with proper decimal places for display
 * @param value - Number to format
 * @param decimalPlaces - Number of decimal places (default: 1)
 * @returns Formatted number string
 */
export const formatNumber = (value: number, decimalPlaces: number = 1): string => {
  return value.toFixed(decimalPlaces);
};

/**
 * Validates credit card number format
 * @param value - Card number input
 * @returns Formatted card number string
 */
export const formatCardNumber = (value: string): string => {
  // Remove all non-numeric characters
  const cleaned = value.replace(/\D/g, '');
  
  // Format as XXXX XXXX XXXX XXXX
  const groups = cleaned.match(/.{1,4}/g);
  return groups ? groups.join(' ').substring(0, 19) : cleaned;
};

/**
 * Validates CVV code
 * @param value - CVV input
 * @returns Validated CVV string
 */
export const validateCvv = (value: string): string => {
  // Remove all non-numeric characters
  const cleaned = value.replace(/\D/g, '');
  
  // Limit to 3-4 digits
  return cleaned.substring(0, 4);
};

/**
 * Formats cost breakdown for display with proper rounding
 * @param breakdown - Cost breakdown object
 * @returns Formatted breakdown object
 */
export const formatCostBreakdown = (breakdown: {
  baseCost: number;
  sizeMultiplier: number;
  fragileFee: number;
  insuranceFee: number;
  total: number;
}) => {
  return {
    baseCost: formatNumberToHalfStep(breakdown.baseCost),
    sizeMultiplier: breakdown.sizeMultiplier,
    fragileFee: formatNumberToHalfStep(breakdown.fragileFee),
    insuranceFee: formatNumberToHalfStep(breakdown.insuranceFee),
    total: formatNumberToHalfStep(breakdown.total)
  };
};

/**
 * Validates quantity input with minimum and maximum constraints
 * @param value - Quantity input
 * @param minQty - Minimum quantity (default: 1)
 * @param maxQty - Maximum quantity (default: 99)
 * @returns Validated quantity string
 */
export const validateQuantity = (
  value: string, 
  minQty: number = 1, 
  maxQty: number = 99
): string => {
  const cleaned = validateNumericInput(value, false);
  const numValue = parseInt(cleaned) || 0;
  
  // Ensure minimum quantity
  if (numValue < minQty && cleaned !== '') {
    return minQty.toString();
  }
  
  // Limit maximum quantity
  if (numValue > maxQty) {
    return maxQty.toString();
  }
  
  return cleaned;
};

/**
 * Creates a numeric input handler with validation
 * @param updateField - Function to update form field
 * @param field - Field name to update
 * @param validationFn - Optional custom validation function
 * @returns Input change handler function
 */
export const createNumericInputHandler = (
  updateField: (field: string, value: any) => void,
  field: string,
  validationFn?: (value: string) => string
) => {
  return (value: string) => {
    const processedValue = validationFn ? validationFn(value) : validateNumericInput(value);
    updateField(field, processedValue);
  };
};

/**
 * Creates a phone number input handler
 * @param updateField - Function to update form field
 * @param field - Field name to update
 * @returns Input change handler function
 */
export const createPhoneInputHandler = (
  updateField: (field: string, value: any) => void,
  field: string
) => {
  return (value: string) => {
    const formatted = formatPhoneNumber(value);
    updateField(field, formatted);
  };
}; 