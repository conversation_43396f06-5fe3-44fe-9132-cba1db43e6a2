import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// Enhanced types to match backend structure
interface PackageDetails {
  type: string;
  description: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  value?: number;
  fragile: boolean;
  size?: 'small' | 'medium' | 'large';
}

interface Address {
  street: string;
  city: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  notes?: string;
}

interface PickupRequest {
  _id: string;
  packageId: string;
  trackingNumber?: string; // Added for compatibility with frontend
  userId: string;
  senderName: string;
  senderPhone: string;
  recipientName: string;
  recipientPhone: string;
  pickupAddress: Address;
  deliveryAddress: Address;
  packageDetails: PackageDetails;
  serviceType: 'send_package' | 'request_pickup';
  status: 'pending' | 'confirmed' | 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'cancelled';
  priority: 'standard' | 'express' | 'urgent';
  cost: number;
  paymentMethod: 'cash' | 'card' | 'wallet';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  scheduledPickupTime?: string;
  actualPickupTime?: string;
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  driverId?: string;
  driverName?: string;
  driverPhone?: string;
  trackingUpdates: Array<{
    status: string;
    timestamp: string;
    message: string;
    location?: {
      lat: number;
      lng: number;
    };
  }>;
  photos?: {
    pickup?: string[];
    delivery?: string[];
  };
  signature?: {
    recipientSignature?: string;
    recipientName?: string;
    timestamp?: string;
  };
  notes?: string;
  rating?: {
    score: number;
    comment?: string;
    ratedAt: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface RequestPickupStore {
  // State
  packages: PickupRequest[];
  currentPackage: PickupRequest | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  addPackage: (packageData: PickupRequest) => void;
  setCurrentPackage: (packageData: PickupRequest | null) => void;
  getPackageById: (packageId: string) => PickupRequest | undefined;
  getPackageByTrackingNumber: (trackingNumber: string) => PickupRequest | undefined;
  updatePackageStatus: (packageId: string, status: PickupRequest['status']) => void;
  updatePackage: (packageId: string, updates: Partial<PickupRequest>) => void;
  removePackage: (packageId: string) => void;
  clearPackages: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Computed
  getRecentPackages: (limit?: number) => PickupRequest[];
  getPackagesByStatus: (status: PickupRequest['status']) => PickupRequest[];
  getActivePackages: () => PickupRequest[];
}

export const useRequestPickupStore = create<RequestPickupStore>()(
  persist(
    (set, get) => ({
      // State
      packages: [],
      currentPackage: null,
      loading: false,
      error: null,
      
      // Actions
      addPackage: (packageData) =>
        set((state) => {
          // Check if package already exists
          const existingIndex = state.packages.findIndex(
            pkg => pkg._id === packageData._id || pkg.packageId === packageData.packageId
          );
          
          if (existingIndex >= 0) {
            // Update existing package
            const updatedPackages = [...state.packages];
            updatedPackages[existingIndex] = { ...updatedPackages[existingIndex], ...packageData };
            return { packages: updatedPackages };
          } else {
            // Add new package
            return { packages: [...state.packages, packageData] };
          }
        }),
      
      setCurrentPackage: (packageData) =>
        set({ currentPackage: packageData }),
      
      getPackageById: (packageId) => {
        const state = get();
        return state.packages.find(pkg => pkg._id === packageId);
      },
      
      getPackageByTrackingNumber: (trackingNumber) => {
        const state = get();
        return state.packages.find(pkg => 
          pkg.packageId === trackingNumber || 
          pkg.trackingNumber === trackingNumber ||
          pkg._id === trackingNumber
        );
      },
      
      updatePackageStatus: (packageId, status) =>
        set((state) => ({
          packages: state.packages.map(pkg => 
            pkg._id === packageId || pkg.packageId === packageId
              ? { ...pkg, status }
              : pkg
          ),
          currentPackage: state.currentPackage && 
            (state.currentPackage._id === packageId || state.currentPackage.packageId === packageId)
              ? { ...state.currentPackage, status }
              : state.currentPackage
        })),
      
      updatePackage: (packageId, updates) =>
        set((state) => ({
          packages: state.packages.map(pkg => 
            pkg._id === packageId || pkg.packageId === packageId
              ? { ...pkg, ...updates }
              : pkg
          ),
          currentPackage: state.currentPackage && 
            (state.currentPackage._id === packageId || state.currentPackage.packageId === packageId)
              ? { ...state.currentPackage, ...updates }
              : state.currentPackage
        })),
      
      removePackage: (packageId) =>
        set((state) => ({
          packages: state.packages.filter(pkg => 
            pkg._id !== packageId && pkg.packageId !== packageId
          ),
          currentPackage: state.currentPackage && 
            (state.currentPackage._id === packageId || state.currentPackage.packageId === packageId)
              ? null
              : state.currentPackage
        })),
      
      clearPackages: () =>
        set({ packages: [], currentPackage: null }),
      
      setLoading: (loading) =>
        set({ loading }),
      
      setError: (error) =>
        set({ error }),
      
      // Computed
      getRecentPackages: (limit = 10) => {
        const state = get();
        return state.packages
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, limit);
      },
      
      getPackagesByStatus: (status) => {
        const state = get();
        return state.packages.filter(pkg => pkg.status === status);
      },
      
      getActivePackages: () => {
        const state = get();
        return state.packages.filter(pkg => 
          pkg.status !== 'delivered' && pkg.status !== 'cancelled'
        );
      },
    }),
    {
      name: 'request-pickup-store', // unique name for localStorage
      partialize: (state) => ({ 
        packages: state.packages,
        currentPackage: state.currentPackage 
      }), // Only persist packages and currentPackage
    }
  )
);

// Export types for use in components
export type { PickupRequest, PackageDetails, Address }; 