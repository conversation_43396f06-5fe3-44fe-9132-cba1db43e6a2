@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: #0f172a;
  overflow-x: hidden;
}

#root {
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Custom components */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .page-container {
    @apply min-h-screen bg-gray-50;
  }

  .content-wrapper {
    @apply w-full;
  }

  /* RTL Support */
  [dir="rtl"] {
    direction: rtl;
    text-align: right;
  }

  [dir="rtl"] .text-left {
    text-align: right;
  }

  [dir="rtl"] .text-right {
    text-align: left;
  }

  [dir="rtl"] .ml-auto {
    margin-left: unset;
    margin-right: auto;
  }

  [dir="rtl"] .mr-auto {
    margin-right: unset;
    margin-left: auto;
  }

  [dir="rtl"] .pl-4 {
    padding-left: unset;
    padding-right: 1rem;
  }

  [dir="rtl"] .pr-4 {
    padding-right: unset;
    padding-left: 1rem;
  }

  [dir="rtl"] .border-l {
    border-left: unset;
    border-right: 1px solid;
  }

  [dir="rtl"] .border-r {
    border-right: unset;
    border-left: 1px solid;
  }

  [dir="rtl"] .rounded-l-lg {
    border-top-left-radius: unset;
    border-bottom-left-radius: unset;
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
  }

  [dir="rtl"] .rounded-r-lg {
    border-top-right-radius: unset;
    border-bottom-right-radius: unset;
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
  }

  /* Smart input direction based on content */
  [dir="rtl"] input[type="text"],
  [dir="rtl"] input[type="email"],
  [dir="rtl"] input[type="password"],
  [dir="rtl"] input[type="number"],
  [dir="rtl"] input[type="tel"],
  [dir="rtl"] input[type="url"],
  [dir="rtl"] input[type="search"],
  [dir="rtl"] textarea {
    direction: auto !important;
    text-align: start !important;
  }

  /* Remove the input-ltr class since we're using smart detection */
  .input-ltr {
    direction: auto !important;
    text-align: start !important;
  }

  /* Center placeholders while preserving input value alignment */
  input::placeholder,
  textarea::placeholder {
    text-align: center !important;
  }

  /* Ensure RTL placeholders are also centered */
  [dir="rtl"] input::placeholder,
  [dir="rtl"] textarea::placeholder {
    text-align: center !important;
  }

  /* Input icons should stay on the left side */
  [dir="rtl"] .input-with-icon {
    flex-direction: row !important;
  }

  /* Ensure input containers maintain proper spacing */
  [dir="rtl"] .input-container {
    direction: ltr !important;
  }

  /* Specific class for LTR inputs */
  .input-ltr {
    direction: ltr !important;
    text-align: left !important;
  }

  [dir="rtl"] .input-ltr {
    direction: ltr !important;
    text-align: left !important;
  }

  /* Logo protection - prevent RTL from affecting logo layout */
  [dir="rtl"] .flex.items-center.justify-center {
    direction: ltr !important;
  }

  /* Ensure logo text and elements maintain their original direction */
  [dir="rtl"] .flex.items-center.justify-center * {
    direction: ltr !important;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Enhanced logo animations */
  .logo-glow {
    animation: logoGlow 3s ease-in-out infinite alternate;
  }

  @keyframes logoGlow {
    from {
      text-shadow: 0 0 20px rgba(117, 41, 179, 0.5), 0 0 40px rgba(103, 179, 41, 0.3);
    }
    to {
      text-shadow: 0 0 30px rgba(147, 51, 234, 0.8), 0 0 60px rgba(251, 191, 36, 0.5), 0 0 80px rgba(103, 179, 41, 0.4);
    }
  }

  .lightning-pulse {
    animation: lightningPulse 2s ease-in-out infinite;
  }

  @keyframes lightningPulse {
    0%, 100% {
      filter: brightness(1) saturate(1);
    }
    50% {
      filter: brightness(1.3) saturate(1.5);
    }
  }

  .electric-text {
    background: linear-gradient(45deg, #9333EA, #7529B3, #FBBF24, #67B329, #059669);
    background-size: 300% 300%;
    animation: electricFlow 3s ease-in-out infinite;
  }

  /* Hide scrollbar for horizontal scroll */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  @keyframes electricFlow {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
}

/* Custom scrollbar for phone input dropdown */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* Firefox scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) rgba(255, 255, 255, 0.1);
}

/* Enhanced focus styles for phone input */
.phone-input-focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 0 0 1px rgba(59, 130, 246, 0.2);
}

/* Country selector button hover effect */
.country-selector-hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Dropdown animation enhancements */
.dropdown-enter {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.dropdown-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.dropdown-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.dropdown-exit-active {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
  transition: opacity 0.2s ease, transform 0.2s ease;
}
