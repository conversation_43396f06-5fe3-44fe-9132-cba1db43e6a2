import React from 'react';
import { motion } from 'framer-motion';
import { LanguageSwitcher } from '../../components/common/LanguageSwitcher';
import { LanguageIndicator } from '../../components/common/LanguageIndicator';
import { LanguageSettings } from '../../components/common/LanguageSettings';

const LanguageDemoPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent mb-4">
            Language Switcher Components
          </h1>
          <p className="text-gray-600 text-lg">
            Explore different variants of our enhanced language switcher components
          </p>
        </motion.div>

        {/* Floating Language Switcher */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Floating Language Switcher</h2>
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <p className="text-gray-600 mb-6">
              A floating language switcher that appears in the top-right corner. Perfect for login pages and main layouts.
            </p>
            <div className="flex justify-center">
              <LanguageSwitcher variant="floating" position="top-right" />
            </div>
          </div>
        </motion.div>

        {/* Compact Language Switcher */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Compact Language Switcher</h2>
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <p className="text-gray-600 mb-6">
              A compact language switcher with both languages visible. Great for headers and navigation bars.
            </p>
            <div className="flex justify-center">
              <LanguageSwitcher variant="compact" />
            </div>
          </div>
        </motion.div>

        {/* Dropdown Language Switcher */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Dropdown Language Switcher</h2>
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <p className="text-gray-600 mb-6">
              A dropdown language switcher with detailed language information. Perfect for settings and profile pages.
            </p>
            <div className="flex justify-center">
              <LanguageSwitcher variant="dropdown" />
            </div>
          </div>
        </motion.div>

        {/* Language Indicator */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Language Indicator</h2>
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <p className="text-gray-600 mb-6">
              A simple language indicator that shows the current language. Useful for displaying current language status.
            </p>
            <div className="flex justify-center gap-4">
              <LanguageIndicator size="sm" />
              <LanguageIndicator size="md" />
              <LanguageIndicator size="lg" />
            </div>
          </div>
        </motion.div>

        {/* Language Settings - Card Variant */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Language Settings - Card</h2>
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <p className="text-gray-600 mb-6">
              A comprehensive language settings component with card layout. Ideal for settings pages.
            </p>
            <div className="max-w-md mx-auto">
              <LanguageSettings variant="card" />
            </div>
          </div>
        </motion.div>

        {/* Language Settings - List Variant */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Language Settings - List</h2>
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <p className="text-gray-600 mb-6">
              A list-based language settings component. Great for mobile interfaces and compact layouts.
            </p>
            <div className="max-w-md mx-auto">
              <LanguageSettings variant="list" />
            </div>
          </div>
        </motion.div>

        {/* Language Settings - Grid Variant */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Language Settings - Grid</h2>
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <p className="text-gray-600 mb-6">
              A grid-based language settings component. Perfect for desktop interfaces with more space.
            </p>
            <div className="max-w-md mx-auto">
              <LanguageSettings variant="grid" />
            </div>
          </div>
        </motion.div>

        {/* Usage Examples */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="bg-white rounded-2xl p-8 shadow-lg"
        >
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Usage Examples</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-700">Login Page</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <code className="text-sm text-gray-600">
                  {`<LanguageSwitcher variant="floating" position="top-right" />`}
                </code>
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-700">Header Navigation</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <code className="text-sm text-gray-600">
                  {`<LanguageSwitcher variant="compact" />`}
                </code>
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-700">Settings Page</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <code className="text-sm text-gray-600">
                  {`<LanguageSettings variant="card" />`}
                </code>
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-700">Language Indicator</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <code className="text-sm text-gray-600">
                  {`<LanguageIndicator size="md" />`}
                </code>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default LanguageDemoPage; 