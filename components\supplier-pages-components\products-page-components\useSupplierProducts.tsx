// stores/useSupplierProducts.ts
import { create } from 'zustand';
import { apiService } from '../../../services/apiService';

type Addition = {
  id: string;
  name: string;
  price: number;
};

type Product = {
  id: string;
  name: string;
  image: string;
  price: number;
  category: string;
  discountPrice?: number;
  isPromotion?: boolean;
  restaurantOptions?: {
    additions?: Addition[];
    without?: string[];
    sides?: Addition[];
  };
  clothingOptions?: {
    sizes: string[];
    colors: string[];
    gallery: string[];
  };
  customOptions?: {
    id: string;
    title: string;
    type: 'text' | 'number' | 'select' | 'multi-select';
    values?: string[]; // for select types
  }[];
};

type SupplierProductsStore = {
  products: Product[];
  supplierId: string | null;
  isLoading: boolean;
  error: string | null;
  setProducts: (products: Product[]) => void;
  setSupplierId: (supplierId: string) => void;
  loadProducts: () => Promise<void>;
  addProduct: (product: Omit<Product, 'id'>) => Promise<void>;
  updateProduct: (id: string, updated: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  toggleProductAvailability: (id: string) => Promise<void>;
  saveProducts: () => Promise<void>;
  lastSaved: Date | null;
  isSaving: boolean;
};

export const useSupplierProducts = create<SupplierProductsStore>((set, get) => ({
  products: [],
  supplierId: null,
  isLoading: false,
  error: null,
  lastSaved: null,
  isSaving: false,

  setProducts: (products) => set({ products }),

  setSupplierId: (supplierId) => set({ supplierId }),

  loadProducts: async () => {
    const { supplierId } = get();
    if (!supplierId) {
      set({ error: 'No supplier ID set' });
      return;
    }

    set({ isLoading: true, error: null });
    try {
      const response = await apiService.getSupplierProducts(supplierId);
      set({
        products: response.products,
        isLoading: false,
        error: null
      });
    } catch (error) {
      console.error('Error loading products:', error);
      set({
        error: 'Failed to load products',
        isLoading: false
      });
    }
  },

  addProduct: async (productData) => {
    const { supplierId } = get();
    if (!supplierId) {
      set({ error: 'No supplier ID set' });
      return;
    }

    set({ isSaving: true, error: null });
    try {
      const newProduct = await apiService.addSupplierProduct(supplierId, productData);
      set((state) => ({
        products: [newProduct, ...state.products],
        isSaving: false,
        lastSaved: new Date(),
        error: null
      }));
    } catch (error) {
      console.error('Error adding product:', error);
      set({
        error: 'Failed to add product',
        isSaving: false
      });
    }
  },

  updateProduct: async (id, updated) => {
    console.log('🏪 updateProduct called with:', { id, updated });

    const { supplierId } = get();
    console.log('🏪 Supplier ID:', supplierId);

    if (!supplierId) {
      console.error('❌ No supplier ID set');
      set({ error: 'No supplier ID set' });
      return;
    }

    set({ isSaving: true, error: null });
    try {
      console.log('🌐 Calling API updateSupplierProduct...');
      const updatedProduct = await apiService.updateSupplierProduct(supplierId, id, updated);
      console.log('✅ API response:', updatedProduct);

      set((state) => ({
        products: state.products.map((p) => (p.id === id ? updatedProduct : p)),
        isSaving: false,
        lastSaved: new Date(),
        error: null
      }));
      console.log('✅ Store updated successfully');
    } catch (error) {
      console.error('❌ Error updating product:', error);
      set({
        error: 'Failed to update product',
        isSaving: false
      });
    }
  },

  deleteProduct: async (id) => {
    const { supplierId } = get();
    console.log('🗑️ Delete product called with:', { id, supplierId });

    if (!supplierId) {
      console.error('❌ No supplier ID set for delete');
      set({ error: 'No supplier ID set' });
      return;
    }

    set({ isSaving: true, error: null });
    try {
      console.log('🗑️ Calling API to delete product:', `${supplierId}/${id}`);
      await apiService.deleteSupplierProduct(supplierId, id);
      console.log('✅ Product deleted successfully from backend');

      set((state) => ({
        products: state.products.filter((p) => p.id !== id),
        isSaving: false,
        lastSaved: new Date(),
        error: null
      }));
      console.log('✅ Product removed from local state');
    } catch (error) {
      console.error('❌ Error deleting product:', error);
      set({
        error: 'Failed to delete product',
        isSaving: false
      });
      throw error; // Re-throw to let the UI handle it
    }
  },

  toggleProductAvailability: async (id) => {
    const { supplierId } = get();
    if (!supplierId) {
      set({ error: 'No supplier ID set' });
      return;
    }

    set({ isSaving: true, error: null });
    try {
      const updatedProduct = await apiService.toggleSupplierProductAvailability(supplierId, id);
      set((state) => ({
        products: state.products.map((p) => (p.id === id ? updatedProduct : p)),
        isSaving: false,
        lastSaved: new Date(),
        error: null
      }));
    } catch (error) {
      console.error('Error toggling product availability:', error);
      set({
        error: 'Failed to toggle product availability',
        isSaving: false
      });
    }
  },

  saveProducts: async () => {
    // This method is now handled by individual operations
    // Keep for backward compatibility
    set({
      isSaving: false,
      lastSaved: new Date()
    });
  },
}));
