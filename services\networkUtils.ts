import { Platform } from 'react-native';

declare global {
}


export const getApiBaseUrl = (): string => {
  const PORT = 3000;

  if (!__DEV__) {
    return 'https://your-production-api.com/api';
  }

  // For web platform, use localhost
  if (Platform.OS === 'web') {
    return `http://localhost:${PORT}/api`;
  }

  // For mobile platforms, try to get the development server URL
  const expoDevServerUrl = (__DEV__ && global.location?.hostname);

  if (expoDevServerUrl && expoDevServerUrl !== 'localhost') {
    return `http://${expoDevServerUrl}:${PORT}/api`;
  }

  // Fallback to common development IPs for mobile
  const commonIPs = [
    '***********',  // Previously working IP
    '***********',
    '***********',  // Alternative IP
    '***********',
    '***********',  // Alternative router configuration
    '********',     // Some corporate networks
  ];

  return `http://${commonIPs[0]}:${PORT}/api`;
};


export const testApiConnection = async (ip: string, port: number = 3000): Promise<boolean> => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000);

    try {
      const response = await fetch(`http://${ip}:${port}/health`, {
        method: 'GET',
        signal: controller.signal,
      });
      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      clearTimeout(timeoutId);
      return false;
    }
  } catch (error) {
    return false;
  }
};


export const findApiServer = async (): Promise<string | null> => {
  const commonIPs = [
    '***********', '***********', '***********', '***********', '***********',
    '***********', '***********', '***********', '***********', '***********',
    '********', '********', '********', '********', '********',
    '**********', '**********', '**********',
  ];

  console.log('🔍 Searching for API server...');

  for (const ip of commonIPs) {
    console.log(`Testing ${ip}:3000...`);
    const isReachable = await testApiConnection(ip);
    
    if (isReachable) {
      console.log(`✅ Found API server at ${ip}:3000`);
      return ip;
    }
  }

  console.log('❌ No API server found on common IP addresses');
  return null;
};


export const getIPInstructions = (): string => {
  const instructions = Platform.select({
    ios: `
📱 To find your computer's IP address:

1. On your computer, open Terminal (Mac) or Command Prompt (Windows)
2. Type: ipconfig (Windows) or ifconfig (Mac)
3. Look for your WiFi adapter's IPv4 address
4. It usually starts with 192.168.x.x or 10.x.x.x
5. Update DEVELOPMENT_IP in services/api.ts with this address
    `,
    android: `
🤖 To find your computer's IP address:

1. On your computer, open Command Prompt (Windows) or Terminal (Mac/Linux)
2. Type: ipconfig (Windows) or ifconfig (Mac/Linux)
3. Look for your WiFi adapter's IPv4 address
4. It usually starts with 192.168.x.x or 10.x.x.x
5. Update DEVELOPMENT_IP in services/api.ts with this address
    `,
    default: `
💻 To find your computer's IP address:

1. Open Command Prompt or Terminal
2. Type: ipconfig (Windows) or ifconfig (Mac/Linux)
3. Look for your network adapter's IPv4 address
4. Update the API configuration with this address
    `
  });

  return instructions;
};
