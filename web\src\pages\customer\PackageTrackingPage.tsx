import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Clock, MapPin, Phone, User, Package, CheckCircle, Truck, Navigation, Zap, Star, Activity, Route, Timer, Shield, AlertCircle, RefreshCw } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup } from 'react-leaflet';
import { motion, AnimatePresence } from 'framer-motion';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import { apiService } from '../../services/api';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Types for package tracking data
type TrackingUpdate = {
  status: string;
  timestamp: string;
  message: string;
  location?: {
    lat: number;
    lng: number;
  };
};

type PackageTrackingData = {
  id: string;
  packageId: string;
  type: 'sent' | 'pickup';
  status: string;
  fromAddress: string;
  fromLocation: { lat: number; lng: number };
  toAddress: string;
  toLocation: { lat: number; lng: number };
  senderName: string;
  senderPhone: string;
  receiverName: string;
  receiverPhone: string;
  driverName?: string;
  driverPhone?: string;
  driverLocation?: { lat: number; lng: number };
  estimatedDelivery: string;
  packageType: string;
  packageSize: string;
  packageWeight: string;
  price: number;
  createdAt: string;
  trackingUpdates: TrackingUpdate[];
  packageDetails?: {
    description?: string;
    type?: string;
    size?: string;
    weight?: string;
  };
  notes?: string;
};

const PackageTrackingPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const packageId = searchParams.get('packageId');
  const pickupId = searchParams.get('pickupId');
  const trackingNumber = searchParams.get('trackingNumber');

  // Backend data state
  const [packageData, setPackageData] = useState<PackageTrackingData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Scroll animation states
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Helper functions for data transformation
  const mapBackendStatus = (backendStatus: string): string => {
    const statusMap: Record<string, string> = {
      'pending': 'Pending',
      'confirmed': 'Confirmed',
      'picked_up': 'Picked Up',
      'in_transit': 'In Transit',
      'out_for_delivery': 'Out for Delivery',
      'delivered': 'Delivered',
      'cancelled': 'Cancelled'
    };
    return statusMap[backendStatus] || backendStatus;
  };

  const getEstimatedTime = (backendData: any): string => {
    if (backendData.estimatedDeliveryTime) {
      const estimatedTime = new Date(backendData.estimatedDeliveryTime);
      const now = new Date();
      const diffHours = Math.ceil((estimatedTime.getTime() - now.getTime()) / (1000 * 60 * 60));
      return `${diffHours}-${diffHours + 1} hours`;
    }
    return '2-3 hours';
  };

  const getPackageSize = (backendData: any): string => {
    const size = backendData.packageDetails?.size;
    if (size) {
      return size.charAt(0).toUpperCase() + size.slice(1);
    }
    return 'Medium';
  };

  const getPackageWeight = (backendData: any): string => {
    const weight = backendData.packageDetails?.weight;
    if (weight) {
      return `${weight}g`;
    }
    return '500g';
  };

  const getDriverLocation = (backendData: any): { lat: number; lng: number } | undefined => {
    // Get the latest tracking update with location
    const latestUpdate = backendData.trackingUpdates?.slice(-1)[0];
    if (latestUpdate?.location) {
      return latestUpdate.location;
    }
    
    // Fallback: calculate midpoint between pickup and delivery
    if (backendData.pickupAddress?.coordinates && backendData.deliveryAddress?.coordinates) {
      const pickup = backendData.pickupAddress.coordinates;
      const delivery = backendData.deliveryAddress.coordinates;
      return {
        lat: (pickup.lat + delivery.lat) / 2,
        lng: (pickup.lng + delivery.lng) / 2
      };
    }
    
    return undefined;
  };

  // Fetch package data from backend
  const fetchPackageData = useCallback(async () => {
    const trackingId = packageId || pickupId || trackingNumber;
    if (!trackingId) {
      setError('No tracking number provided');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await apiService.getPackageByTrackingNumber(trackingId);

      if (response.success && response.data) {
        const backendData = response.data;

        // Transform backend data to our tracking format
        const transformedData: PackageTrackingData = {
          id: backendData.packageId || backendData._id || trackingId,
          packageId: backendData.packageId || backendData._id || trackingId,
          type: backendData.serviceType === 'request_pickup' ? 'pickup' : 'sent',
          status: mapBackendStatus(backendData.status),
          fromAddress: `${backendData.pickupAddress?.street}, ${backendData.pickupAddress?.city}`,
          fromLocation: backendData.pickupAddress?.coordinates || { lat: 32.2211, lng: 35.2544 },
          toAddress: `${backendData.deliveryAddress?.street}, ${backendData.deliveryAddress?.city}`,
          toLocation: backendData.deliveryAddress?.coordinates || { lat: 31.9038, lng: 35.2034 },
          senderName: backendData.senderName || 'Sender',
          senderPhone: backendData.senderPhone || '',
          receiverName: backendData.recipientName || 'Receiver',
          receiverPhone: backendData.recipientPhone || '',
          driverName: backendData.driverName || undefined, // Don't set fallback if no driver assigned
          driverPhone: backendData.driverPhone || undefined, // Don't set fallback if no driver assigned
          driverLocation: getDriverLocation(backendData),
          estimatedDelivery: getEstimatedTime(backendData),
          packageType: backendData.packageDetails?.type || 'General',
          packageSize: getPackageSize(backendData),
          packageWeight: getPackageWeight(backendData),
          price: backendData.cost || 0,
          createdAt: backendData.createdAt || new Date().toISOString(),
          trackingUpdates: backendData.trackingUpdates || [],
          packageDetails: backendData.packageDetails,
          notes: backendData.notes
        };

        setPackageData(transformedData);
        setLastUpdate(new Date());
      } else {
        setError(response.message || 'Failed to fetch package data');
      }
    } catch (error) {
      console.error('Error fetching package data:', error);
      setError('Failed to load package tracking information');
    } finally {
      setIsLoading(false);
    }
  }, [packageId, pickupId, trackingNumber]);

  // Fetch data on component mount
  useEffect(() => {
    fetchPackageData();
  }, [fetchPackageData]);

  // Auto-refresh for active packages
  useEffect(() => {
    if (!packageData || packageData.status === 'Delivered' || packageData.status === 'Cancelled') {
      return;
    }

    const interval = setInterval(() => {
      fetchPackageData();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [packageData, fetchPackageData]);

  // Mock package data
  // const packageData = {
  //   id: packageId || pickupId || 'PK-123456',
  //   type: packageId ? 'sent' : 'pickup',
  //   fromAddress: 'Nablus, Palestine',
  //   fromLocation: { lat: 32.2211, lng: 35.2544 },
  //   toAddress: 'Ramallah, Palestine',
  //   toLocation: { lat: 31.9038, lng: 35.2034 },
  //   senderName: 'Ahmad Samer',
  //   senderPhone: '+970568406041',
  //   receiverName: 'Sara Ahmad',
  //   receiverPhone: '+970599123456',
  //   driverName: 'Omar Khalil',
  //   driverPhone: '0599887766',
  //   driverLocation: { lat: 32.1000, lng: 35.1500 }, // Between pickup and delivery
  //   status: 'In Transit',
  //   estimatedDelivery: '2-3 hours',
  //   packageType: 'Documents',
  //   packageSize: 'Small',
  //   packageWeight: '500g',
  //   price: 15,
  //   createdAt: new Date().toISOString()
  // };

  // Package status timeline
  const generateStatusTimeline = () => {
    if (!packageData) return [];

    const timeline = [];
    const isPickup = packageData.type === 'pickup';

    // Base timeline based on service type
    if (isPickup) {
      timeline.push({
        status: 'Pickup Requested',
        time: new Date(packageData.createdAt).toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit',
          hour12: true 
        }),
        completed: true,
        description: 'Pickup request has been placed'
      });
    } else {
      timeline.push({
        status: 'Package Sent',
        time: new Date(packageData.createdAt).toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit',
          hour12: true 
        }),
        completed: true,
        description: 'Package has been sent for delivery'
      });
    }

    // Add tracking updates from backend
    if (packageData.trackingUpdates && packageData.trackingUpdates.length > 0) {
      packageData.trackingUpdates.forEach((update, index) => {
        const updateTime = new Date(update.timestamp).toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit',
          hour12: true 
        });

        timeline.push({
          status: mapBackendStatus(update.status),
          time: updateTime,
          completed: true,
          description: update.message || `Package status updated to ${mapBackendStatus(update.status)}`
        });
      });
    } else {
      // Fallback timeline based on current status
      const statusOrder = ['pending', 'confirmed', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered'];
      const currentStatusIndex = statusOrder.findIndex(status => 
        packageData.status.toLowerCase().includes(status)
      );

      if (currentStatusIndex >= 0) {
        // Add completed steps
        for (let i = 0; i <= currentStatusIndex; i++) {
          const status = statusOrder[i];
          const statusDisplay = mapBackendStatus(status);
          
          timeline.push({
            status: statusDisplay,
            time: i === 0 ? new Date(packageData.createdAt).toLocaleTimeString('en-US', { 
              hour: '2-digit', 
              minute: '2-digit',
              hour12: true 
            }) : 'ETA',
            completed: true,
            description: `Package ${statusDisplay.toLowerCase()}`
          });
        }

        // Add upcoming steps
        for (let i = currentStatusIndex + 1; i < statusOrder.length; i++) {
          const status = statusOrder[i];
          const statusDisplay = mapBackendStatus(status);
          
          timeline.push({
            status: statusDisplay,
            time: 'ETA',
            completed: false,
            description: `Package will be ${statusDisplay.toLowerCase()}`
          });
        }
      }
    }

    return timeline;
  };

  const statusTimeline = generateStatusTimeline();

  // Handle scroll for header animation with ultra-smooth debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    let rafId: number;

    const handleScroll = () => {
      rafId = requestAnimationFrame(() => {
        const currentScrollY = window.scrollY;
        setScrollY(currentScrollY);

        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          setIsHeaderCompact(currentScrollY > 120);
        }, 50);
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
      cancelAnimationFrame(rafId);
    };
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleCallDriver = () => {
    window.open(`tel:${packageData?.driverPhone}`);
  };

  const handleCallSender = () => {
    window.open(`tel:${packageData?.senderPhone}`);
  };

  const handleCallReceiver = () => {
    window.open(`tel:${packageData?.receiverPhone}`);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-primary-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full mx-auto mb-4"
          />
          <motion.h2
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-2xl font-bold text-white mb-2"
          >
            Loading Package Details
          </motion.h2>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-white/80"
          >
            Fetching tracking information...
          </motion.p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-primary-900 to-slate-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6"
          >
            <AlertCircle className="w-10 h-10 text-red-400" />
          </motion.div>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-2xl font-bold text-white mb-4"
          >
            Unable to Load Package
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-white/80 mb-6"
          >
            {error}
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-3"
          >
            <button
              onClick={fetchPackageData}
              className="w-full bg-primary-500 hover:bg-primary-600 text-white font-medium py-3 px-6 rounded-xl transition-colors"
            >
              Try Again
            </button>
            <button
              onClick={() => navigate(-1)}
              className="w-full bg-white/10 hover:bg-white/20 text-white font-medium py-3 px-6 rounded-xl transition-colors"
            >
              Go Back
            </button>
          </motion.div>
        </div>
      </div>
    );
  }

  // No package data state
  if (!packageData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-primary-900 to-slate-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="w-20 h-20 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-6"
          >
            <Package className="w-10 h-10 text-yellow-400" />
          </motion.div>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-2xl font-bold text-white mb-4"
          >
            Package Not Found
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-white/80 mb-6"
          >
            The package you're looking for could not be found. Please check the tracking number and try again.
          </motion.p>
          <motion.button
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            onClick={() => navigate(-1)}
            className="w-full bg-primary-500 hover:bg-primary-600 text-white font-medium py-3 px-6 rounded-xl transition-colors"
          >
            Go Back
          </motion.button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-primary-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-primary-500/30 to-secondary-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-secondary-500/30 to-third-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-orange-500/30 to-pink-600/30 rounded-full blur-3xl"
          />

          {/* Floating particles */}
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, -100, -20],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Main Header */}
        <div className="relative z-10 bg-white/10 backdrop-blur-md border-b border-white/20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center gap-4">
              <motion.button
                onClick={() => navigate(-1)}
                className="p-3 hover:bg-white/20 rounded-xl transition-all duration-300 group"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ArrowLeft className="w-6 h-6 text-white group-hover:text-primary-200 transition-colors" />
              </motion.button>
              <div className="flex-1">
                <motion.h1
                  className="text-3xl font-bold text-white"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  Track Package
                </motion.h1>
                <motion.p
                  className="text-white/80 text-lg"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                >
                  Package #{packageData?.id}
                </motion.p>
                <motion.p
                  className="text-white/60 text-sm"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  Last updated: {lastUpdate.toLocaleTimeString()}
                </motion.p>
              </div>
              <motion.button
                onClick={fetchPackageData}
                className="p-3 hover:bg-white/20 rounded-xl transition-all duration-300 group"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                disabled={isLoading}
              >
                <motion.div
                  animate={isLoading ? { rotate: 360 } : {}}
                  transition={{ duration: 1, repeat: isLoading ? Infinity : 0, ease: "linear" }}
                >
                  <RefreshCw className="w-6 h-6 text-white group-hover:text-primary-200 transition-colors" />
                </motion.div>
              </motion.button>
            </div>
          </div>
        </div>

        {/* Sticky Header with Ultra-Smooth Scroll Animation */}
        <motion.div
          className="fixed left-0 right-0 transition-all duration-500"
          animate={{
            top: isHeaderCompact ? "0px" : "-100px",
            zIndex: isHeaderCompact ? 50 : 40,
            backgroundColor: isHeaderCompact
              ? "rgba(15, 23, 42, 0.95)"
              : "rgba(15, 23, 42, 0)",
            backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
            borderBottom: isHeaderCompact
              ? "1px solid rgba(255, 255, 255, 0.1)"
              : "1px solid rgba(255, 255, 255, 0)",
          }}
          transition={{
            duration: 0.6,
            ease: [0.25, 0.46, 0.45, 0.94],
            type: "tween"
          }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate(-1)}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </button>
              <div className="flex-1">
                <h1 className="text-xl font-bold text-white">Track Package</h1>
                <p className="text-white/70 text-sm">#{packageData?.id}</p>
                <p className="text-white/50 text-xs">Updated: {lastUpdate.toLocaleTimeString()}</p>
              </div>
              <button
                onClick={fetchPackageData}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                disabled={isLoading}
              >
                <motion.div
                  animate={isLoading ? { rotate: 360 } : {}}
                  transition={{ duration: 1, repeat: isLoading ? Infinity : 0, ease: "linear" }}
                >
                  <RefreshCw className="w-5 h-5 text-white" />
                </motion.div>
              </button>
            </div>
          </div>
        </motion.div>

        {/* Main Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Package Info */}
            <div className="space-y-6">
              {/* Current Status - Enhanced */}
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <div className="flex items-center gap-4 mb-6">
                  <motion.div
                    className="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-lg"
                    animate={{
                      scale: [1, 1.05, 1],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Truck className="w-8 h-8 text-white" />
                  </motion.div>
                  <div>
                    <motion.h2
                      className="text-2xl font-bold text-white"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4 }}
                    >
                      {packageData?.status}
                    </motion.h2>
                    <motion.p
                      className="text-white/80 text-lg"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5 }}
                    >
                      ETA: {packageData?.estimatedDelivery}
                    </motion.p>
                    {packageData?.driverName && (
                      <motion.p
                        className="text-white/60 text-sm"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.6 }}
                      >
                        Driver: {packageData.driverName}
                      </motion.p>
                    )}
                    {!packageData?.driverName && (
                      <motion.p
                        className="text-white/60 text-sm"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.6 }}
                      >
                        Driver: Not assigned yet
                      </motion.p>
                    )}
                  </div>
                </div>

                <motion.div
                  className="bg-gradient-to-r from-primary-500/20 to-secondary-500/20 border border-primary-400/30 rounded-xl p-4 backdrop-blur-sm"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.6 }}
                >
                  <div className="flex items-center gap-3">
                    <motion.div
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.7, 1, 0.7]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <Activity className="w-5 h-5 text-secondary-400" />
                    </motion.div>
                    <p className="text-white font-medium">
                      {packageData?.status === 'Delivered' 
                        ? 'Your package has been delivered successfully!'
                        : packageData?.status === 'Cancelled'
                        ? 'This package delivery has been cancelled.'
                        : packageData?.driverName
                        ? `Your package is on the way! ${packageData.driverName} is delivering it.`
                        : 'Your package is being processed and a driver will be assigned soon.'
                      }
                    </p>
                  </div>
                </motion.div>
              </motion.div>

              {/* Package Details - Enhanced */}
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <div className="flex items-center gap-3 mb-6">
                  <motion.div
                    className="w-10 h-10 bg-gradient-to-br from-third-500 to-primary-500 rounded-xl flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <Package className="w-5 h-5 text-white" />
                  </motion.div>
                  <h3 className="text-xl font-bold text-white">Package Details</h3>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {[
                    { label: 'Type', value: packageData?.packageType || 'Not Specified', icon: Package },
                    { label: 'Sent/Requested', value: packageData?.type || 'Not Specified', icon: Truck },
                    { label: 'Weight', value: packageData?.packageWeight || 'Not Specified', icon: Timer },
                    { label: 'Service Fee', value: `₪${packageData?.price || 0}`, icon: Star }
                  ].map((item, index) => (
                    <motion.div
                      key={item.label}
                      className="bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-all duration-300"
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.4 + index * 0.1 }}
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <item.icon className="w-4 h-4 text-primary-400" />
                        <p className="font-medium text-white/90 text-sm">{item.label}</p>
                      </div>
                      <p className="text-white font-semibold">{item.value}</p>
                    </motion.div>
                  ))}
                </div>

                {/* Package Description and Notes */}
                {(packageData?.packageDetails?.description || packageData?.notes) && (
                  <motion.div
                    className="mt-6 p-4 bg-white/5 rounded-xl border border-white/10"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                  >
                    {packageData?.packageDetails?.description && (
                      <div className="mb-3">
                        <p className="text-white/80 text-sm font-medium mb-1">Description:</p>
                        <p className="text-white text-sm">{packageData.packageDetails.description}</p>
                      </div>
                    )}
                    {packageData?.notes && (
                      <div>
                        <p className="text-white/80 text-sm font-medium mb-1">Notes:</p>
                        <p className="text-white text-sm">{packageData.notes}</p>
                      </div>
                    )}
                  </motion.div>
                )}
              </motion.div>

              {/* Contact Information - Enhanced */}
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <div className="flex items-center gap-3 mb-6">
                  <motion.div
                    className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <Phone className="w-5 h-5 text-white" />
                  </motion.div>
                  <h3 className="text-xl font-bold text-white">Contact Information</h3>
                </div>

                <div className="space-y-4">
                  {/* Driver - Only show if driver is assigned */}
                  {packageData?.driverName && packageData?.driverPhone && (
                    <motion.div
                      className="flex items-center gap-4 bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-all duration-300"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5 }}
                      whileHover={{ scale: 1.02 }}
                    >
                      <motion.div
                        className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-lg"
                        whileHover={{ scale: 1.1 }}
                      >
                        <User className="w-6 h-6 text-white" />
                      </motion.div>
                      <div className="flex-1">
                        <h4 className="font-bold text-white">{packageData.driverName}</h4>
                        <p className="text-white/70 text-sm">Driver</p>
                      </div>
                      <motion.button
                        onClick={handleCallDriver}
                        className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl hover:from-primary-600 hover:to-primary-700 transition-all duration-300 text-sm font-medium shadow-lg"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Phone className="w-4 h-4" />
                        Call
                      </motion.button>
                    </motion.div>
                  )}

                  {/* Driver not assigned yet */}
                  {(!packageData?.driverName || !packageData?.driverPhone) && (
                    <motion.div
                      className="flex items-center gap-4 bg-white/5 rounded-xl p-4 border border-white/10"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5 }}
                    >
                      <motion.div
                        className="w-12 h-12 bg-gradient-to-br from-gray-500 to-gray-600 rounded-xl flex items-center justify-center shadow-lg"
                      >
                        <User className="w-6 h-6 text-white" />
                      </motion.div>
                      <div className="flex-1">
                        <h4 className="font-bold text-white">Driver</h4>
                        <p className="text-white/70 text-sm">Will be assigned soon</p>
                      </div>
                      <div className="px-4 py-2 bg-gray-500/20 text-white/60 rounded-xl text-sm font-medium">
                        Pending
                      </div>
                    </motion.div>
                  )}

                  {/* Sender */}
                  <motion.div
                    className="flex items-center gap-4 bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-all duration-300"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <motion.div
                      className="w-12 h-12 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-xl flex items-center justify-center shadow-lg"
                      whileHover={{ scale: 1.1 }}
                    >
                      <User className="w-6 h-6 text-white" />
                    </motion.div>
                    <div className="flex-1">
                      <h4 className="font-bold text-white">{packageData?.senderName}</h4>
                      <p className="text-white/70 text-sm">Sender</p>
                    </div>
                    {packageData?.senderPhone && (
                      <motion.button
                        onClick={handleCallSender}
                        className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white rounded-xl hover:from-secondary-600 hover:to-secondary-700 transition-all duration-300 text-sm font-medium shadow-lg"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Phone className="w-4 h-4" />
                        Call
                      </motion.button>
                    )}
                  </motion.div>

                  {/* Receiver */}
                  <motion.div
                    className="flex items-center gap-4 bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-all duration-300"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.7 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <motion.div
                      className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg"
                      whileHover={{ scale: 1.1 }}
                    >
                      <User className="w-6 h-6 text-white" />
                    </motion.div>
                    <div className="flex-1">
                      <h4 className="font-bold text-white">{packageData?.receiverName}</h4>
                      <p className="text-white/70 text-sm">Receiver</p>
                    </div>
                    {packageData?.receiverPhone && (
                      <motion.button
                        onClick={handleCallReceiver}
                        className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-orange-600 hover:to-orange-700 transition-all duration-300 text-sm font-medium shadow-lg"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Phone className="w-4 h-4" />
                        Call
                      </motion.button>
                    )}
                  </motion.div>
                </div>
              </motion.div>

              {/* Package Timeline - Enhanced */}
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <div className="flex items-center gap-3 mb-6">
                  <motion.div
                    className="w-10 h-10 bg-gradient-to-br from-third-500 to-secondary-500 rounded-xl flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <Route className="w-5 h-5 text-white" />
                  </motion.div>
                  <h3 className="text-xl font-bold text-white">Package Timeline</h3>
                </div>

                <div className="space-y-6">
                  {statusTimeline.map((step, index) => (
                    <motion.div
                      key={index}
                      className="flex items-start gap-4"
                      initial={{ opacity: 0, x: -30 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6 + index * 0.1 }}
                    >
                      <motion.div
                        className={`w-12 h-12 rounded-2xl flex items-center justify-center shadow-lg relative ${
                          step.completed
                            ? 'bg-gradient-to-br from-secondary-500 to-secondary-600'
                            : 'bg-white/10 border border-white/20'
                        }`}
                        whileHover={{ scale: 1.1 }}
                        animate={step.completed ? {
                          scale: [1, 1.1, 1],
                        } : {}}
                        transition={{
                          duration: 2,
                          repeat: step.completed ? Infinity : 0,
                          ease: "easeInOut"
                        }}
                      >
                        {step.completed ? (
                          <CheckCircle className="w-6 h-6 text-white" />
                        ) : (
                          <Clock className="w-6 h-6 text-white/60" />
                        )}
                        {step.completed && (
                          <motion.div
                            className="absolute inset-0 rounded-2xl bg-secondary-400/30"
                            animate={{
                              scale: [1, 1.5, 1],
                              opacity: [0.5, 0, 0.5]
                            }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                          />
                        )}
                      </motion.div>
                      <div className="flex-1 bg-white/5 rounded-xl p-4 border border-white/10">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className={`font-bold ${
                            step.completed ? 'text-white' : 'text-white/60'
                          }`}>
                            {step.status}
                          </h4>
                          <span className={`text-sm font-medium px-3 py-1 rounded-lg ${
                            step.completed
                              ? 'bg-secondary-500/20 text-secondary-300 border border-secondary-400/30'
                              : 'bg-white/10 text-white/60 border border-white/20'
                          }`}>
                            {step.time}
                          </span>
                        </div>
                        <p className={`text-sm ${
                          step.completed ? 'text-white/80' : 'text-white/50'
                        }`}>
                          {step.description}
                        </p>
                      </div>
                      {/* Connecting line */}
                      {index < statusTimeline.length - 1 && (
                        <div className="absolute left-6 top-16 w-0.5 h-6 bg-gradient-to-b from-white/30 to-white/10" />
                      )}
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>

            {/* Right Column - Map */}
            <div className="space-y-6">
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <div className="p-6 border-b border-white/20">
                  <div className="flex items-center gap-3 mb-2">
                    <motion.div
                      className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center"
                      animate={{
                        scale: [1, 1.1, 1],
                        rotate: [0, 5, -5, 0]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <Navigation className="w-5 h-5 text-white" />
                    </motion.div>
                    <h3 className="text-xl font-bold text-white">Live Tracking</h3>
                  </div>
                  <p className="text-white/80">Follow your package's journey in real-time</p>
                </div>

                <div className="h-96 relative">
                  <MapContainer
                    center={[
                      packageData?.driverLocation?.lat || packageData?.fromLocation?.lat || 31.9038, 
                      packageData?.driverLocation?.lng || packageData?.fromLocation?.lng || 35.2034
                    ]}
                    zoom={10}
                    style={{ height: '100%', width: '100%' }}
                  >
                    <TileLayer
                      attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                      url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                    />

                    {/* Pickup Location Marker */}
                    {packageData?.fromLocation && (
                      <Marker position={[packageData.fromLocation.lat, packageData.fromLocation.lng]}>
                        <Popup>
                          <div className="text-center">
                            <strong>Pickup Location</strong>
                            <br />
                            <span className="text-sm text-gray-600">{packageData?.fromAddress}</span>
                          </div>
                        </Popup>
                      </Marker>
                    )}

                    {/* Driver Marker - Only show if driver location is available */}
                    {packageData?.driverLocation && packageData?.driverName && (
                      <Marker position={[packageData.driverLocation.lat, packageData.driverLocation.lng]}>
                        <Popup>
                          <div className="text-center">
                            <strong>{packageData.driverName}</strong>
                            <br />
                            <span className="text-sm text-gray-600">Your Driver</span>
                          </div>
                        </Popup>
                      </Marker>
                    )}

                    {/* Delivery Location Marker */}
                    {packageData?.toLocation && (
                      <Marker position={[packageData.toLocation.lat, packageData.toLocation.lng]}>
                        <Popup>
                          <div className="text-center">
                            <strong>Delivery Address</strong>
                            <br />
                            <span className="text-sm text-gray-600">{packageData?.toAddress}</span>
                          </div>
                        </Popup>
                      </Marker>
                    )}

                    {/* Fallback marker if no coordinates available */}
                    {!packageData?.fromLocation && !packageData?.toLocation && (
                      <Marker position={[31.9038, 35.2034]}>
                        <Popup>
                          <div className="text-center">
                            <strong>Package Location</strong>
                            <br />
                            <span className="text-sm text-gray-600">Location data unavailable</span>
                          </div>
                        </Popup>
                      </Marker>
                    )}
                  </MapContainer>

                  {/* Live indicator overlay - Only show for active packages */}
                  {packageData && packageData.status !== 'Delivered' && packageData.status !== 'Cancelled' && (
                    <motion.div
                      className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center gap-2 shadow-lg"
                      animate={{
                        scale: [1, 1.05, 1],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <motion.div
                        className="w-2 h-2 bg-white rounded-full"
                        animate={{
                          opacity: [1, 0.3, 1],
                        }}
                        transition={{
                          duration: 1,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                      LIVE
                    </motion.div>
                  )}
                </div>
              </motion.div>

              {/* Addresses - Enhanced */}
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <div className="flex items-center gap-3 mb-6">
                  <motion.div
                    className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <MapPin className="w-5 h-5 text-white" />
                  </motion.div>
                  <h3 className="text-xl font-bold text-white">Addresses</h3>
                </div>

                <div className="space-y-4">
                  <motion.div
                    className="flex items-start gap-4 bg-white/5 rounded-xl p-4 border border-white/10"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-xl flex items-center justify-center mt-1 shadow-lg"
                      animate={{
                        scale: [1, 1.1, 1],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <div className="w-3 h-3 bg-white rounded-full"></div>
                    </motion.div>
                    <div>
                      <p className="font-bold text-white mb-1">From</p>
                      <p className="text-white/80">{packageData?.fromAddress}</p>
                    </div>
                  </motion.div>

                  <motion.div
                    className="flex items-start gap-4 bg-white/5 rounded-xl p-4 border border-white/10"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mt-1 shadow-lg"
                      animate={{
                        scale: [1, 1.1, 1],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: 0.5
                      }}
                    >
                      <div className="w-3 h-3 bg-white rounded-full"></div>
                    </motion.div>
                    <div>
                      <p className="font-bold text-white mb-1">To</p>
                      <p className="text-white/80">{packageData?.toAddress}</p>
                    </div>
                  </motion.div>
                </div>
              </motion.div>

              {/* Map Legend - Enhanced */}
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <div className="flex items-center gap-3 mb-4">
                  <motion.div
                    className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <Zap className="w-4 h-4 text-white" />
                  </motion.div>
                  <h4 className="font-bold text-white">Map Legend</h4>
                </div>
                <div className="space-y-3">
                  {[
                    { color: 'bg-secondary-500', label: 'Pickup Location' },
                    { color: 'bg-primary-500', label: 'Driver Location (Live)' },
                    { color: 'bg-red-500', label: 'Delivery Address' }
                  ].map((item, index) => (
                    <motion.div
                      key={item.label}
                      className="flex items-center gap-3 text-sm"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6 + index * 0.1 }}
                    >
                      <motion.div
                        className={`w-4 h-4 ${item.color} rounded-full shadow-lg`}
                        animate={{
                          scale: [1, 1.2, 1],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut",
                          delay: index * 0.3
                        }}
                      />
                      <span className="text-white/90 font-medium">{item.label}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PackageTrackingPage;