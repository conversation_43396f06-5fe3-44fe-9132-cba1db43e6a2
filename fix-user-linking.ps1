#!/usr/bin/env pwsh

Write-Host "🔗 Fixing user-supplier linking for 3a-kefak..." -ForegroundColor Yellow

# Configuration
$API_BASE = "http://***********:3000/api"
$USER_EMAIL = "<EMAIL>"
$USER_PASSWORD = "SfE#76132"
$SUPPLIER_ID = "3a-kefak"

# Step 1: Login
Write-Host "`n📧 Step 1: Logging in as $USER_EMAIL..." -ForegroundColor Cyan

$loginData = @{
    email = $USER_EMAIL
    password = $USER_PASSWORD
} | ConvertTo-<PERSON>son

try {
    $loginResponse = Invoke-WebRequest -Uri "$API_BASE/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    $loginResult = $loginResponse.Content | ConvertFrom-Json
    
    if ($loginResult.success) {
        $token = $loginResult.data.accessToken
        Write-Host "✅ Login successful!" -ForegroundColor Green
        Write-Host "   Email: $($loginResult.data.user.email)" -ForegroundColor White
        Write-Host "   Current supplierId: $($loginResult.data.user.supplierId)" -ForegroundColor White
        Write-Host "   Current storeName: $($loginResult.data.user.storeName)" -ForegroundColor White
        
        # Step 2: Link to existing supplier
        Write-Host "`n🔗 Step 2: Linking to $SUPPLIER_ID supplier..." -ForegroundColor Cyan
        
        $linkData = @{ existingSupplierId = $SUPPLIER_ID } | ConvertTo-Json
        $headers = @{ 
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json" 
        }
        
        $linkResponse = Invoke-WebRequest -Uri "$API_BASE/suppliers/link-to-existing" -Method POST -Body $linkData -Headers $headers
        $linkResult = $linkResponse.Content | ConvertFrom-Json
        
        if ($linkResult.success) {
            Write-Host "✅ Successfully linked to $SUPPLIER_ID!" -ForegroundColor Green
            Write-Host "   New supplierId: $($linkResult.data.user.supplierId)" -ForegroundColor Cyan
            Write-Host "   Store name: $($linkResult.data.user.storeName)" -ForegroundColor Cyan
            
            # Step 3: Test supplier profile access
            Write-Host "`n🧪 Step 3: Testing supplier profile access..." -ForegroundColor Cyan
            
            $profileResponse = Invoke-WebRequest -Uri "$API_BASE/suppliers/profile" -Method GET -Headers $headers
            $profileResult = $profileResponse.Content | ConvertFrom-Json
            
            Write-Host "✅ Profile access successful!" -ForegroundColor Green
            Write-Host "   Supplier name: $($profileResult.data.supplier.name)" -ForegroundColor Cyan
            Write-Host "   isActive status: $($profileResult.data.supplier.isActive)" -ForegroundColor Cyan
            Write-Host "   Opening hours: $($profileResult.data.supplier.openHours)" -ForegroundColor Cyan
            
            Write-Host "`n🎉 SUCCESS! User account is now properly linked!" -ForegroundColor Green
            Write-Host "`n📱 Next steps:" -ForegroundColor Yellow
            Write-Host "   1. Restart the mobile app" -ForegroundColor White
            Write-Host "   2. Login with: $USER_EMAIL" -ForegroundColor White
            Write-Host "   3. Navigate to supplier profile" -ForegroundColor White
            Write-Host "   4. The profile should load without errors" -ForegroundColor White
            
        } else {
            Write-Host "❌ Link failed: $($linkResult.message)" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "❌ Login failed: $($loginResult.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Error occurred: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "   Status Code: $statusCode" -ForegroundColor Red
        
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorBody = $reader.ReadToEnd()
            Write-Host "   Error details: $errorBody" -ForegroundColor Red
        } catch {
            Write-Host "   Could not read error details" -ForegroundColor Red
        }
    }
    
    exit 1
}

Write-Host "`n✅ Script completed successfully!" -ForegroundColor Green
