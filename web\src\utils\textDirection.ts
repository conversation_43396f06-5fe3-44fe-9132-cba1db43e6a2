/**
 * Detects if text contains Arabic characters
 * @param text - The text to analyze
 * @returns true if text contains Arabic characters, false otherwise
 */
export const containsArabic = (text: string): boolean => {
  // Arabic Unicode range: U+0600 to U+06FF (Arabic), U+0750 to U+077F (Arabic Supplement)
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F]/;
  return arabicRegex.test(text);
};

/**
 * Detects if text contains Hebrew characters
 * @param text - The text to analyze
 * @returns true if text contains Hebrew characters, false otherwise
 */
export const containsHebrew = (text: string): boolean => {
  // Hebrew Unicode range: U+0590 to U+05FF
  const hebrewRegex = /[\u0590-\u05FF]/;
  return hebrewRegex.test(text);
};

/**
 * Detects if text contains RTL characters (Arabic, Hebrew, etc.)
 * @param text - The text to analyze
 * @returns true if text contains RTL characters, false otherwise
 */
export const containsRTL = (text: string): boolean => {
  return containsArabic(text) || containsHebrew(text);
};

/**
 * Gets the appropriate text direction for given content
 * @param text - The text to analyze
 * @returns 'rtl' for RTL text, 'ltr' for LTR text
 */
export const getTextDirection = (text: string): 'rtl' | 'ltr' => {
  if (!text || text.trim() === '') return 'ltr';
  return containsRTL(text) ? 'rtl' : 'ltr';
};

/**
 * Sets the direction of an input element based on its content
 * @param element - The input element to update
 */
export const setInputDirection = (element: HTMLInputElement | HTMLTextAreaElement): void => {
  const direction = getTextDirection(element.value);
  element.style.direction = direction;
  element.style.textAlign = direction === 'rtl' ? 'right' : 'left';
};

/**
 * Creates a function to handle input direction changes
 * @param element - The input element
 * @returns A function that updates the direction based on current content
 */
export const createDirectionHandler = (element: HTMLInputElement | HTMLTextAreaElement) => {
  return () => {
    setInputDirection(element);
  };
}; 