import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircle, Clock, MapPin, Phone, Package, User, Home, ArrowLeft, Search,
  Truck, Shield, Star, Zap, Gift, Sparkles, Crown, Bot, Navigation,
  Calendar, DollarSign, FileText, Award, Target, TrendingUp, AlertCircle
} from 'lucide-react';
import { apiService } from '../../services/api';

// Import the web-specific Zustand store
import { useRequestPickupStore } from '../../stores/useRequestPickupStore';

// Types for package data
interface PackageDetails {
  type: string;
  description: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  value?: number;
  fragile: boolean;
  size?: 'small' | 'medium' | 'large';
}

interface Address {
  street: string;
  city: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  notes?: string;
}

interface PickupRequest {
  _id: string;
  packageId: string;
  userId: string;
  senderName: string;
  senderPhone: string;
  recipientName: string;
  recipientPhone: string;
  pickupAddress: Address;
  deliveryAddress: Address;
  packageDetails: PackageDetails;
  serviceType: 'send_package' | 'request_pickup';
  status: 'pending' | 'confirmed' | 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'cancelled';
  priority: 'standard' | 'express' | 'urgent';
  cost: number;
  paymentMethod: 'cash' | 'card' | 'wallet';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  scheduledPickupTime?: string;
  actualPickupTime?: string;
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  driverId?: string;
  driverName?: string;
  driverPhone?: string;
  trackingUpdates: Array<{
    status: string;
    timestamp: string;
    message: string;
    location?: {
      lat: number;
      lng: number;
    };
  }>;
  photos?: {
    pickup?: string[];
    delivery?: string[];
  };
  signature?: {
    recipientSignature?: string;
    recipientName?: string;
    timestamp?: string;
  };
  notes?: string;
  rating?: {
    score: number;
    comment?: string;
    ratedAt: string;
  };
  createdAt: string;
  updatedAt: string;
}

const RequestPickupConfirmationPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();

  // Zustand store
  const { 
    packages, 
    getPackageByTrackingNumber, 
    getPackageById, 
    addPackage,
    setCurrentPackage,
    setLoading: setStoreLoading,
    setError: setStoreError
  } = useRequestPickupStore();

  // State management
  const [pickupRequest, setPickupRequest] = useState<PickupRequest | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dataSource, setDataSource] = useState<'store' | 'api' | 'location'>('store');
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [showCelebration, setShowCelebration] = useState(true);

  // Get pickup request data from URL params or location state
  const trackingNumber = searchParams.get('trackingNumber') || location.state?.trackingNumber;
  const requestId = searchParams.get('requestId') || location.state?.requestId;

  // Handle scroll for header animation with debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsHeaderCompact(currentScrollY > 200);
      }, 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Hide celebration after 3 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowCelebration(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  // Fetch pickup request data with Zustand store as primary source
  useEffect(() => {
    const fetchPickupRequest = async () => {
      try {
        setLoading(true);
        setStoreLoading(true);
        setError(null);
        setStoreError(null);

        let requestData: PickupRequest | null = null;
        let source: 'store' | 'api' | 'location' = 'store';

        // 1. First, try to get data from location state (if coming from form submission)
        if (location.state?.pickupRequest) {
          requestData = location.state.pickupRequest;
          source = 'location';
        }
        // 2. Try to get from Zustand store by tracking number
        else if (trackingNumber) {
          const storeData = getPackageByTrackingNumber(trackingNumber);
          if (storeData) {
            requestData = storeData;
            source = 'store';
          }
        }
        // 3. Try to get from Zustand store by request ID
        else if (requestId) {
          const storeData = getPackageById(requestId);
          if (storeData) {
            requestData = storeData;
            source = 'store';
          }
        }

        // 4. If not in store, try API call
        if (!requestData) {
          if (trackingNumber) {
            const response = await apiService.getPackageByTrackingNumber(trackingNumber);
            if (response.success && response.data) {
              requestData = response.data;
              source = 'api';
              // Store the data in Zustand for future use
              addPackage(response.data);
            }
          } else if (requestId) {
            const response = await apiService.getPackageByTrackingNumber(requestId);
            if (response.success && response.data) {
              requestData = response.data;
              source = 'api';
              // Store the data in Zustand for future use
              addPackage(response.data);
            }
          }
        }

        if (requestData) {
          setPickupRequest(requestData);
          setCurrentPackage(requestData); // Update store's current package
          setDataSource(source);
        } else {
          setError('No pickup request found. Please try again.');
          setStoreError('No pickup request found. Please try again.');
        }
      } catch (err) {
        console.error('Error fetching pickup request:', err);
        const errorMessage = 'Failed to load pickup request details. Please try again.';
        setError(errorMessage);
        setStoreError(errorMessage);
      } finally {
        setLoading(false);
        setStoreLoading(false);
      }
    };

    fetchPickupRequest();
  }, [trackingNumber, requestId, location.state, getPackageByTrackingNumber, getPackageById, addPackage]);

  const handleTrackPickup = () => {
    if (pickupRequest) {
      navigate(`/customer/package-tracking?trackingNumber=${pickupRequest.packageId}`);
    } else {
      navigate('/customer/packages');
    }
  };

  const handleGoHome = () => {
    navigate('/customer/home');
  };

  const handleViewPackages = () => {
    navigate('/customer/packages');
  };

  const getSizeLabel = (size: string) => {
    const labels: Record<string, string> = {
      'small': 'Small (up to 30cm)',
      'medium': 'Medium (30-60cm)',
      'large': 'Large (60-100cm)',
      'extra-large': 'Extra Large (100cm+)'
    };
    return labels[size] || size;
  };

  const getTimeLabel = (time: string) => {
    if (time === 'asap') return 'As soon as possible';
    if (time === 'today') return 'Later today';
    return time;
  };

  const formatAddress = (address: Address) => {
    return `${address.street}, ${address.city}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 border-4 border-white/20 border-t-white rounded-full mx-auto mb-4"
          />
          <p className="text-white text-lg">Loading pickup request details...</p>
          <p className="text-white/60 text-sm mt-2">Checking local cache...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !pickupRequest) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-slate-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4"
          >
            <AlertCircle className="w-8 h-8 text-white" />
          </motion.div>
          <h2 className="text-2xl font-bold text-white mb-4">Oops!</h2>
          <p className="text-white/70 mb-6">{error || 'No pickup request found'}</p>
          <div className="space-y-3">
            <button
              onClick={() => navigate('/customer/packages')}
              className="w-full py-3 bg-white/10 border border-white/20 text-white font-semibold rounded-xl hover:bg-white/20 transition-colors"
            >
              View All Packages
            </button>
            <button
              onClick={() => navigate('/customer/home')}
              className="w-full py-3 bg-white/10 border border-white/20 text-white font-semibold rounded-xl hover:bg-white/20 transition-colors"
            >
              Back to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-green-900 to-slate-900">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-green-500/30 to-emerald-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute top-1/3 right-0 w-80 h-80 bg-gradient-to-br from-teal-500/30 to-cyan-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
          className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-yellow-500/20 to-orange-600/20 rounded-full blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [0, -100, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 5,
            }}
            className="absolute w-1 h-1 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>

      {/* Celebration Confetti */}
      <AnimatePresence>
        {showCelebration && (
          <div className="fixed inset-0 pointer-events-none z-30">
            {[...Array(50)].map((_, i) => (
              <motion.div
                key={i}
                initial={{
                  y: -100,
                  x: Math.random() * window.innerWidth,
                  rotate: 0,
                  opacity: 1
                }}
                animate={{
                  y: window.innerHeight + 100,
                  rotate: 360 * 3,
                  opacity: 0
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  delay: Math.random() * 2,
                  ease: "easeOut"
                }}
                className={`absolute w-3 h-3 ${
                  ['bg-yellow-400', 'bg-green-400', 'bg-blue-400', 'bg-purple-400', 'bg-pink-400'][Math.floor(Math.random() * 5)]
                } rounded-full`}
              />
            ))}
          </div>
        )}
      </AnimatePresence>

      {/* Sticky Header with Scroll Animation */}
      <motion.div
        className="fixed left-0 right-0 transition-all duration-500"
        animate={{
          top: isHeaderCompact ? "0px" : "64px",
          zIndex: isHeaderCompact ? 55 : 35,
          backgroundColor: isHeaderCompact
            ? "rgba(15, 23, 42, 0.95)"
            : "rgba(15, 23, 42, 0)",
          backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
          borderBottom: isHeaderCompact
            ? "1px solid rgba(255, 255, 255, 0.1)"
            : "1px solid rgba(255, 255, 255, 0)",
        }}
        transition={{ duration: 0.3 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            animate={{
              paddingTop: isHeaderCompact ? "1rem" : "2rem",
              paddingBottom: isHeaderCompact ? "1rem" : "2rem",
            }}
            transition={{ duration: 0.3 }}
            className="flex items-center justify-between"
          >
            <div className="flex items-center gap-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate(-1)}
                className="p-3 bg-white/10 backdrop-blur-xl rounded-xl border border-white/20 hover:bg-white/20 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </motion.button>

              <motion.div
                animate={{
                  fontSize: isHeaderCompact ? "1.5rem" : "2rem",
                }}
                transition={{ duration: 0.3 }}
                className="font-bold text-white"
              >
                Pickup Confirmed
              </motion.div>
            </div>

            {/* Data source indicator */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className={`px-3 py-1 rounded-full text-xs font-medium ${
                dataSource === 'store' 
                  ? 'bg-green-500/20 text-green-400 border border-green-400/30'
                  : dataSource === 'api'
                  ? 'bg-blue-500/20 text-blue-400 border border-blue-400/30'
                  : 'bg-purple-500/20 text-purple-400 border border-purple-400/30'
              }`}
            >
              {dataSource === 'store' && '📦 Local Cache'}
              {dataSource === 'api' && '🌐 API'}
              {dataSource === 'location' && '📍 Direct'}
            </motion.div>

            {/* Compact Search when header is compact */}
            <AnimatePresence>
              {isHeaderCompact && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="flex-1 max-w-md mx-8"
                >
                  <div className="bg-white/10 backdrop-blur-xl rounded-xl border border-white/20">
                    <div className="flex items-center gap-3 p-3">
                      <Search size={18} className="text-white/60" />
                      <input
                        type="text"
                        placeholder="Search..."
                        className="flex-1 bg-transparent text-white placeholder-white/50 outline-none text-sm"
                      />
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </motion.div>

      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="relative z-10 pt-24 pb-16"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* Success Animation */}
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
              duration: 0.8,
              delay: 0.2,
              type: "spring",
              stiffness: 200,
              damping: 10
            }}
            className="mb-8"
          >
            <div className="relative inline-block">
              {/* Pulsing rings */}
              <motion.div
                animate={{ scale: [1, 1.5, 1], opacity: [0.5, 0, 0.5] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="absolute inset-0 bg-green-400 rounded-full"
              />
              <motion.div
                animate={{ scale: [1, 1.3, 1], opacity: [0.7, 0, 0.7] }}
                transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
                className="absolute inset-0 bg-emerald-400 rounded-full"
              />

              {/* Main success icon */}
              <div className="relative bg-white rounded-full p-8 shadow-2xl border-4 border-green-400">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 2, delay: 1 }}
                >
                  <CheckCircle className="w-20 h-20 text-green-500" />
                </motion.div>
              </div>

              {/* Sparkles around the icon */}
              {[...Array(8)].map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{
                    scale: [0, 1, 0],
                    opacity: [0, 1, 0],
                    rotate: [0, 180, 360]
                  }}
                  transition={{
                    duration: 2,
                    delay: 1 + i * 0.1,
                    repeat: Infinity,
                    repeatDelay: 3
                  }}
                  className="absolute w-4 h-4 text-yellow-400"
                  style={{
                    top: `${20 + 60 * Math.cos((i * Math.PI * 2) / 8)}%`,
                    left: `${20 + 60 * Math.sin((i * Math.PI * 2) / 8)}%`,
                  }}
                >
                  <Sparkles className="w-4 h-4" />
                </motion.div>
              ))}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mb-8"
          >
            <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-white via-green-100 to-emerald-100 bg-clip-text text-transparent mb-4">
              Success!
            </h1>
            <h2 className="text-2xl md:text-3xl font-semibold text-white mb-4">
              Pickup Request Confirmed
            </h2>
            <p className="text-white/70 text-xl font-medium max-w-2xl mx-auto">
              Your package pickup has been scheduled. We'll find the best driver and keep you updated every step of the way.
            </p>
          </motion.div>

          {/* Quick Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto mb-12"
          >
            {[
              { icon: Zap, label: 'Fast Response', value: '< 15 mins', color: 'from-yellow-500 to-orange-500' },
              { icon: Shield, label: 'Fully Insured', value: 'Up to ₪500', color: 'from-blue-500 to-cyan-500' },
              { icon: Star, label: 'Top Rated', value: '4.9/5 Stars', color: 'from-purple-500 to-pink-500' }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center mx-auto mb-3`}>
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
                <div className="text-2xl font-bold text-white">{stat.value}</div>
                <div className="text-white/60 text-sm">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen px-4 sm:px-6 lg:px-8 pb-20">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">

            {/* Enhanced Details Card */}
            <div className="xl:col-span-2 space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <div className="flex items-center gap-4 mb-8">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg shadow-green-500/25"
                  >
                    <Package className="w-8 h-8 text-white" />
                  </motion.div>
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-2">Pickup Request Details</h2>
                    <p className="text-white/70">Request ID: <span className="font-mono text-green-400">{pickupRequest.packageId}</span></p>
                    <p className="text-white/60 text-sm">Created: {formatDate(pickupRequest.createdAt)}</p>
                    <p className="text-white/50 text-xs">Data source: {dataSource}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Location Information */}
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                      <MapPin className="w-5 h-5 text-green-400" />
                      Location Details
                    </h3>

                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="bg-white/5 rounded-xl p-4 border border-white/10"
                    >
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                          <MapPin className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <p className="font-semibold text-white mb-1">Pickup Location</p>
                          <p className="text-white/70 text-sm">{formatAddress(pickupRequest.pickupAddress)}</p>
                          {pickupRequest.pickupAddress.notes && (
                            <p className="text-white/60 text-xs mt-1">Notes: {pickupRequest.pickupAddress.notes}</p>
                          )}
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                      className="bg-white/5 rounded-xl p-4 border border-white/10"
                    >
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                          <Target className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <p className="font-semibold text-white mb-1">Delivery Location</p>
                          <p className="text-white/70 text-sm">{formatAddress(pickupRequest.deliveryAddress)}</p>
                          {pickupRequest.deliveryAddress.notes && (
                            <p className="text-white/60 text-xs mt-1">Notes: {pickupRequest.deliveryAddress.notes}</p>
                          )}
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                      className="bg-white/5 rounded-xl p-4 border border-white/10"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                          <Clock className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <p className="font-semibold text-white mb-1">Scheduled Pickup</p>
                          <p className="text-white/70 text-sm">
                            {pickupRequest.scheduledPickupTime 
                              ? formatDate(pickupRequest.scheduledPickupTime)
                              : 'As soon as possible'
                            }
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  </div>

                  {/* Contact & Package Information */}
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                      <User className="w-5 h-5 text-blue-400" />
                      Contact & Package Info
                    </h3>

                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="bg-white/5 rounded-xl p-4 border border-white/10"
                    >
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="font-semibold text-white mb-1">Sender</p>
                          <p className="text-white/70 text-sm">{pickupRequest.senderName}</p>
                          <p className="text-white/60 text-xs">{pickupRequest.senderPhone}</p>
                        </div>
                        <div>
                          <p className="font-semibold text-white mb-1">Receiver</p>
                          <p className="text-white/70 text-sm">{pickupRequest.recipientName}</p>
                          <p className="text-white/60 text-xs">{pickupRequest.recipientPhone}</p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                      className="bg-white/5 rounded-xl p-4 border border-white/10"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
                          <Package className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <p className="font-semibold text-white mb-1">Package Details</p>
                          <p className="text-white/70 text-sm">{pickupRequest.packageDetails.description}</p>
                          <p className="text-white/60 text-xs">
                            Type: {pickupRequest.packageDetails.type}
                            {pickupRequest.packageDetails.size && ` • ${getSizeLabel(pickupRequest.packageDetails.size)}`}
                            {pickupRequest.packageDetails.weight && ` • ${pickupRequest.packageDetails.weight}g`}
                            {pickupRequest.packageDetails.fragile && ' • Fragile'}
                          </p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                      className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl p-4 border border-green-400/30"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                            <span className="text-white text-lg">₪</span>
                          </div>
                          <div>
                            <p className="font-semibold text-white">Service Fee</p>
                            <p className="text-white/70 text-sm">Total cost</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold text-white">₪{pickupRequest.cost}</p>
                          <p className="text-green-400 text-sm">{pickupRequest.paymentStatus}</p>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </div>

                {/* Status Banner */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  className="mt-8 p-4 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-xl border border-yellow-400/30"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <motion.div
                        animate={{ rotate: [0, 360] }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                        className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center"
                      >
                        <Clock className="w-4 h-4 text-white" />
                      </motion.div>
                      <div>
                        <p className="font-semibold text-white">Current Status</p>
                        <p className="text-white/70 text-sm">
                          {pickupRequest.status === 'pending' && "We're processing your request"}
                          {pickupRequest.status === 'confirmed' && "Your request has been confirmed"}
                          {pickupRequest.status === 'picked_up' && "Package has been picked up"}
                          {pickupRequest.status === 'in_transit' && "Package is in transit"}
                          {pickupRequest.status === 'out_for_delivery' && "Package is out for delivery"}
                          {pickupRequest.status === 'delivered' && "Package has been delivered"}
                          {pickupRequest.status === 'cancelled' && "Request has been cancelled"}
                        </p>
                      </div>
                    </div>
                    <div className="px-4 py-2 bg-yellow-400 text-yellow-900 rounded-full text-sm font-bold">
                      {pickupRequest.status.replace('_', ' ').toUpperCase()}
                    </div>
                  </div>
                </motion.div>

                {/* Notes Section */}
                {pickupRequest.notes && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                    className="mt-6 p-4 bg-white/5 rounded-xl border border-white/10"
                  >
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                        <FileText className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <p className="font-semibold text-white mb-1">Additional Notes</p>
                        <p className="text-white/70 text-sm">{pickupRequest.notes}</p>
                      </div>
                    </div>
                  </motion.div>
                )}
              </motion.div>
            </div>

            {/* Enhanced Sidebar */}
            <div className="xl:col-span-1">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl sticky top-8"
              >
                <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl"
                  >
                    <Navigation className="w-6 h-6 text-white" />
                  </motion.div>
                  What's Next?
                </h3>

                {/* Next Steps */}
                <div className="space-y-4 mb-8">
                  {[
                    {
                      icon: Bot,
                      title: 'Driver Assignment',
                      description: 'We\'ll find the best driver for your pickup',
                      time: '5-15 mins',
                      status: pickupRequest.status === 'pending' ? 'pending' : 'completed'
                    },
                    {
                      icon: Phone,
                      title: 'Driver Contact',
                      description: 'Driver will call you before pickup',
                      time: '15-30 mins',
                      status: pickupRequest.status === 'confirmed' ? 'pending' : pickupRequest.status === 'pending' ? 'upcoming' : 'completed'
                    },
                    {
                      icon: Truck,
                      title: 'Package Pickup',
                      description: 'Driver arrives at pickup location',
                      time: '30-60 mins',
                      status: pickupRequest.status === 'picked_up' ? 'pending' : pickupRequest.status === 'confirmed' ? 'upcoming' : pickupRequest.status === 'pending' ? 'upcoming' : 'completed'
                    },
                    {
                      icon: TrendingUp,
                      title: 'Live Tracking',
                      description: 'Track your package in real-time',
                      time: 'Ongoing',
                      status: pickupRequest.status === 'delivered' ? 'completed' : 'upcoming'
                    }
                  ].map((step, index) => (
                    <motion.div
                      key={step.title}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
                      className={`p-4 rounded-xl border transition-all duration-300 ${
                        step.status === 'pending'
                          ? 'bg-yellow-500/20 border-yellow-400/30'
                          : step.status === 'completed'
                          ? 'bg-green-500/20 border-green-400/30'
                          : 'bg-white/5 border-white/10'
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                          step.status === 'pending'
                            ? 'bg-gradient-to-r from-yellow-500 to-orange-500'
                            : step.status === 'completed'
                            ? 'bg-gradient-to-r from-green-500 to-emerald-500'
                            : 'bg-white/10'
                        }`}>
                          <step.icon className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <p className="font-semibold text-white text-sm">{step.title}</p>
                            <span className="text-xs text-white/60">{step.time}</span>
                          </div>
                          <p className="text-white/70 text-xs">{step.description}</p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="space-y-4">
                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleTrackPickup}
                    className="w-full py-4 bg-gradient-to-r from-green-500 to-emerald-500 text-white font-bold rounded-xl transition-all duration-300 flex items-center justify-center gap-2 shadow-lg shadow-green-500/25"
                  >
                    <Clock className="w-5 h-5" />
                    Track Pickup
                  </motion.button>

                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleViewPackages}
                    className="w-full py-4 bg-white/10 border border-white/20 text-white font-semibold rounded-xl transition-all duration-300 flex items-center justify-center gap-2 hover:bg-white/20"
                  >
                    <Package className="w-5 h-5" />
                    View All Packages
                  </motion.button>

                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.9 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleGoHome}
                    className="w-full py-4 bg-white/10 border border-white/20 text-white font-semibold rounded-xl transition-all duration-300 flex items-center justify-center gap-2 hover:bg-white/20"
                  >
                    <Home className="w-5 h-5" />
                    Back to Home
                  </motion.button>
                </div>

                {/* Support Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1.0 }}
                  className="mt-8 p-4 bg-white/5 rounded-xl border border-white/10"
                >
                  <div className="text-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                      <Award className="w-6 h-6 text-white" />
                    </div>
                    <p className="text-white font-semibold mb-2">Need Help?</p>
                    <p className="text-white/60 text-sm mb-4">Our support team is here 24/7</p>
                    <button className="w-full py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-lg text-sm">
                      Contact Support
                    </button>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestPickupConfirmationPage;