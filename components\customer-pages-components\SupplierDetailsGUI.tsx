import { Card, YStack, Text, XStack, Paragraph, Input, Image, Button, Separator, View, H1, H2 } from 'tamagui';
import { Ionicons } from '@expo/vector-icons'
import { Pressable, ScrollView, StyleSheet, Dimensions } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useState, useEffect, useMemo } from 'react';
import { Sheet } from 'tamagui';
import { CartPage } from './Cart';
import { useCartStore } from './CartStore';
import { useTranslation } from 'react-i18next';
import { TamaguiProvider } from 'tamagui';
import { PortalProvider } from '@tamagui/portal';
import { LinearGradient } from 'expo-linear-gradient';
import { MotiView } from 'moti';
import { getSupplierProducts } from '../../services/apiService';

// Hook for responsive values that updates on screen changes
const useResponsive = () => {
  const [screenData, setScreenData] = useState(Dimensions.get('window'));

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData(window);
    });

    return () => subscription?.remove();
  }, []);

  const isTablet = screenData.width >= 768;
  const isDesktop = screenData.width >= 1024;

  const getResponsiveValue = (mobile: number, tablet: number, desktop: number) => {
    if (isDesktop) return desktop;
    if (isTablet) return tablet;
    return mobile;
  };

  const getResponsivePadding = () => {
    if (isDesktop) return 32;
    if (isTablet) return 24;
    return 16;
  };

  const getResponsiveWidth = () => {
    if (isDesktop) return '32%';
    if (isTablet) return '48%';
    return '100%';
  };

  return {
    isTablet,
    isDesktop,
    getResponsiveValue,
    getResponsivePadding,
    getResponsiveWidth,
    screenWidth: screenData.width,
    screenHeight: screenData.height
  };
};


type Addition = { id: string; name: string; price: number }

type SupplierDetailsGUIProps = {
    currentSupplier: {
        id: string;
        name: string;
        banner: string;
        logoUrl: string;
        rating: number;
        deliveryTime: string;
        openHours: string;
        tags: string[];
        phone: string;
        category: string;
        products: Array<{
            id: string;
            name: string;
            image: string;
            price: number;
            category: string;
            restaurantOptions?: {
                additions?: Addition[]
                without?: string[]
                sides?: Addition[]
                /* options can be changed for each meal - resturant put sections it want */
            }
            clothingOptions?: {
                sizes: string[]
                colors: string[]
                gallery: string[]
            }
        }>;
    } | null;
    supplierPromotions: Array<{
        id: string;
        name: string;
        image: string;
        price: number;
        discountPrice: number;
        category: string;
    }>;
    setShowFilters: (show: boolean) => void;
    supplierCategories: string[];
    selectedCategory: string;
    setSelectedCategory: (category: string) => void;
    searchQuery: string;
    setSearchQuery: (query: string) => void;
    filteredProducts: Array<{
            id: string;
            name: string;
            image: string;
            price: number;
            category: string;
            restaurantOptions?: {
                additions?: Addition[]
                without?: string[]
                sides?: Addition[]
                /* options can be changed for each meal - resturant put sections it want */ 
            }
            clothingOptions?: {
                sizes: string[]
                colors: string[]
                gallery: string[]
            }
    }>;
};

export const SupplierDetailsGUI = ({ currentSupplier, supplierPromotions, setShowFilters, supplierCategories, selectedCategory, setSelectedCategory, searchQuery, setSearchQuery, filteredProducts }: SupplierDetailsGUIProps) => {
    // ALL HOOKS MUST BE CALLED AT THE TOP LEVEL, BEFORE ANY CONDITIONAL LOGIC
    const { t } = useTranslation();
    const { addItem, removeItem, updateQty } = useCartStore();
    const router = useRouter();
    const [open, setOpen] = useState(false);

    // Use responsive hook
    const { isTablet, isDesktop, getResponsiveValue, getResponsivePadding, getResponsiveWidth } = useResponsive();

    // Calculate cart items (safe to do even if currentSupplier is null)
    const items = useCartStore(state =>
        currentSupplier
            ? state.items.filter(i => i.supplierCategory === currentSupplier.category)
            : []
    );
    const totalQty = items.reduce((sum, i) => sum + i.qty, 0);
    const totalPrice = items.reduce((sum, i) => sum + i.qty * i.finalPrice, 0);

    // Pre-calculate cart quantities for all products to avoid calling hooks inside map
    const cartQuantities = useMemo(() => {
        if (!currentSupplier || !filteredProducts) return {};

        const quantities: { [productId: string]: number } = {};
        filteredProducts.forEach(product => {
            const cartItem = items.find(i => i.product.id === product.id && i.supplierCategory === currentSupplier.category);
            quantities[product.id] = cartItem?.qty ?? 0;
        });
        return quantities;
    }, [items, filteredProducts, currentSupplier]);

    // NOW we can do conditional checks AFTER all hooks
    if (!currentSupplier) {
        return (
            <>
                <Stack.Screen options={{ title: 'Supplier Details', headerShown: true }} />
                <ScrollView contentContainerStyle={{
                    paddingBottom: 120,
                    paddingHorizontal: getResponsiveValue(16, 24, 32),
                    width: '100%'
                }}>
                    <YStack flex={1} justifyContent="center" alignItems="center" gap="$4" padding="$4">
                        <Ionicons name="storefront-outline" size={80} color="#ccc" />
                        <Text fontSize="$5" color="$gray10">Supplier data not available</Text>
                        <Text fontSize="$3" color="$gray8" textAlign="center">
                            Please try refreshing or go back and select a supplier again.
                        </Text>
                    </YStack>
                </ScrollView>
            </>
        );
    }

    return (
        <>
            <Stack.Screen options={{ title: currentSupplier?.name || 'Supplier Details', headerShown: true }} />

            <ScrollView contentContainerStyle={{
                paddingBottom: 120,
                width: '100%'
            }}>
                <YStack gap={getResponsiveValue(16, 20, 24)} width="100%" flex={1}>
                
                {/* Enhanced Banner */}
                <MotiView
                    from={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ type: 'timing', duration: 600 }}
                >
                    <Card
                        p="$0"
                        br="$6"
                        bw="$0"
                        elevation="$6"
                        shadowColor="$shadowColor"
                        shadowOffset={{ width: 0, height: 8 }}
                        shadowOpacity={0.15}
                        shadowRadius={16}
                        overflow="hidden"
                        height={getResponsiveValue(220, 280, 320)}
                        width="100%"
                        position="relative"
                    >
                        <Image
                            source={{
                                uri: currentSupplier.banner || 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=800&h=400&fit=crop'
                            }}
                            width="100%"
                            height="100%"
                            style={StyleSheet.absoluteFillObject}
                        />
                        <LinearGradient
                            colors={['transparent', 'rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']}
                            style={StyleSheet.absoluteFillObject}
                        />
                        <View style={{
                            position: 'absolute',
                            bottom: getResponsiveValue(12, 16, 20),
                            left: getResponsiveValue(12, 16, 20),
                            right: getResponsiveValue(12, 16, 20),
                            zIndex: 1
                        }}>
                            <XStack ai="flex-end" jc="space-between" gap="$4">
                                <YStack gap={getResponsiveValue(8, 12, 16)} flex={1}>
                                    <H1
                                        color="white"
                                        fontSize={getResponsiveValue(24, 32, 40)}
                                        fontWeight="800"
                                        lineHeight={getResponsiveValue(1.2, 1.3, 1.4)}
                                        numberOfLines={2}
                                        style={{
                                            textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                                            flexWrap: 'wrap'
                                        }}
                                    >
                                        {currentSupplier.name || 'Unknown Supplier'}
                                    </H1>
                                    <XStack gap={getResponsiveValue(12, 16, 20)} ai="center" flexWrap="wrap">
                                        <XStack ai="center" gap="$1" bg="rgba(255,255,255,0.2)" px="$3" py="$2" br="$4">
                                            <Ionicons name="star" size={getResponsiveValue(16, 18, 20)} color="#FFD700" />
                                            <Text color="white" fontWeight="600" fontSize={getResponsiveValue(14, 16, 18)}>
                                                {String(currentSupplier.rating || 0)}
                                            </Text>
                                        </XStack>
                                        <XStack ai="center" gap="$1" bg="rgba(255,255,255,0.2)" px="$3" py="$2" br="$4">
                                            <Ionicons name="time-outline" size={getResponsiveValue(16, 18, 20)} color="white" />
                                            <Text color="white" fontWeight="600" fontSize={getResponsiveValue(14, 16, 18)}>
                                                {currentSupplier.deliveryTime || 'Delivery time not set'}
                                            </Text>
                                        </XStack>
                                        <XStack ai="center" gap="$1" bg={(currentSupplier as any).isActive !== false ? "rgba(16, 185, 129, 0.9)" : "rgba(239, 68, 68, 0.9)"} px="$3" py="$2" br="$4">
                                            <View style={{
                                                width: getResponsiveValue(8, 10, 12),
                                                height: getResponsiveValue(8, 10, 12),
                                                borderRadius: getResponsiveValue(4, 5, 6),
                                                backgroundColor: 'white'
                                            }} />
                                            <Text color="white" fontWeight="700" fontSize={getResponsiveValue(12, 14, 16)}>
                                                {(currentSupplier as any).isActive !== false ? 'OPEN' : 'CLOSED'}
                                            </Text>
                                        </XStack>
                                    </XStack>
                                </YStack>
                                <View style={{
                                    borderRadius: getResponsiveValue(20, 24, 28),
                                    overflow: 'hidden',
                                    borderWidth: getResponsiveValue(3, 4, 5),
                                    borderColor: 'white',
                                    elevation: 4,
                                    shadowColor: '#000',
                                    shadowOffset: { width: 0, height: 2 },
                                    shadowOpacity: 0.3,
                                    shadowRadius: 4
                                }}>
                                    <Image
                                        source={{
                                            uri: currentSupplier.logoUrl || 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=200&h=200&fit=crop'
                                        }}
                                        width={getResponsiveValue(80, 100, 120)}
                                        height={getResponsiveValue(80, 100, 120)}
                                    />
                                </View>
                            </XStack>
                        </View>
                    </Card>
                </MotiView>

                {/* Content with padding */}
                <YStack gap={getResponsiveValue(16, 20, 24)} paddingHorizontal={getResponsiveValue(12, 16, 20)}>

                {/* Enhanced Info Section */}
                <MotiView
                    from={{ opacity: 0, translateY: 20 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ type: 'timing', duration: 500, delay: 200 }}
                >
                    <Card
                        p="$4"
                        br="$5"
                        bw="$0"
                        bg="$background"
                        elevation="$3"
                        shadowColor="$shadowColor"
                        shadowOffset={{ width: 0, height: 4 }}
                        shadowOpacity={0.08}
                        shadowRadius={12}
                        width="100%"
                    >
                        <YStack gap="$3">
                            <XStack ai="center" gap="$2">
                                <Ionicons name="time-outline" size={20} color="#10B981" />
                                <Text fontSize="$4" fontWeight="600" color="$gray12">
                                    {t('suppliers.openHours', { defaultValue: 'Open' })}:
                                </Text>
                                <Text fontSize="$4" color="$green10" fontWeight="600">
                                    {currentSupplier.openHours || 'Hours not set'}
                                </Text>
                            </XStack>

                            <XStack ai="center" gap="$2" flexWrap="wrap">
                                {(currentSupplier.tags || []).filter(tag => tag && typeof tag === 'string').map((tag, index) => (
                                    <View key={index} style={{
                                        backgroundColor: '#F3F4F6',
                                        borderRadius: 16,
                                        paddingHorizontal: 12,
                                        paddingVertical: 6
                                    }}>
                                        <Text fontSize="$3" color="$gray11" fontWeight="500">
                                            {tag || 'Tag'}
                                        </Text>
                                    </View>
                                ))}
                            </XStack>

                            <XStack ai="center" gap="$2">
                                <Ionicons name="call-outline" size={20} color="#3B82F6" />
                                <Text fontSize="$4" color="$gray10">
                                    Contact:
                                </Text>
                                <Text fontSize="$4" color="$blue10" fontWeight="600">
                                    {currentSupplier.phone || 'Phone not available'}
                                </Text>
                            </XStack>
                        </YStack>
                    </Card>
                </MotiView>

                {/* Closed Supplier Notice */}
                {(currentSupplier as any).isActive === false && (
                    <MotiView
                        from={{ opacity: 0, translateY: -20 }}
                        animate={{ opacity: 1, translateY: 0 }}
                        transition={{ type: 'timing', duration: 400, delay: 250 }}
                    >
                        <Card
                            p="$4"
                            br="$5"
                            bg="$red2"
                            borderWidth={1}
                            borderColor="$red6"
                            elevation="$2"
                        >
                            <XStack ai="center" gap="$3">
                                <View style={{
                                    backgroundColor: '#EF4444',
                                    borderRadius: 20,
                                    padding: 8
                                }}>
                                    <Ionicons name="time-outline" size={20} color="white" />
                                </View>
                                <YStack flex={1} gap="$1">
                                    <Text fontSize="$5" fontWeight="700" color="$red11">
                                        Store Currently Closed
                                    </Text>
                                    <Text fontSize="$3" color="$red10">
                                        You can browse products but cannot place orders at this time.
                                    </Text>
                                </YStack>
                            </XStack>
                        </Card>
                    </MotiView>
                )}

                {/* Enhanced Contact Button */}
                <MotiView
                    from={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ type: 'timing', duration: 400, delay: 300 }}
                >
                    <Button
                        size="$5"
                        br="$6"
                        bg="$green6"
                        color="white"
                        fontWeight="700"
                        fontSize="$5"
                        elevation="$4"
                        shadowColor="$green8"
                        shadowOffset={{ width: 0, height: 4 }}
                        shadowOpacity={0.3}
                        shadowRadius={8}
                        hoverStyle={{ bg: '$green7', scale: 1.02 }}
                        pressStyle={{ bg: '$green8', scale: 0.98 }}
                        icon={<Ionicons name="chatbubble-ellipses" size={20} color="white" />}
                        onPress={() => console.log('Chat with supplier')}
                        width="100%"
                    >
                        {t('suppliers.chatWithSupplier', { defaultValue: 'Chat with Supplier' })}
                    </Button>
                </MotiView>

                {/* Enhanced Menu Section */}
                <MotiView
                    from={{ opacity: 0, translateY: 20 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ type: 'timing', duration: 500, delay: 400 }}
                >
                    <YStack gap="$4" mt="$4" width="100%">
                        <XStack ai="center" gap="$3">
                            <View style={{
                                backgroundColor: '#FEF3C7',
                                borderRadius: 12,
                                padding: 8
                            }}>
                                <Ionicons
                                    name={currentSupplier.category === "restaurants" ? "restaurant" : "storefront"}
                                    size={24}
                                    color="#F59E0B"
                                />
                            </View>
                            <YStack>
                                <H2 fontSize="$7" fontWeight="800" color="$gray12">
                                    {(currentSupplier.category==="restaurants") ? t('suppliers.menu', { defaultValue: 'Menu' }) : t('suppliers.products', { defaultValue: 'Products' })}
                                </H2>
                                <Text fontSize="$3" color="$gray10">
                                    Discover our delicious offerings
                                </Text>
                            </YStack>
                        </XStack>

                        {/* Enhanced Search & Filter */}
                        <XStack gap="$3" ai="center" jc="space-between" width="100%">
                            <View style={{
                                flex: 1,
                                position: 'relative',
                                shadowColor: '#000',
                                shadowOffset: { width: 0, height: 2 },
                                shadowOpacity: 0.05,
                                shadowRadius: 8,
                                elevation: 2
                            }}>
                                <Input
                                    placeholder={t('common.search', { defaultValue: 'Search menu items...' })}
                                    flex={1}
                                    value={searchQuery}
                                    onChangeText={setSearchQuery}
                                    size="$5"
                                    br="$6"
                                    bw="$0"
                                    bg="$background"
                                    pl="$5"
                                    pr="$4"
                                    fontSize="$4"
                                    placeholderTextColor="$gray9"
                                />
                                <View style={{
                                    position: 'absolute',
                                    left: 16,
                                    top: '50%',
                                    transform: [{ translateY: -10 }]
                                }}>
                                    <Ionicons name="search" size={20} color="#9CA3AF" />
                                </View>
                            </View>
                            <Button
                                size="$5"
                                br="$6"
                                bg="$blue6"
                                color="white"
                                px="$3"
                                elevation="$2"
                                hoverStyle={{ bg: '$blue7' }}
                                pressStyle={{ bg: '$blue8' }}
                                icon={<Ionicons name="filter-outline" size={18} color="white" />}
                                onPress={() => setShowFilters(true)}
                            />
                            <Button
                                size="$5"
                                br="$6"
                                bg="$purple6"
                                color="white"
                                px="$3"
                                elevation="$2"
                                hoverStyle={{ bg: '$purple7' }}
                                pressStyle={{ bg: '$purple8' }}
                                icon={<Ionicons name="swap-vertical-outline" size={18} color="white" />}
                                onPress={() => console.log('Sort by price')}
                            />
                        </XStack>
                    </YStack>
                </MotiView>

                {/* Enhanced Promotions */}
                <MotiView
                    from={{ opacity: 0, translateX: -30 }}
                    animate={{ opacity: 1, translateX: 0 }}
                    transition={{ type: 'timing', duration: 500, delay: 500 }}
                >
                    <YStack gap="$3" mt="$4">
                        <XStack ai="center" gap="$3">
                            <View style={{
                                backgroundColor: '#FEE2E2',
                                borderRadius: 12,
                                padding: 8
                            }}>
                                <Ionicons name="pricetag" size={20} color="#EF4444" />
                            </View>
                            <YStack>
                                <Text fontSize="$6" fontWeight="bold" color="$gray12">
                                    {t('categories.promotions', { defaultValue: 'Special Offers' })}
                                </Text>
                                <Text fontSize="$3" color="$gray10">
                                    Limited time deals
                                </Text>
                            </YStack>
                        </XStack>
                        {isTablet ? (
                            // Grid layout for tablets and desktop
                            <View style={{
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                                gap: getResponsiveValue(16, 20, 24),
                                justifyContent: 'space-between'
                            }}>
                                {Array.isArray(supplierPromotions) && supplierPromotions.filter(item => item && item.id).map((item, index) => (
                                    <MotiView
                                        key={item.id}
                                        from={{ opacity: 0, scale: 0.9 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ type: 'timing', duration: 300, delay: index * 100 }}
                                        style={{
                                            width: isDesktop ? '30%' : '48%',
                                            minWidth: 200
                                        }}
                                    >
                                        <Card
                                            width="100%"
                                            p="$0"
                                            br="$6"
                                            bw="$0"
                                            elevation="$4"
                                            shadowColor="$shadowColor"
                                            shadowOffset={{ width: 0, height: 4 }}
                                            shadowOpacity={0.1}
                                            shadowRadius={8}
                                            overflow="hidden"
                                            hoverStyle={{ scale: 1.02 }}
                                            pressStyle={{ scale: 0.98 }}
                                            onPress={() => {
                                                console.log('🔥 Promotion clicked!', item.name);
                                                console.log('🔥 Promotion data:', item);
                                                try {
                                                    // Convert promotion to product format for navigation
                                                    const promotionAsProduct = {
                                                        id: item.id,
                                                        name: item.name,
                                                        image: item.image,
                                                        price: item.price,
                                                        category: currentSupplier.category,
                                                        discountPrice: item.discountPrice
                                                    };

                                                    router.push({
                                                        pathname: "/(customer-pages)/home/<USER>" as any,
                                                        params: {
                                                            productId: item.id,
                                                            supplierId: currentSupplier.id,
                                                            supplierName: currentSupplier.name,
                                                            category: currentSupplier.category,
                                                            product: JSON.stringify(promotionAsProduct)
                                                        }
                                                    });
                                                } catch (error: any) {
                                                    console.error('❌ Promotion navigation error:', error);
                                                }
                                            }}
                                        >
                                            <Image source={{
                                                uri: item.image || 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop'
                                            }} height={120} width="100%" />
                                            <YStack p="$3" gap="$2">
                                                <Text fontWeight="bold" fontSize="$4" color="$gray12">
                                                    {item.name || 'Unknown Item'}
                                                </Text>
                                                <XStack ai="center" gap="$2">
                                                    <View style={{
                                                        backgroundColor: '#FEE2E2',
                                                        borderRadius: 8,
                                                        paddingHorizontal: 8,
                                                        paddingVertical: 4
                                                    }}>
                                                        <Text color="$red10" fontSize="$2" fontWeight="600">
                                                            -{String(item.discountPrice || 0)}%
                                                        </Text>
                                                    </View>
                                                    <Text color="$red10" fontSize="$4" fontWeight="bold">
                                                        ₪{String(item.price || 0)}
                                                    </Text>
                                                </XStack>
                                            </YStack>
                                        </Card>
                                    </MotiView>
                                ))}
                            </View>
                        ) : (
                            // Horizontal scroll for mobile
                            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                                <XStack gap="$4" px="$2">
                                {Array.isArray(supplierPromotions) && supplierPromotions.filter(item => item && item.id).map((item, index) => (
                                    <MotiView
                                        key={item.id}
                                        from={{ opacity: 0, scale: 0.9 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ type: 'timing', duration: 300, delay: index * 100 }}
                                    >
                                        <Card
                                            width={getResponsiveValue(220, 260, 300)}
                                            p="$0"
                                            br="$6"
                                            bw="$0"
                                            elevation="$4"
                                            shadowColor="$shadowColor"
                                            shadowOffset={{ width: 0, height: 4 }}
                                            shadowOpacity={0.1}
                                            shadowRadius={8}
                                            overflow="hidden"
                                            hoverStyle={{ scale: 1.02 }}
                                            pressStyle={{ scale: 0.98 }}
                                            onPress={() => {
                                                console.log('🔥 Promotion clicked (horizontal)!', item.name);
                                                console.log('🔥 Promotion data:', item);
                                                try {
                                                    // Convert promotion to product format for navigation
                                                    const promotionAsProduct = {
                                                        id: item.id,
                                                        name: item.name,
                                                        image: item.image,
                                                        price: item.price,
                                                        category: currentSupplier.category,
                                                        discountPrice: item.discountPrice
                                                    };

                                                    router.push({
                                                        pathname: "/(customer-pages)/home/<USER>" as any,
                                                        params: {
                                                            productId: item.id,
                                                            supplierId: currentSupplier.id,
                                                            supplierName: currentSupplier.name,
                                                            category: currentSupplier.category,
                                                            product: JSON.stringify(promotionAsProduct)
                                                        }
                                                    });
                                                } catch (error: any) {
                                                    console.error('❌ Promotion navigation error:', error);
                                                }
                                            }}
                                        >
                                        <Image source={{
                                            uri: item.image || 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop'
                                        }} height={120} width="100%" />
                                        <YStack p="$3" gap="$2">
                                            <Text fontWeight="bold" fontSize="$4" color="$gray12">
                                                {item.name || 'Unknown Item'}
                                            </Text>
                                            <XStack ai="center" gap="$2">
                                                <View style={{
                                                    backgroundColor: '#FEE2E2',
                                                    borderRadius: 8,
                                                    paddingHorizontal: 8,
                                                    paddingVertical: 4
                                                }}>
                                                    <Text color="$red10" fontSize="$2" fontWeight="600">
                                                        -{String(item.discountPrice || 0)}%
                                                    </Text>
                                                    
                                                </View>
                                                <Text color="$red10" fontSize="$4" fontWeight="bold">
                                                    ₪{String(item.price || 0)}
                                                </Text>
                                            </XStack>
                                        </YStack>
                                    </Card>
                                </MotiView>
                            ))}
                                </XStack>
                            </ScrollView>
                        )}
                    </YStack>
                </MotiView>

                {/* Enhanced Category Tabs */}
                <MotiView
                    from={{ opacity: 0, translateY: 20 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ type: 'timing', duration: 400, delay: 600 }}
                >
                    <YStack gap="$3" mt="$5">
                        <Text fontSize="$5" fontWeight="bold" color="$gray12">
                            Categories
                        </Text>
                        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                            <XStack gap="$3" px="$2">
                                {supplierCategories.filter(cat => cat && typeof cat === 'string').map((cat, index) => (
                                <MotiView
                                    key={cat}
                                    from={{ opacity: 0, scale: 0.9 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ type: 'timing', duration: 300, delay: index * 50 }}
                                >
                                    <Button
                                        size="$4"
                                        br="$6"
                                        px="$4"
                                        py="$2"
                                        bg={selectedCategory === cat ? '$blue6' : '$gray3'}
                                        color={selectedCategory === cat ? 'white' : '$gray11'}
                                        fontWeight="600"
                                        elevation={selectedCategory === cat ? '$3' : '$1'}
                                        shadowColor={selectedCategory === cat ? '$blue8' : '$gray6'}
                                        shadowOffset={{ width: 0, height: 2 }}
                                        shadowOpacity={selectedCategory === cat ? 0.3 : 0.1}
                                        shadowRadius={selectedCategory === cat ? 6 : 3}
                                        hoverStyle={{
                                            scale: 1.05,
                                            bg: selectedCategory === cat ? '$blue7' : '$gray4'
                                        }}
                                        pressStyle={{
                                            scale: 0.95,
                                            bg: selectedCategory === cat ? '$blue8' : '$gray5'
                                        }}
                                        onPress={() => setSelectedCategory(cat)}
                                    >
                                        {cat}
                                    </Button>
                                </MotiView>
                                ))}
                            </XStack>
                        </ScrollView>
                    </YStack>
                </MotiView>

                {/* Enhanced Product List */}
                <MotiView
                    from={{ opacity: 0, translateY: 30 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ type: 'timing', duration: 500, delay: 700 }}
                >
                    <YStack gap={getResponsiveValue(16, 20, 24)} mt="$4">
                        <Text fontSize={getResponsiveValue(20, 24, 28)} fontWeight="bold" color="$gray12">
                            Menu Items
                        </Text>
                        <View style={{
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            gap: getResponsiveValue(12, 16, 20),
                            justifyContent: 'space-between',
                            width: '100%'
                        }}>
                        {filteredProducts.length === 0 ? (
                            <YStack alignItems="center" jc="center" py="$10" width="100%">
                                <Ionicons name="restaurant-outline" size={80} color="#ccc" />
                                <Text fontSize="$5" mt="$2" color="$gray9" textAlign="center">
                                    {t('suppliers.noProductsFound', { defaultValue: 'No menu items available' })}
                                </Text>
                                <Text fontSize="$3" mt="$1" color="$gray7" textAlign="center">
                                    {t('suppliers.checkBackLater', { defaultValue: 'Please check back later or contact the restaurant' })}
                                </Text>
                            </YStack>
                        ) : filteredProducts.filter(product => product && product.id).map((product, index) => (
                        <MotiView
                            key={product.id}
                            from={{ opacity: 0, translateX: -20 }}
                            animate={{ opacity: 1, translateX: 0 }}
                            transition={{ type: 'timing', duration: 400, delay: index * 100 }}
                            style={{
                                width: getResponsiveWidth(),
                                minWidth: getResponsiveValue(280, 300, 320)
                            }}
                        >
                            <Pressable
                                onPress={() => {
                                    console.log('🔥 Product clicked!', product.name);
                                    console.log('🔥 Product data:', product);
                                    console.log('🔥 Supplier data:', currentSupplier);
                                    console.log('🔥 Router object:', router);

                                    try {
                                        console.log('🔥 Attempting navigation...');

                                        const navigationParams = {
                                            productId: product.id,
                                            supplierId: currentSupplier.id,
                                            supplierName: currentSupplier.name,
                                            category: currentSupplier.category,
                                            product: JSON.stringify(product)
                                        };

                                        console.log('🔥 Navigation params:', navigationParams);

                                        router.push({
                                            pathname: "/(customer-pages)/home/<USER>" as any,
                                            params: navigationParams
                                        });
                                        console.log('🔥 Navigation call completed');
                                    } catch (error: any) {
                                        console.error('❌ Navigation error:', error);
                                        console.log('❌ Product data:', product);
                                        console.log('❌ Error details:', error?.message, error?.stack);
                                    }
                                }}
                                style={({ pressed }) => ({
                                    transform: pressed ? [{ scale: 0.98 }] : [{ scale: 1 }]
                                })}
                                disabled={false}
                            >
                            <Card
                                p="$4"
                                br="$6"
                                bw="$0"
                                bg="$background"
                                elevation="$3"
                                shadowColor="$shadowColor"
                                shadowOffset={{ width: 0, height: 4 }}
                                shadowOpacity={0.08}
                                shadowRadius={12}
                            >
                                <XStack gap="$4" ai="center" jc="space-between">
                                    <XStack gap="$4" ai="center" flex={1}>
                                        <View style={{
                                            borderRadius: 16,
                                            overflow: 'hidden',
                                            elevation: 2,
                                            shadowColor: '#000',
                                            shadowOffset: { width: 0, height: 2 },
                                            shadowOpacity: 0.1,
                                            shadowRadius: 4
                                        }}>
                                            <Image
                                                source={{
                                                    uri: product.image || 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=200&h=200&fit=crop'
                                                }}
                                                width={80}
                                                height={80}
                                            />
                                        </View>
                                        <YStack flex={1} gap="$2">
                                            <Text fontSize="$5" fontWeight="bold" color="$gray12">
                                                {product.name || 'Unknown Product'}
                                            </Text>
                                            <Text fontSize="$3" color="$gray10" numberOfLines={2}>
                                                Delicious and freshly prepared with premium ingredients
                                            </Text>
                                            <XStack ai="center" gap="$2">
                                                <Text fontSize="$5" fontWeight="bold" color="$green10">
                                                    ₪{String(product.price || 0)}
                                                </Text>
                                                <View style={{
                                                    backgroundColor: '#FEF3C7',
                                                    borderRadius: 8,
                                                    paddingHorizontal: 8,
                                                    paddingVertical: 2
                                                }}>
                                                    <Text fontSize="$2" color="$yellow11" fontWeight="600">
                                                        Popular
                                                    </Text>
                                                </View>
                                            </XStack>
                                        </YStack>
                                    </XStack>
                        {(() => {
                            // Show add to cart buttons for all categories
                            // Use pre-calculated cart quantity instead of calling hook
                            const inCartQty = cartQuantities[product.id] || 0;
                            const isSupplierClosed = (currentSupplier as any).isActive === false;

                            if (isSupplierClosed) {
                                // Show disabled button when supplier is closed
                                return (
                                    <Button
                                        size="$2"
                                        disabled={true}
                                        opacity={0.5}
                                        backgroundColor="$gray5"
                                        color="$gray9"
                                    >
                                        <Text fontSize="$2" color="$gray9">Closed</Text>
                                    </Button>
                                );
                            } else if (inCartQty === 0) {
                                return (
                                    <Button
                                        size="$2"
                                        icon={<Ionicons name="add" size={16} />}
                                        onPress={() => {
                                            const newItem = {
                                                id: product.id,
                                                name: product.name,
                                                price: product.price,
                                                image: product.image,
                                                category: product.category
                                            };
                                            addItem({
                                                product: newItem,
                                                qty: 1,
                                                finalPrice: newItem.price,
                                                supplierId: currentSupplier.id,
                                                supplierName: currentSupplier.name,
                                                supplierCategory: currentSupplier.category
                                            });
                                        }}
                                    />
                                );
                            } else {
                                return (
                                    <XStack ai="center" gap="$2" width={"25%"}>
                                        <Button
                                            icon={<Ionicons name="remove" size={16} />}
                                            size="$2"
                                            onPress={() => {
                                                const item = items.find(i => i.product.id === product.id && i.supplierCategory === currentSupplier.category);
                                                if (item) {
                                                    if (item.qty > 1) {
                                                        updateQty(item.id, item.qty - 1);
                                                    } else {
                                                        removeItem(item.id);
                                                    }
                                                }
                                            }}
                                        />
                                        <Text>{String(inCartQty || 0)}</Text>
                                        <Button
                                            icon={<Ionicons name="add" size={16} />}
                                            size="$2"
                                            disabled={isSupplierClosed}
                                            opacity={isSupplierClosed ? 0.5 : 1}
                                            backgroundColor={isSupplierClosed ? "$gray5" : undefined}
                                            onPress={isSupplierClosed ? undefined : () => {
                                                const newItem = {
                                                    id: product.id,
                                                    name: product.name,
                                                    price: product.price,
                                                    image: product.image,
                                                    category: product.category
                                                };
                                                addItem({
                                                    product: newItem,
                                                    qty: 1,
                                                    finalPrice: newItem.price,
                                                    supplierId: currentSupplier.id,
                                                    supplierName: currentSupplier.name,
                                                    supplierCategory: currentSupplier.category
                                                });
                                            }}
                                        />
                                    </XStack>
                                );
                            }
                        })()}
                        </XStack>
                    </Card>
                    </Pressable>
                        </MotiView>
                    ))}
                        </View>
                    </YStack>
                </MotiView>

                </YStack> 

                </YStack>
            </ScrollView>

            {/* Enhanced Floating Cart */}
            {totalQty > 0 && (
                <MotiView
                    from={{ opacity: 0, translateY: 100 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    exit={{ opacity: 0, translateY: 100 }}
                    transition={{ type: 'spring', damping: 20, stiffness: 300 }}
                    style={{
                        position: 'absolute',
                        bottom: getResponsiveValue(20, 24, 32),
                        left: getResponsiveValue(16, 24, 32),
                        right: getResponsiveValue(16, 24, 32),
                        zIndex: 1000,
                        maxWidth: isDesktop ? 600 : '100%',
                        alignSelf: 'center'
                    }}
                >
                    <Card
                        p="$0"
                        br="$8"
                        bw="$0"
                        elevation="$8"
                        shadowColor="$shadowColor"
                        shadowOffset={{ width: 0, height: 12 }}
                        shadowOpacity={0.25}
                        shadowRadius={20}
                        overflow="hidden"
                    >
                        <LinearGradient
                            colors={['#667eea', '#764ba2']}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 1 }}
                            style={{ padding: 20 }}
                        >
                            <XStack jc="space-between" ai="center">
                                <XStack gap="$3" ai="center" flex={1}>
                                    <View style={{
                                        backgroundColor: 'rgba(255,255,255,0.2)',
                                        borderRadius: 12,
                                        padding: 8
                                    }}>
                                        <Ionicons name="cart" size={24} color="white" />
                                    </View>
                                    <YStack>
                                        <Text color="white" fontWeight="bold" fontSize="$5">
                                            {String(totalQty)} {t('suppliers.items', { defaultValue: 'items' })}
                                        </Text>
                                        <Text color="white" fontSize="$6" fontWeight="800">
                                            ₪{(totalPrice || 0).toFixed(2)}
                                        </Text>
                                    </YStack>
                                </XStack>
                                <Button
                                    bg={(currentSupplier as any).isActive === false ? "$gray5" : "white"}
                                    color={(currentSupplier as any).isActive === false ? "$gray9" : "$purple10"}
                                    fontWeight="700"
                                    fontSize="$4"
                                    br="$6"
                                    px="$5"
                                    py="$2"
                                    height="$5"
                                    minHeight={48}
                                    elevation="$4"
                                    shadowColor="rgba(0,0,0,0.3)"
                                    shadowOffset={{ width: 0, height: 4 }}
                                    shadowOpacity={0.3}
                                    shadowRadius={8}
                                    alignItems="center"
                                    justifyContent="center"
                                    disabled={(currentSupplier as any).isActive === false}
                                    opacity={(currentSupplier as any).isActive === false ? 0.6 : 1}
                                    hoverStyle={(currentSupplier as any).isActive === false ? {} : {
                                        scale: 1.05,
                                        shadowOpacity: 0.4
                                    }}
                                    pressStyle={(currentSupplier as any).isActive === false ? {} : {
                                        scale: 0.95,
                                        shadowOpacity: 0.2
                                    }}
                                    onPress={(currentSupplier as any).isActive === false ? undefined : () => setOpen(true)}
                                >
                                    <Text
                                        color={(currentSupplier as any).isActive === false ? "$gray9" : "$purple10"}
                                        fontWeight="700"
                                        fontSize="$4"
                                        textAlign="center"
                                        lineHeight="$4"
                                    >
                                        {(currentSupplier as any).isActive === false
                                            ? 'Store Closed'
                                            : t('suppliers.viewCart', { defaultValue: 'View Cart' })
                                        }
                                    </Text>
                                </Button>
                            </XStack>
                        </LinearGradient>
                    </Card>
                </MotiView>
            )}

            {/* Slide-up Sheet Cart */}
            <Sheet
                modal
                open={open}
                onOpenChange={setOpen}
                snapPoints={[100, 100]}
                dismissOnSnapToBottom
                >
                <Sheet.Frame f={1} p="$4" bg="white" pointerEvents="auto">
                    <CartPage
                        category={currentSupplier.category}
                        onClose={() => setOpen(false)}
                    />
                </Sheet.Frame>
                <Sheet.Overlay pointerEvents="auto" />
            </Sheet>
        </>
    );
};
