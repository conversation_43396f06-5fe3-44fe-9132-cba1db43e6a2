# Simple Link Supplier Script

Write-Host "Linking User Account to Existing Supplier..." -ForegroundColor Green

# Step 1: Login
Write-Host "Step 1: Logging in..." -ForegroundColor Yellow

$loginData = @{
    email = "<EMAIL>"
    password = "SfE#76132"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    $loginResult = $loginResponse.Content | ConvertFrom-Json
    
    if ($loginResult.success) {
        $token = $loginResult.data.accessToken
        Write-Host "Login successful!" -ForegroundColor Green
        Write-Host "User: $($loginResult.data.user.firstName) $($loginResult.data.user.lastName)" -ForegroundColor Cyan
        Write-Host "Current Supplier ID: $($loginResult.data.user.supplierId)" -ForegroundColor Cyan
        Write-Host "Current Store: $($loginResult.data.user.storeName)" -ForegroundColor Cyan
    } else {
        Write-Host "Login failed: $($loginResult.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Login error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Link to existing supplier
Write-Host "Step 2: Linking to existing 3a kefak supplier..." -ForegroundColor Yellow

$linkData = @{
    existingSupplierId = "3a-kefak"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $linkResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/suppliers/link-to-existing" -Method POST -Body $linkData -Headers $headers
    $linkResult = $linkResponse.Content | ConvertFrom-Json
    
    if ($linkResult.success) {
        Write-Host "Successfully linked to existing supplier!" -ForegroundColor Green
        Write-Host "New Supplier ID: $($linkResult.data.user.supplierId)" -ForegroundColor Cyan
        Write-Host "Store Name: $($linkResult.data.user.storeName)" -ForegroundColor Cyan
        Write-Host "Products Count: $($linkResult.data.supplier.productsCount)" -ForegroundColor Cyan
        Write-Host "Rating: $($linkResult.data.supplier.rating)" -ForegroundColor Cyan
    } else {
        Write-Host "Link failed: $($linkResult.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Link error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Verify products
Write-Host "Step 3: Verifying products..." -ForegroundColor Yellow

try {
    $productsResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/suppliers/3a-kefak/products" -Method GET
    $productsResult = $productsResponse.Content | ConvertFrom-Json
    
    if ($productsResult.success) {
        Write-Host "Verification successful!" -ForegroundColor Green
        Write-Host "Supplier: $($productsResult.data.supplier.name)" -ForegroundColor Cyan
        Write-Host "Products: $($productsResult.data.products.Count)" -ForegroundColor Cyan
        Write-Host "Rating: $($productsResult.data.supplier.rating)" -ForegroundColor Cyan
        
        if ($productsResult.data.products.Count -gt 0) {
            Write-Host "Sample products:" -ForegroundColor White
            $productsResult.data.products | Select-Object -First 3 | ForEach-Object {
                Write-Host "- $($_.name) - $($_.price) NIS" -ForegroundColor Gray
            }
        }
    }
} catch {
    Write-Host "Could not verify products: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "Process completed!" -ForegroundColor Green
Write-Host "SUMMARY:" -ForegroundColor White
Write-Host "Your account is now linked to 3a kefak supplier" -ForegroundColor Green
Write-Host "All products are accessible through your account" -ForegroundColor Green
Write-Host "You can login to mobile app and manage the store" -ForegroundColor Green
