// Test utilities for package functionality
export const testPackageData = {
  senderName: '<PERSON>',
  senderPhone: '+970568406041',
  recipientInfo: {
    name: '<PERSON>',
    phone: '+970599123456'
  },
  pickupAddress: {
    street: 'Main Street, Nablus',
    city: 'Nablus',
    coordinates: {
      lat: 32.2211,
      lng: 35.2544
    },
    notes: 'Near the university'
  },
  deliveryAddress: {
    street: 'City Center, Ramallah',
    city: 'Ramallah',
    coordinates: {
      lat: 31.9038,
      lng: 35.2034
    },
    notes: 'Main building entrance'
  },
  packageDetails: {
    type: 'documents',
    description: 'Important business documents',
    size: 'small' as const,
    weight: '500g',
    value: 100,
    fragile: false
  },
  priority: 'standard' as const,
  paymentMethod: 'cash' as const,
  notes: 'Please handle with care'
};

export const validatePackageData = (data: any): string[] => {
  const errors: string[] = [];
  
  if (!data.senderName?.trim()) errors.push('Sender name is required');
  if (!data.senderPhone?.trim()) errors.push('Sender phone is required');
  if (!data.recipientInfo?.name?.trim()) errors.push('Recipient name is required');
  if (!data.recipientInfo?.phone?.trim()) errors.push('Recipient phone is required');
  if (!data.pickupAddress?.street?.trim()) errors.push('Pickup address is required');
  if (!data.deliveryAddress?.street?.trim()) errors.push('Delivery address is required');
  if (!data.packageDetails?.type?.trim()) errors.push('Package type is required');
  if (!data.packageDetails?.description?.trim()) errors.push('Package description is required');
  if (!data.packageDetails?.weight?.trim()) errors.push('Package weight is required');
  
  return errors;
};
